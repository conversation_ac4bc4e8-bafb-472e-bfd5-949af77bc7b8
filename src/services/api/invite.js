/**
 *
 * @param {Axios} api
 * @returns
 */
export default function makeInviteService(api) {
  return {
    newInvite: function (data, projectKey) {
      const url = `/${data.handle}${projectKey ? `/projects/${projectKey}` : ''}/invite`;
      return api.post(url, data);
    },
    listInvites: function ({handle, status, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invites`;
      return api.get(url, { params: { status } });
    },
    updatePendingInvites: function ({handle, updates, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invites`;
      return api.patch(url, { updates });
    },
    deleteInviteByEmail: function ({handle, email, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invite`;
      return api.delete(url, { data: { email } });
    },
    resendInvite: function ({handle, emails, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invite/resend`;
      return api.post(url, { emails });
    },
  }
}