export default function makeProjectsService(api) {
  return {
    getAuthz: async function (handle, key) {
      return api.get(`/orgs/${handle}/projects/${key}/authz`);
    },
    getProjects: async function (handle, params) {
      return api.get(`/${handle}/projects?${params}`);
    },
    getProject: async function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}`);
    },
    createProject: async function (handle, { ...data }) {
      return api.post(`/${handle}/projects`, data);
    },
    deleteProject: async function (handle, item) {
      return api.delete(`/${handle}/projects/${item}`);
    },
    updateProject: async function (handle, item, payload) {
      return api.patch(`/${handle}/projects/${item}`, payload);
    },
    validateProjectKey: async function (handle, projectKey) {
      return api.get(`/${handle}/projects/keys/${projectKey}`);
    },
    getSignedAttachmentUrl: function({params,payload}){
      return api.post(`/${params.handle}/projects/${params.projectKey}/attachments`, payload)
    },
    cleanupAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/projects/attachments/${id}/failed`)
    },
    deleteAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/attachments/${id}`)
    },
    getUsers: function ({handle ,projectKey, payload}) {
      return api.get(`/${handle}/projects/${projectKey}/users`, { params: payload });
    },
    getProjectMembers: async function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}/users`);
    },
    getEntityCount: async function (handle, projectKey, entityType) {
      return api.get(`/${handle}/projects/${projectKey}/entities/count?entityType=${entityType}`);
    },
  };
}
