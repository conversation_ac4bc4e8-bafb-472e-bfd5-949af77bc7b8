import DefaultLayout from '@/Layouts/DefaultLayout.vue';

const routes = [
  {
    path: '/:handle/projects',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'ProjectsView',
        component: () => import('@/views/Projects/index'),
      },
      {
        path: 'create',
        name: 'ProjectCreateView',
        component: () => import('@/views/Projects/ProjectCreateView'),
        meta: {
          requiredAuthz: ['write_project'],
        },
      },
      {
        path: ':key/edit',
        name: 'ProjectDetailView',
        component: () => import('@/views/Projects/ProjectDetailView'),
        meta: {
          requiredAuthz: ['write_project'],
          authzScope: 'project',
        },
      },
    ],
  },
];

export default routes.map(route => {
  const meta = {
    authzScope: 'org',
    requiresAuth: true,
    ...route.meta,
  }
  return { ...route, meta };
});
