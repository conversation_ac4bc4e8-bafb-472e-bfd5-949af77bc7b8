import ProjectLayout from '@/Layouts/ProjectLayout.vue';
import DefaultLayout from '@/Layouts/DefaultLayout.vue';

const routes = [
  {
    path: '/:handle/:key/runs',
    component: ProjectLayout,
    meta: {
      requiredAuthz: ['read_entity'],
    },
    children: [
      {
        path: '',
        name: 'Runs',
        component: () => import('@/views/Tests/Runs/Index'),
        props: true,
      },
    ],
  },
  {
    path: '/:handle/:key/runs/create',
    component: DefaultLayout,
    meta: {
      requiredAuthz: ['write_entity'],
    },
    children: [
      {
        path: '',
        name: 'TestRunCreate',
        component: () => import('@/views/Tests/Runs/Create/TestRunCreateView'),
      },
      {
        path: 'cases',
        name: 'RunAddCase',
        component: () => import('@/views/Tests/Runs/Create/RunAddCaseView'),
        props: true,
      },
      {
        path: 'duplicate',
        name: 'TestRunDuplicate',  
      component: () => import('@/views/Tests/Runs/Duplicate/Index'),
      }
    ]
  },
  {
    path: '/:handle/:key/runs/:id',
    component: ProjectLayout,
    children: [
      {
        path: 'folders/:folderUid/executions/:executionUid',
        name: 'TestRunCaseEditExecutions',
        component: () => import('@/views/Tests/Runs/RunCaseEditView'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: 'folders/:folderUid?',
        name: 'TestRunCaseEdit',
        component: () => import('@/views/Tests/Runs/RunCaseEditView'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: 'edit/:folderUid?',
        name: 'TestRunEdit',
        component: () => import('@/views/Tests/Runs/RunEditView'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: '',
        name: 'TestRunCase',
        component: () => import('@/views/Tests/Runs/RunCaseEditView'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
    ]
  },
];

export default routes.map((route) => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'project',
    requiredAuthz: ['read_entity'],
  }
  return { ...route, meta };
});
