.v-btn {
  letter-spacing: 0 !important;
}

.b-avatar .b-avatar-badge {
  font-size: 3rem !important;
  cursor: pointer;
}

.v-input {
  font-size: 14px !important;
}
.bg-white {
  background-color: #ffffff !important;
}
.theme--light.v-text-field--filled > .v-input__control > .v-input__slot {
  background-color: #f9f9fb !important;
}
.v-skeleton-loader__bone {
  height: 100% !important;
  width: 100% !important;
}
.mh-32.v-autocomplete .v-select__selections input {
  max-height: 32px !important;
}
.bg-none {
  background-color: none !important;
}
.h-500px {
  height: 500px;
}
.gap-0 {
  gap: 0px !important;
}
.gap-1 {
  gap: 4px !important;
}
.gap-2 {
  gap: 8px !important;
}
.gap-3 {
  gap: 16px !important;
}
.gap-4 {
  gap: 24px !important;
}
.gap-5 {
  gap: 32px !important;
}
.gap-6 {
  gap: 40px !important;
}
.gap-7 {
  gap: 48px !important;
}
.gap-8 {
  gap: 56px !important;
}
.gap-9 {
  gap: 64px !important;
}
.gap-10 {
  gap: 72px !important;
}
.v-skeleton-loader.primary .v-skeleton-loader__bone {
  background: #8cafff !important;
}
.v-skeleton-loader.danger .v-skeleton-loader__bone {
  background: #f98ca1 !important;
}
.panel-content-theme .v-expansion-panel-content__wrap {
  padding-left: 0px;
  padding-right: 0px;
}
.v-skeleton-loader.chip-primary .v-skeleton-loader__bone:first-child {
  background: #e6ecff !important;
}
.v-skeleton-loader .v-skeleton-loader__bone {
  background: #eff2f4 !important;
}
.v-skeleton-loader .v-skeleton-loader__bone:after {
  background: linear-gradient(90deg, hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.6), hsla(0, 0%, 100%, 0)) !important;
}
.v-skeleton-loader.circle {
  border-radius: 100px !important;
}
.pdf-preview .vue-pdf-embed__page canvas {
  height: 100% !important;
  max-height: 80px !important;
  border: solid 1px #d0d5dd;
  object-fit: cover;
}

.w-5 {
  width: 5%;
}
.w-10 {
  width: 10%;
}
.w-20 {
  width: 20%;
}
.w-40 {
  width: 40%;
}
.w-35 {
  width: 35%;
}
.w-50 {
  width: 50%;
}
.w-65 {
  width: 65%;
}
.w-45 {
  width: 45%;
}
.w-15 {
  width: 15%;
}
.w-25 {
  width: 25%;
}
.w-75 {
  width: 75%;
}
.w-90 {
  width: 90%;
}
* {
  font-family: 'Inter', sans-serif !important;
}
.font-inter {
  font-family: 'Inter', sans-serif !important;
}
.w-full {
  width: 100% !important;
}
.no-border {
  border: 0px !important;
  outline: none !important;
}
.active {
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
}
.d-none {
  display: none !important;
}
.text-decoration-none {
  text-decoration: none !important;
}
.no-wrap-panel-content .v-expansion-panel-content__wrap {
  padding: 0px !important;
}
.scrollable {
  max-height: max-content;
  overflow-y: scroll;
}
.disabled-action {
  opacity: 0.5;
  pointer-events: none;
}
.opacity-md {
  opacity: 0.5;
}
.opacity-1 {
  opacity: 1 !important;
}
@media screen and (max-width: 576px) {
  .hide-leftpanel {
    display: none;
  }
  .navsearch-box-width {
    width: 200px !important;
  }
}
@media screen and (min-width: 576px) {
  .show-only-on-mobile {
    display: none;
  }
  .navsearch-box-width {
    width: 300px !important;
  }
}

@media screen and (min-width: 577px) {
  .bm-burger-button {
    display: none;
  }
}
.point-events-none:hover {
  pointer-events: none !important;
}

.b-avatar .b-avatar-badge {
  font-size: 3rem !important;
  cursor: pointer;
}
thead {
  background-color: #f9fafb !important;
}

.text-align-left {
  text-align: left;
}
.box-shadow-none {
  box-shadow: none;
}

th {
  color: #6b7280;
}
.page-item.active .page-link {
  z-index: 3;
  color: #4f46e5 !important;
  background-color: #eef2ff !important;
  border-color: #1877f2 !important;
}
.page-item span {
  width: 38px !important;
  height: 38px !important;
}
.table th,
.table td {
  padding: 0.75rem;
  vertical-align: middle !important;
}
.dropdown-toggle svg {
  padding: 0 !important;
}

.swal2-actions {
  justify-content: right !important;
}

.v-text-field > .v-input__control > .v-input__slot:before {
  border-width: 0 !important;
}
.font-weight-extrabold {
  font-weight: 800 !important;
}
.flex-none {
  flex: none;
}
.mt-auto {
  margin-top: auto;
}
.fs-12px {
  font-size: 12px;
}
.fs-14px {
  font-size: 14px !important;
}
.fs-16px {
  font-size: 16px;
}
.fs-18px {
  font-size: 18px;
}
.fs-20px {
  font-size: 20px !important;
}
.fs-24px {
  font-size: 24px !important;
}
.fs-28px {
  font-size: 28px;
}
.fs-32px {
  font-size: 32px;
}
.menu-shadow-custom {
  box-shadow: 0px 16px 40px 0px #0000000f !important;
}
.actions-list .actions-item {
  cursor: pointer;
}
.actions-list .actions-item:hover {
  background: #eaecf0;
}
.actions-list .actions-item .actions-item-title {
  font-size: 16px;
}
.v-expansion-panel-header::before {
  background-color: initial !important;
}
.v-menu__content {
  max-width: 25vw !important;
}

//================== Custom CSS ==================
.bg-theme-base {
  background-color: #f2f4f7 !important;
}
.fs-24px {
  font-size: 24px;
}
.fw-semibold,
.v-application .fw-semibold {
  font-weight: 600 !important;
}
.fw-normal {
  font-weight: 500;
}
.text-theme-label {
  color: #0c111d !important;
}
.text-theme-secondary {
  color: #667085 !important;
}
.field-theme {
  border-radius: 8px;
}
.v-input.v-input--is-disabled input {
  color: #A8AEB9 !important;
}
.field-theme .v-text-field__slot {
  padding: 0.5rem 1rem;
  border-radius: 8px;
}
.field-theme.custom-prepend .v-text-field__slot {
  padding: 0.25rem !important;
  border-radius: 8px;
}
.field-theme.custom-append .v-input__append-inner {
  margin-top: 4px !important;
}
.btn-theme.v-btn {
  text-transform: inherit;
}
.field-theme .v-input__append-inner,
.field-theme .v-input__prepend-inner {
  margin-right: 8px;
  margin-left: 8px;
}
.field-theme .v-input__prepend-inner {
  margin-top: 8px !important;
}

.field-theme .v-select__slot .v-input__append-inner {
  align-self: center;
  justify-self: center;
}

.icon-checkbox-on {
  background-image: url('@/assets/svg/checkbox-on.svg');
}
.icon-checkbox-off {
  background-image: url('@/assets/svg/checkbox-off.svg');
}
.icon-indeterminate {
  background-image: url('@/assets/svg/indeterminate.svg');
}
.field-theme .v-input--selection-controls__input {
  height: 1rem;
  margin-top: 1.75px;
}
.btn-theme.btn-loading-opacity {
  opacity: 0.4;
}
.btn-theme.v-btn {
  border-radius: 6px !important;
}
.bg-gray-theme {
  background: #f9fafb !important;
}
.text-theme-base {
  color: #101828 !important;
}
.treeview-theme .v-treeview-node--active::before {
  opacity: 0 !important;
}
.treeview-theme .v-treeview-node--active {
  background: #f2f4f7 !important;
}
.treeview-theme .v-treeview-node.v-treeview-node--rounded .v-treeview-node__root {
  border-radius: 8px !important;
}
.treeview-theme .v-icon:focus::after,
.treeview-theme .v-treeview-node--click > .v-treeview-node__root:hover::before {
  opacity: 0 !important;
}
.treeview-theme .v-treeview-node__children {
  border-left: 2px solid #f2f4f7;
}
.text-theme-primary {
  color: #0c2ff3 !important;
}
.cursor-pointer {
  cursor: pointer;
}
.curser-resize {
  cursor: ew-resize;
}
.shadow-theme {
  box-shadow: 0px 16px 40px 0px #0000000f !important;
}
.v-menu__content {
  box-shadow: 0px 16px 40px 0px #0000000f !important;
  border-radius: 0.5rem;
}
.text-theme-disabled {
  color: #d0d5dd !important;
}
.dialog-theme .v-dialog__content.v-dialog__content--active {
  background: #18181a66;
}
.dialog-theme .v-sheet.v-card {
  border-radius: 0px !important;
}
.dialog-theme .v-dialog {
  box-shadow: 0px 16px 40px 0px #0000000f !important;
  border-top-left-radius: 1rem !important;
  border-bottom-left-radius: 1rem !important;
}
.field-theme .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.field-theme .v-input--selection-controls {
  margin-top: 8px !important;
}
.table-fixed table {
  table-layout: fixed;
}

/* Table horizontal scroll wrapper */
.table-scroll-container {
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #d0d5dd #f9fafb;
  height: 70vh;
}

/* Table container without horizontal scroll */
.table-no-scroll-container {
  overflow-x: hidden;
  overflow-y: visible;
  height: 70vh;
}

/* Webkit scrollbar styling */
.table-scroll-container::-webkit-scrollbar {
  height: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: #d0d5dd;
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #98a2b3;
}

/* Minimum width for tables to preserve readability */
.table-min-width {
  min-width: 1480px;
}

/* For smaller tables that don't need as much space */
.table-min-width-sm {
  min-width: 800px;
}

/* For larger tables with many columns */
.table-min-width-lg {
  min-width: 1600px;
}

.break-all {
  word-break: break-all;
}
.checkbox-align-start .v-input__slot {
  align-items: flex-start !important;
}
.v-data-table .v-data-footer {
  border-top: none !important;
}
.data-table-style .v-data-table-header {
  background-color: #f2f4f7 !important;
}
.data-table-style .v-data-table-header th:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.data-table-style .v-data-table-header th:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

/* Sticky Table Headers - Reusable across components */
.v-data-table--fixed-header thead th {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background-color: #f2f4f7 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure data-table-style headers maintain their background */
.data-table-style.v-data-table--fixed-header .v-data-table-header th {
  background-color: #f2f4f7 !important;
}
.v-data-table.data-table-style tbody tr.v-data-table__selected {
  background-color: transparent !important;
}
.v-data-table.data-table-style tbody tr:hover,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: #eaecf0 !important;
}
.v-data-table.data-table-style tbody tr.active-row {
  background-color: #eaecf0 !important;
}
.v-data-table.data-table-style tbody tr.active-row td:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}
.v-data-table.data-table-style tbody tr:hover td:first-child,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) td:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.v-data-table.data-table-style tbody tr:hover td:last-child,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) td:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.v-data-table.data-table-style tbody tr.active-row td:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.v-data-table.data-table-style tbody tr:hover .icon-checkbox-off,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) .icon-checkbox-off {
  background-image: url('@/assets/svg/checkbox-white.svg');
}

.v-data-table.data-table-style tbody tr.active-row .icon-checkbox-off {
  background-image: url('@/assets/svg/checkbox-white.svg');
}
.v-data-table.data-table-style tbody tr:hover .progress-linear-bar {
  background-color: #fff !important;
}
.progress-bar-menu .v-list-item--dense,
.progress-bar-menu .v-list-item {
  min-height: 28px !important;
}
.text-theme-table-text {
  color: #344054;
}
.text-theme-danger {
  color: #f2284e !important;
}
.text-theme-dark {
  color: #18181A !important;
}
.step-label-corner {
  position: absolute;
  top: 0;
  left: 2rem;
  font-size: 14px;
  color: #667085;
}
.field-theme .v-select__selections input {
  padding-left: 12px !important;
}
.field-theme .v-select__selection--comma {
  margin: 7px 4px 7px 12px !important;
}
.field-theme-file {
  border: dotted 2px #d0d5dd;
}
.field-theme-file .v-input__prepend-outer {
  display: none;
}
.field-theme-file .v-file-input__text {
  justify-content: center;
  color: #0c2ff3 !important;
  font-weight: 600;
  cursor: pointer;
}
.field-theme-file .v-text-field__slot {
  height: 124px;
}
.test-cases-filter-drawer .v-sheet {
  display: flex;
}

.test-cases-filter-drawer .v-card__text {
  display: flex;
  flex-direction: column;
}

.actions-container {
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  z-index: 999;
  border-top: solid 1px #f2f4f7;
}
.mh-38px .v-input__slot {
  min-height: 38px;
}
.minw-32px {
  min-width: 32px;
}
.mh-56px {
  min-height: 56px;
}
.h-full {
  height: 100% !important;
}
.timeline-theme .v-timeline-item__dot {
  height: 12px !important;
  box-shadow: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
}
.timeline-theme .v-timeline-item__body {
  background-color: #fff;
  padding: 0px;
  border-radius: 8px;
}
.timeline-theme .v-timeline-item__inner-dot {
  height: 12px !important;
  width: 12px !important;
}
.timeline-theme:before {
  background: #f2f4f7 !important;
}
.text-truncate {
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}
.sticky-div {
  position: sticky;
  top: 0;
}
.sticky-div-bottom {
  position: sticky;
  bottom: 0;
}
.overflow-auto {
  overflow: auto;
}
.h-100vh {
  height: 100vh !important;
}
.v-timeline-item__divider {
  min-width: auto !important;
  margin: 0px -8px;
}
.v-timeline--dense:not(.v-timeline--reverse):before,
.v-application--is-rtl .v-timeline--reverse.v-timeline--dense:before {
  left: 10px !important;
}
.v-timeline--dense .v-timeline-item {
  justify-content: flex-end;
  gap: 8px;
}
.timeline-item h4 {
  color: #0c111d;
  font-size: 14px;
}
.v-timeline--dense .v-timeline-item__body {
  max-width: max-content;
  min-width: calc(100% - 24px) !important;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}
.v-timeline--dense .v-timeline-item__body button {
  padding: 0px !important;
}
.slider-theme .v-slider__track-container {
  height: 8px !important;
}
.slider-theme .v-slider__thumb {
  height: 16px !important;
  width: 16px !important;
  border: solid 2px #0c2ff3 !important;
}
.v-list-item__content {
  text-align: start;
}
.bg-theme-primary-light {
  background-color: #e6ecff !important;
}
.field-theme.combo-box .v-select__slot input {
  padding-left: 14px !important;
  padding-bottom: 4px !important;
}
.v-list-item__title {
  font-size: 1rem !important;
}

.full-height-custom {
  height: calc(100vh - 32px);
}
.w-full {
  width: 100%;
}
.btn-full .v-btn__content {
  width: 100%;
}
.flex-inherit {
  flex: inherit !important;
}
.select-assignee .v-select__slot {
  width: 108px !important;
}
.select-assignee.status .v-select__slot {
  width: 128px !important;
}
.select-assignee .v-input__append-inner {
  padding-left: 0px !important;
}
.select-assignee .v-select__selections input {
  font-size: 14px;
}
.select-assignee .v-select__selections input::placeholder,
.select-placeholder-theme .v-select__selections input::placeholder {
  color: #344054 !important;
  font-size: 14px !important;
}
.mh-36px {
  min-height: 36px !important;
}
.app-height-global {
  min-height: calc(100vh - 24px);
  height: 100%;
}
.timeline-history .v-timeline-item__dot .v-timeline-item__inner-dot.grey.lighten-2 {
  background-color: #f2f4f7 !important;
}
.timeline-history .v-timeline-item__dot {
  box-shadow: none;
}
.timeline-history .v-timeline-item__dot .v-timeline-item__inner-dot {
  height: 32px !important;
  width: 32px !important;
}
.v-timeline.timeline-history::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: repeating-linear-gradient(180deg, transparent, transparent 5px, #eaecf0 5px, #eaecf0 10px) !important;
  content: '';
}
.timeline-history .v-timeline-item__divider {
  margin-right: 0.5rem;
}
.timeline-history .v-timeline-item__body {
  min-width: calc(100% - 46px) !important;
}
.timeline-history .v-timeline-item {
  padding-bottom: 12px;
}
.active-menu-item.v-list-item--link:before {
  background-color: #667085 !important;
  border-radius: 8px !important;
}
.active-menu-item .v-list-item__title span {
  color: #0c2ff3 !important;
}
.is-collapsed-menu .field-theme,
.is-collapsed-menu .group {
  display: none;
}
.is-collapsed-menu .v-list--nav .v-list-item {
  padding: 0px 6px !important;
}
.is-collapsed-menu {
  display: flex !important;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.is-collapsed-menu .collapse-btn {
  position: sticky !important;
  position: -webkit-sticky !important;
  padding-left: 0px !important;
}
.no-expansion-header-icon .v-expansion-panel-header__icon {
  display: none;
}
.invisible {
  visibility: hidden;
}
.visible {
  visibility: visible;
}
.chip-theme {
  background: #f9fafb !important;
  border: solid 1px rgba(0, 0, 0, 0.12);
}
.v-btn--plain.btn-plain-theme:not(.v-btn--active):not(.v-btn--loading):not(:focus):not(:hover) .v-btn__content {
  opacity: 1 !important;
}
.flex-flow-wrap {
  flex-flow: wrap;
}
.active-menu-item .v-list-item__icon.stroke svg path,
.active-menu-item .v-list-item__content.stroke svg path {
  stroke: #0c2ff3 !important;
}
.active-menu-item .v-list-item__icon.fill svg path,
.active-menu-item .v-list-item__content.fill svg path {
  fill: #0c2ff3 !important;
}
.no-table-label span {
  visibility: hidden;
}
.position-relative {
  position: relative;
}
.position-absolute {
  position: absolute;
}
.position-sticky {
  position: sticky;
}
.top-0 {
  top: 0;
}
.bottom-0 {
  bottom: 0;
}
.left-0 {
  left: 0;
}
.right-0 {
  right: 0;
}
.tooltip-theme {
  border-radius: 8px !important;
  background: #e0e0e0 !important;
  color: rgb(12, 17, 29) !important;
  word-break: break-all;
}

.v-select__selections .v-chip:not(.v-chip--active) {
  background: white !important;
  border: solid 2px #d0d5dd !important;
  border-radius: 8px !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.v-select__selections .v-chip--select.v-chip--active {
  border-radius: 12px !important;
  border: solid 2px #d0d5dd !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.mdi-close-circle::before {
  content: '\F0156' !important;
  color: grey !important;
  font-size: 15px !important;
  margin-right: 4px !important;
}

.custom-chip-theme {
  border: solid 2px #d0d5dd;
  border-radius: 8px;
  background: #fff;
  padding: 2px 4px 2px 8px;
  max-width: 100%;
}
.w-33 {
  width: 33.33333%;
}
.w-25 {
  width: 25%;
}
.divider-theme {
  border-color: #f2f4f7 !important;
}
.divider-theme-width-2px {
  border-width: 2px !important;
}
.collapse-btn {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: blue;
  position: fixed;
  bottom: 2rem;
  padding-left: 1rem;
}

.collapse-btn .collapse-text {
  margin-left: 8px;
  font-weight: bold;
}
.right-0 {
  right: 0;
}
.bottom-0 {
  bottom: 0;
}
.camera-placeholder {
  background: #f2f4f7;
  border: solid 1px #fff;
  padding: 2px 4px;
  border-radius: 100%;
}

.edit-title-input .v-input__control .v-input__slot .v-text-field__slot input {
  font-size: 24px;
  font-weight: 700;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.due-input .v-input__control .v-input__slot .v-text-field__slot input {
  padding-top: 0px !important;
}
.edit-subtitle-input .v-input__control .v-input__slot .v-text-field__slot input {
  font-size: 14px;
  font-weight: 400;
  color: #667085 !important;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.mw-108px,
.mw-108px .v-select__slot {
  max-width: 108px;
}
.mw-leftmenu {
  min-width: 189px;
}
.select-input-none.v-select input {
  display: none;
}
.field-theme .v-label {
  width: 100%;
}
.btn-nav-back {
  width: max-content;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #0c2ff3 !important;
  text-transform: none;
  opacity: 1;
  display: flex;
  align-items: center;
}
.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}
.v-text-field.v-input--is-focused > .v-input__control > .v-input__slot:after {
  display: none;
}
.project-placeholder-height {
  min-height: calc(100vh - 25vh);
  height: 100%;
}
.v-btn.btn-no-hover:hover:before,
.v-btn.btn-no-hover:focus:before {
  background: none !important;
}
.list-theme .v-list-item:hover {
  background: transparent !important;
}
.action-column .v-icon {
  display: none !important;
}
.action-column {
  pointer-events: none;
}
.user-select-none {
  user-select: none;
}
.data-table-style .active {
  box-shadow: none !important;
}
.custom-carousel {
  display: flex;
  overflow: hidden;
  padding: 0;
  position: relative;
}
.custom-carousel .v-slide-group__prev,
.custom-carousel .v-slide-group__next {
  position: absolute;
  height: 100%;
  z-index: 999;
}
.custom-carousel .v-slide-group__prev {
  left: 0;
}
.custom-carousel .v-slide-group__next {
  right: 0;
}
.custom-carousel .v-slide-group__prev:not(.v-slide-group__prev--disabled) {
  background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0.28%, rgba(0, 0, 0, 0.15) 100%);
}
.custom-carousel .v-slide-group__next:not(.v-slide-group__next--disabled) {
  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0.28%, rgba(0, 0, 0, 0.15) 100%);
}
.custom-carousel .v-slide-group__content .v-card {
  width: 100%;
  max-width: 120px;
  height: 82px;
  margin-right: 0.5rem;
  flex: 0 0 auto;
  border-radius: 4px;
}
.custom-carousel .v-slide-group__prev .v-icon,
.custom-carousel .v-slide-group__next .v-icon {
  background: #ffffff;
  border-radius: 100%;
  color: #0c111d;
}

.preview-container {
  position: relative;
  width: 100%;
  max-width: 120px;
  height: 80px;
}

.preview-image {
  width: 100%;
  height: 100%;
  max-width: 120px;
  max-height: 80px;
  min-height: 80px;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 4px;
  border: solid 1px #d0d5dd;
}

.non-image-preview {
  width: 100%;
  height: 100%;
  max-width: 120px;
  max-height: 80px;
  min-height: 80px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: solid 1px #d0d5dd;
}

.file-placeholder {
  color: #6c757d;
  font-size: 12px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.delete-btn {
  position: absolute !important;
  top: 0.5rem;
  right: 0.5rem;
  background: #fff;
  cursor: pointer !important;
  display: none !important;
  z-index: 1;
}

.download-btn {
  position: absolute !important;
  top: 0.5rem;
  right: 2.75rem;
  background: #fff;
  cursor: pointer !important;
  display: none !important;
  z-index: 1;
}

.link-btn {
  position: absolute !important;
  top: 0.5rem;
  right: 5rem;
  background: #fff;
  cursor: pointer !important;
  display: none !important;
  z-index: 1;
}

.preview-container:hover .delete-btn,
.preview-container:hover .download-btn,
.preview-container:hover .link-btn {
  display: block !important;
}
.preview-container:hover .preview-image {
  filter: grayscale(100%);
}
.preview-container:hover .non-image-preview {
  background-color: #e0e0e0 !important;
}
.file-input-bg {
  background: #f9fafb;
}
.file-input-bg .v-input__prepend-inner {
  position: absolute;
  color: #0c111d;
  top: 20px;
  font-family: 'Inter', sans-serif;
  font-size: 14px !important;
}
.tiptap-theme .v-card {
  border: none !important;
}
.tiptap-theme .tiptap.ProseMirror {
  background: #f9f9fb;
  min-height: 80px;
}
.tiptap-theme .tiptap-footer {
  background: #f9f9fb;
  padding: 8px 12px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
  align-items: center;
}
.tiptap-theme header.v-toolbar {
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}
.tiptap-theme header.v-toolbar.grey.lighten-4 {
  background-color: #f2f4f7 !important;
}
.lh-normal {
  line-height: normal;
}
.vertical-align-super {
  vertical-align: super;
}
.text-status-new {
  color: #42a5f5;
}
.text-status-passed {
  color: #66bb6a;
}
.custom__tooltip__title {
  max-width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.swal-theme .custom-svg-icon {
  margin-right: 0.5rem !important;
}
.swal-theme.custom-content {
  justify-content: flex-start !important;
}
.swal2-popup.swal2-toast .swal2-html-container {
  margin: 0.5em !important;
}
.swal2-toast.custom-success-popup,
.swal2-toast.custom-error-popup {
  min-height: 58px;
  border-radius: 12px;
  padding: 0px;
  box-shadow: 0 0px 16px hsl(0deg 0% 0% / 0.095) !important;
}
.swal-theme .custom-message {
  color: #0c111d !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}
.swal2-timer-progress-bar {
  background: #0c2ff3;
}
.v-application--is-ltr .custom-switch.v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb {
  transform: translate(10px) !important;
}
.v-application--is-ltr .custom-switch.v-input--switch--inset .v-input--switch__thumb {
  transform: translate(-2px) !important;
}
.custom-switch .v-input--switch--inset .v-input--switch__track {
  top: calc(50% - 10px) !important;
}
.custom-switch.v-input--switch--inset.v-input--is-dirty .v-input--switch__track {
  background: #0c2ff3 !important;
}
.custom-switch.v-input--switch--inset .v-input--switch__track {
  background: #f2f4f7 !important;
}
.custom-switch {
  .v-input--switch__track {
    border-radius: 1rem !important;
    height: 20px !important;
    left: -4px !important;
    width: 32px !important;
    opacity: 1 !important;
    top: calc(50% - 10px) !important;
  }
  .v-input--switch__thumb {
    top: calc(50% - 8.55px) !important;
    height: 17.42px !important;
    width: 16.94px !important;
    background: #fff !important;
    box-shadow: 0px 3px 8px 0px #00000020 !important;
    right: 0 !important;
  }
  .v-input--switch__track {
    background: #d0d5dd !important;
  }
  .v-input--selection-controls__input {
    width: 32px !important;
  }
}
.toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.toggle-container {
  display: flex;
  background: #f9fafb;
  border-radius: 8px;
  padding: 4px;
  position: relative;
  width: 100%;
  max-width: 421px;
}

.toggle-option {
  border-radius: 8px;
  color: #667085; 
  transition: background 0.3s;
  font-weight: 600;
  font-size: 14px;
  padding: 8px 0;
  width: 50%;
  cursor: pointer;
  text-align: center;
}

.toggle-option.active {
  font-weight: 600;
  background: #fff;
  color: #0a26c3;
  transition: background 0.3s;
  border-radius: 8px;
  box-shadow: 0px 16px 40px 0px #0000000f !important;
}
/* General placeholder color for all inputs */
input::placeholder,
textarea::placeholder,
select::placeholder {
  color: #667085 !important;
  opacity: 1 !important;
}

/* Specific styles for input types if needed */
input[type='text']::placeholder,
input[type='email']::placeholder,
input[type='password']::placeholder {
  color: #667085 !important;
}

/* Placeholder styling inside disabled inputs */
input:disabled::placeholder,
textarea:disabled::placeholder {
  color: #667085 !important;
}

/* Cross-browser compatibility */

/* Chrome, Safari, Edge */
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
select::-webkit-input-placeholder {
  color: #667085 !important;
}

/* Firefox */
input::placeholder,
textarea::placeholder,
select::placeholder {
  color: #667085 !important;
}

/* Internet Explorer */
input:-ms-input-placeholder,
textarea:-ms-input-placeholder,
select:-ms-input-placeholder {
  color: #667085 !important;
}

/* Older Edge */
input::-ms-input-placeholder,
textarea::-ms-input-placeholder,
select::-ms-input-placeholder {
  color: #667085 !important;
}

/* Global Tooltip Styles */
.v-tooltip__content {
  background: white !important;
  color: #344054 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1) !important;
  margin-top: 4px !important;
  font-size: 14px !important;
}

.v-tooltip__content:before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background-color: white;
  transform-origin: center;
  transform: translateX(-50%) rotate(45deg);
  box-shadow: -1px -1px 1px rgba(0, 0, 0, 0.05);
}

.tooltip-content {
  position: relative;
  z-index: 1;
}

.v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
  background-color: #ffffff;
}

.v-data-table-header__icon {
  opacity: 1 !important;
}

.v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.pointer {
  cursor: pointer;
}

.header_text {
  color: #475467;
  font-weight: 700;
}

.custom_color {
  color: #667085;
}

.custom_border {
  border: 2px solid #ffffff;
}

.v-data-table table {
  border-collapse: collapse;
}

.v-data-table th {
  border: none !important;
}

.v-data-table td {
  border: none !important;
}

.v-data-table .v-data-table__wrapper tbody tr {
  height: 80px;
}

.v-data-table .v-data-table__wrapper tbody tr td {
  height: 80px !important;
  padding-top: 0;
}

.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}
.img-custom-border {
  border: solid 2px #fff;
}
.img-rest-amount {
  background: #eaecf0;
}
.avatar-group {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-group v-avatar:first-child {
  margin-left: 0;
}
