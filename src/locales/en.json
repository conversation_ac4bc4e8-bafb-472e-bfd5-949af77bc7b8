{"configurations": {"title": "Configurations", "add_configuration": "Add configuration", "edit_configuration": "Edit configuration", "emptyTitle": "Ready to create a configuration group?", "emptyDescription": "Your configurations page is empty — let's update it! Configurations will be grouped by categories like OS or Web Browsers and applied to test runs within test plans., ensuring thorough compatibility.", "deleteConfirm": "Are you sure you want to delete \"{name}\" configuration group?", "deleteDescription": "Are you sure you want to delete this configuration group and {count} configurations in it?", "options": "Options", "enter_token": "Enter new token"}, "integrations": {"disabledMessage": {"prefixUser": "This feature isn't available for individual accounts, create an ", "prefix": "Individual accounts do not have integrations, create an ", "action": "Organization", "suffix": " to access this feature."}, "name": "Integrations", "syncedAt": "Synced At", "noPermissionToDo": "You don't have permission to {action} this integration", "no_integrations": "No data available", "please_reauth": "Please reauthenticate!", "apps": "Apps", "resolveAll": "Resolve all", "projects": "Projects", "enter_token": "Enter new token", "personal_token_title": "Personal Access Token", "personal_token_confirmation_message": "This token will be used with {integrationName} integration to access your account and perform actions on your behalf. Are you sure you want to continue?", "personal_token_note_message": "Please make sure the account has access to following repos else it can cause problems with defect creation", "create_intergrations": {"name": "Name", "description": "Description", "organisanion_user_url": "Organisanion/user url", "access_token": "Access token", "this_field_is_required": "This field is required", "integration_name": "{serviceName}", "integration_description": "{serviceName}", "configuration_name": "Configuration Name", "back_to_integrations": "Back to Integrations", "back_to_app_integrations": "Back to {serviceName} Details", "back_to_app": "Back to Apps", "configurations": "Configurations", "select_configuration": "Select projects for your configuration", "add_configuration": "Add Configuration", "configuration_limit": "Maximum 3 configurations allowed", "testfiesta_projects": "Testfiesta Projects", "service_entity": "{serviceName} {entityName}", "service_projects": "{serviceName} {entityName}", "select_testfiesta_projects": "Select Testfiesta projects", "select_service_entity": "Select {serviceName} {entityName}", "enter_manually": "Or enter details ", "loginWithService": "Login with {serviceName}", "save_configuration": "Save Configuration", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "username_or_email": "Username/email", "enter_integration_details": "Enter Integration Details", "upload_thumbnail_description": "Upload a thumbnail, then enter the name and details", "custom_integration_title": "Create Custom Integration", "custom_integration_description": "Create a custom integration to connect with external systems", "add_url": "Add URL", "add_url_placeholder": "Enter URL for adding defects", "view_url": "View URL", "view_url_placeholder": "Enter URL for viewing defects", "invalid_url": "Please enter a valid URL"}, "integration_card": {"name": "Unnamed Integration", "description": "No description available"}, "button_library": {"text": "Go to Library"}, "empty_state": {"no_added_apps": "You don't have any integrated applications yet", "no_available_apps": "You have no applications to integrate yet"}, "button_custom_app": {"text": "Add custom app"}, "header": {"add_app": "Add app", "active": "Active", "available": "Available"}, "success": {"oauth_success": "OAuth Integration Success", "orgs": "{serviceName} Orgs", "select_org": "Please select an {serviceName} org to continue", "search_project": "Search for project", "testfiesta_projects": "Testfiesta projects", "select_projects": "Select projects for integration", "saved": "Integration saved successfully", "reauth_success": "Reauthentication successful", "integration_created": "Integration created successfully", "error_resolved": "Error resolved successfully", "all_errors_resolved": "All errors resolved successfully", "data_management_success": "Data Management saved successfully", "data_management_updated": "Data Management updated successfully", "data_management_error": "There's a problem saving data management"}, "delete_integration_dialog": {"title": "Are you sure you want to delete {integrationName} integration?", "confirm_button": "Delete", "info": "You can remove the integration, but all previously imported data will be retained.", "keep_data": "Keep data", "remove_completely": "Remove completely", "deleteSuccess": "Integration deleted successfully"}, "close_dialog": {"cancel_button": "Cancel", "continue_button": "Continue", "save_button": "Save", "warning": "All progress will be lost", "confirm_button": "Confirm", "title": "Are you sure you want to go back without completing the integration?"}, "edit_integration": {"edit": "Edit", "attachments": "Attachments", "drag_and_drop": "Drag&Drop or", "browse_files": "Browse files", "select_all_projects": "Select all projects", "service_projects": "{serviceName} {entityName}", "testfiesta_projects": "Testfiesta Projects", "back": "Back", "continue": "Continue", "description": "Description", "details": "Details", "configurations": "Configurations", "external_url": "External URL", "no_external_url": "No external URL set", "enter_external_url": "Enter external URL", "add_url": "Add URL", "view_url": "View URL", "add_url_instruction": "This URL will be used to navigate to external screen/source where you can add defects", "view_url_instruction": "Please provide URL with which we can append ID and view defect on external screen", "enter_add_url": "Enter add URL", "enter_view_url": "Enter view URL"}, "error": {"resolveErrors": "Resolve these errors to resume integration sync.", "logoUploadFailed": "Failed to upload integration logo", "logoRemoveFailed": "Failed to remove integration logo", "fileSizeExceeded": "File size exceeds the maximum limit", "attachmentUploadFailed": "Failed to upload attachments", "sendEmailError": "Failed to send email. Please try again.", "fetchResourcesFailed": "Failed to fetch resources", "onlyOrgsCanIntegrate": "Only organizations can integrate with external services", "handleNotFound": "<PERSON><PERSON> not found", "saveFailed": "Failed to save integration", "initializationFailed": "Failed to initialize data", "failedToFetchProjects": "Failed to fetch projects", "fileFormatNotSupported": "File format not supported", "fetchingProjects": "Error fetching projects", "selectProjectsBoth": "Please select at least one project from both TestFiesta and Service", "invalidAccess": "Invalid access", "inactiveIntegration": "Reauthentication required", "integrationError": "Integration error", "apiIssues": "API issues occurred.", "seeDetails": "See details", "noErrorsToDisplay": "No errors to display", "tokenError": "Token is required", "failedToCreateIntegration": "Failed to create integration", "failedToResolveError": "Failed to resolve error", "failedToResolveAllErrors": "Failed to resolve all errors"}, "verification_token_dialog": {"placeholder": "Enter a valid token"}}, "projects": {"noPermissionToDo": "You don't have permission to {action} this project", "projects_list": "Projects list", "key": "Key", "archive_project_dialog": {"title": "Are you sure you want to {action} this project?", "warning": "Archived projects are removed from your active view, but can always be reviewed and all data is stored", "unarchive_warning": "Once the project is unarchived, it will be restored to its active state"}, "create_project": {"add": "Add", "add_users": "Add users", "back_to_projects": "Back to Projects", "back_to_project_info": "Back to Project Info", "cannot_blank": "This field cannot be left blank", "close_dialog": {"cancel_button": "Cancel", "continue_button": "Continue", "save_button": "Save", "confirm_button": "Confirm", "maxProjectNameLength": "Max 100 characters", "maxDescriptionLength": "Max 500 characters", "title": "All progress will be lost. You would need to start creation once again from scratch"}, "description": "Description", "project_name": "Project name", "project_key": "Project key", "title": "Project info", "remove": "Remove", "projectKeyMax10Chars": "A project key cannot be more than 10 characters", "projectKeyFormat": "A project key can only contain letters, numbers, hyphens, and underscores", "projectKeyExists": "Project key is not available", "create_demo_with_sample_data": "Create demo project with sample data"}, "delete_project_dialog": {"title": "Are you sure you want to delete this project?", "warning": "All data will be lost.", "recommend": "You can move projects to the Archive. Archived projects are removed from the active view, but they can always be viewed and all data is stored"}, "archive_project_success": "Project archived successfully", "archive_project_failed": "Failed to archive the project", "unarchive_project_success": "Project unarchived successfully", "unarchive_project_failed": "Failed to unarchive the project", "delete_project_success": "Project deleted successfully", "delete_project_failed": "Failed to delete the project", "delete_milestone_success": "Milestone deleted successfully", "delete_milestone_failed": "Failed to delete the milestone", "close_milestone_success": "Milestone closed successfully", "close_milestone_failed": "Failed to close the Milestone", "reopen_milestone_success": "Milestone reopened successfully", "reopen_milestone_failed": "Failed to reopen the Milestone", "empty_state": {"lets_get_started": "Let's get started by creating your first project.", "take_the_lead": {"part1": "Take the lead – hit", "part2": "Create Project", "part3": "now!"}, "title": "Elevate Your QA Journey!"}, "archived_empty_state": {"description": {"part1": "To maintain organized workflows, consider archiving completed projects", "part2": "for easy access when needed in the future."}, "title": "No Archived Projects Available"}, "no_data_available": "No data available", "demo_project": {"creation": {"failed": "Failed to create demo project", "check_status_failed": "Failed to check project status"}}}, "drag_and_drop": {"part1": "Drag & Drop or", "part2": "the logo or", "part3": "Browse files"}, "archived_empty_state": {"description": {"part1": "To maintain organized workflows, consider archiving completed {name}", "part2": "for easy access when needed in the future."}, "title": "No Archived {name} Available"}, "closed_empty_state": {"description": {"part1": "To maintain organized workflows, consider archiving completed {name}", "part2": "for easy access when needed in the future."}, "title": "No Archived {name} Available"}, "ignore": "Ignore", "resolve": "Resolve", "resultComment": "Result Comment", "needHelp": "Need help?", "UpdateConfigurations": "Update Configurations", "completeYourRegistration": "Complete your registration", "inviteSetupAccount": "{name} has set up an account for you. To complete the process, just specify a password and your name", "acceptInvite": "{name} invited you to join the party! To RSVP, add your first and last name below to join their organization.", "reEnterPassword": "Re-enter password", "areYouSureDecline": "Are you sure you want to decline an invitation?", "sureDeclineInvitation": "Are you sure you want to decline an invitation from {name}?", "whyDeclineInvitation": "Why are you declining this invitation?", "completeRegistration": "Complete registration", "invalidInvite": "This invite is expired or invalid", "noSharedStepsFound": "No shared steps found", "noMilestonesFound": "No Milestones", "click": "click", "here": "here", "toCreateOne": "to create one", "collapse": "Collapse", "externalId": "External ID", "source": "Source", "link": "Link", "unlink": "Unlink", "custom": "Custom", "view": "View", "testPlansBulkDuplicateTitle": "Are you sure you want to duplicate selected test plans?", "testPlansBulkDuplicate": "This will create an exact copy of the selected test plans, including all associated test cases, configurations, and settings. You can modify the duplicated version without affecting the original.", "analytics": "Analytics", "groupBy": "Group By", "List View": "List View", "Dashboard View": "Dashboard View", "avatarUploaded": "Avatar has been uploaded successfully", "createFolderDescription": "Create a folder to organize your test cases", "createFolder": "Create folder", "customFields": "Custom Fields", "references": "References", "externalCreatedAt": "External Created At", "externalUpdatedAt": "External Updated At", "parentFolder": "<PERSON><PERSON>er", "fieldRequired": "This field is required", "noResults": "No results", "noSearchResultsFound": "No search results found", "noResultFound": "Your results history will appear here once you start testing.", "noDefectFound": "No defects found", "projectUsers": "Project users", "projectsName": "Projects", "none": "None", "new": "New", "next": "Next", "emailUndeliverable": "The email sent to this user came back as undeliverable", "linkedTo": "Linked to", "addMatrix": "Add matrix", "editMatrix": "Edit matrix", "configurationMatrix": "Configuration matrix", "createRuns": "Create runs", "vertical": "Vertical", "horizontal": "Horizontal", "previous": "Previous", "all": "All", "allProjects": "All projects", "assignedTo": "Assigned To", "me": "Me", "allMilestones": "All Milestones", "allPlans": "All test plans", "allRuns": "All test runs", "errorOccurred": "An error occurred", "selected": "Selected", "projectRole": "Project Role", "show_only_project": "Show only project with activity", "search_by_name": "Search by name", "search": "Search", "filter": {"no_results": {"title": "No filtered results found", "description": "No {item} found matching your current filters", "clear_filters": "Clear filters"}}, "addProject": "Add project", "selectProjectsToInvite": "Select project(s) to invite", "enterTitle": "Enter title", "enterDescription": "Enter description", "enterType": "Enter type", "selectCases": "Select cases", "selectProject": "Select a project", "selectProjects": "Select projects", "selectTag": "Select a tag", "testCasesCount": "Test cases count", "selectRole": "Select a role", "dateCreation": "Date of creation", "project": "Project", "projectsMenu": "Projects", "myWorkspace": "My Workspace", "groups": "Groups", "summary": "Summary", "defaultAccess": "Default access", "projectCompleted": "This project is completed", "invalidUrl": "Invalid URL", "noProjects": "No projects available", "noTags": "No tags available", "no_tags": "No tags", "description": "Description", "addComment": "Add comment", "select": "Select", "steps": "Steps", "template": "Template", "folder": "Folder", "priority": "Priority", "prompt": "Prompt", "caseAddFailed": "Unable to add test case", "tag": "Tag", "title": "Title", "noDescription": "No description", "noDescriptionAvailableYet": "No description available yet.", "uploadFilesToProvide": "Upload files to provide additional context or support for your test results.", "addAttachment": "Add attachment", "browseFile": "Browse file", "tags": "Tags", "addTags": "Add tags", "milestones": "Milestones", "roles": "Roles", "roleLevelRequired": "Role level is required.", "problemFetchingData": "We are having problem fetching data", "status": "Status", "labels": "Labels", "testFormatDescription": "These fields are used in a test format and help define test case structure or requirements.", "resultFormatDescription": "These fields appear in the result format and define the actions testers need to complete when a specific status is selected.", "choosePriority": "Choose Priority", "chooseStatus": "Choose <PERSON>", "estimation": "Estimation", "addTag": "Add tag", "createTag": "Create tag", "createATag": "Create a tag", "addEmail": "Add email", "createRole": "Create role", "createNewRole": "Create new role", "stepTitlePlaceholder": "Step {index}", "customField": "Custom field", "editRole": "Edit role", "clearAll": "Clear all", "limitConfigToCase": "Optionally limit the configurations this case applies to.", "issue": "Issue", "optional": "Optional", "estimate": "Estimate", "loading": "Loading", "addIssue": "Add issue", "retry": "Retry", "attachments": "Attachments", "stopUploadingMessage": "Are you sure you want to stop the upload?", "stopUploading": "Stop Uploading", "chooseFile": "Choose file", "chooseRole": "Choose role", "setUp": "Set-up", "projectAlreadyAdded": "This project has already been added.", "projectFolders": "Project folders", "chooseProject": "Choose project", "addSharedStep": "Add shared step", "sharedStep": "Shared step", "addStep": "Add step", "addStepDescription": "Add step description", "stepDescription": "Step description", "testCaseUpdated": "Test case updated", "caseUpdateFailed": "Unable to update test case", "expectedResult": "Expected result", "runs": "Runs", "addDescription": "Add description", "editRun": "Edit run", "addRun": "Add run", "state": "State", "cannotExceed9999": "Cannot exceed 9999", "casesSelected": "cases selected", "noCasesSelected": "No cases selected", "includeAllTestCases": "Include all test cases", "selectCasesToInclude": "Select cases to include", "more": "More", "noMatchesFound": "No matches found", "noMatchingResults": "No matching results found", "createItemTag": "Create {tagName} tag", "text": "Text", "noHistory": "No history available", "history": "History", "sessions": "Sessions", "caseDeleteFailed": "Unable to delete test case", "setAllStepsPassed": "Set all steps to passed", "comments": "Comments", "issues": "Issues", "results": "Results", "createUser": "Create user", "noDataAvaliable": "No data available", "failedToLoadChart": "Failed to load the chart. Please try again later.", "selectAvatar": "Select avatar", "dropImagePlaceholder": "Drag an image here...", "browseText": "Browse", "addMemberToOrg": "Are you ready to add members to your organization ?", "inviteMembers": "Invite members to your organization via email.", "resendInvite": "Resend invite", "inviteNewUser": "Invite new user", "unlockTheFun": "Unlock the fun", "updateAvatar": "Update avatar", "org": "Organization", "orgs": "Organizations", "leave": "Leave", "elapsed": "Elapsed", "addResult": "Add result", "editResult": "Edit Result", "resultHistory": "Result History", "changeMyUsername": "Change my username", "invitationSent": "Invitation was sent successfully", "usernameUpdated": "Username was updated successfully", "profileUpdated": "Profile was updated sucessfully", "avatarUpdatedSuccessfully": "Avatar updated successfully", "nameInUse": "Username {username} is not available", "handleInUse": "Handle \"{handle}\" is not available", "problemProcessingRequest": "We are having problem processing your request.", "noCasesYet": "No test cases yet?", "itsVibeHere": "It's a pre-party vibe here!", "waitingforcases": "Waiting for the test cases to turn this workspace into a testing rave!", "orgAccountNameLabel": "This will be the username of your account on TestFiesta.", "noOrg": "You haven't created any organization yet.", "toAccessDashboard": "Please create an organization to access dashboard and anyltics features.", "run": "Run", "operatingSystem": "Operating System", "branches": "Branches", "addNewCustomField": "Add new custom field", "projectName": "Project name", "createProject": "Create Project", "createDemoProject": "Create demo project", "accessTokens": "Access tokens", "noExecutions": "No executions related to this test case", "general": "General", "copyOf": "Copy of", "linkCopied": "Link copied to clipboard!", "onAccessToken": "No access tokens available", "runDeleted": "Test run was deleted successfully", "orgName": "Organization name", "enterOrgHandle": "Enter organization handle", "personalAccount": "Your personal account", "removeMember": "Remove from organization", "orgUpdated": "Organization profile was updated successfully", "personalProfile": "Personal profile", "firstName": "First name", "settings": "Settings", "cancelSubscription": "Cancel subscription", "deleting": "deleting...", "lastName": "Last name", "username": "Username", "signIn": "Sign in", "startHere": "Start here", "selectFolder": "Select folder", "noFoldersFound": "No folders found", "startTypingToSearch": "Start typing to search folders", "selectTemplate": "Select template", "period": "Period", "customize": "Customize", "maxProjectsSelected": "You can't select more than 5 projects.", "xHorizontal": "X-horizontal", "yvertical": "Y-vertical", "dashboard": {"fieldName": "Field name", "breakdown": "Breakdown", "filterField": "Filter field", "filterName": "Filter name", "filterCondition": "Filter condition", "noFieldError": "Please select a field", "filterValue": "Filter value", "color": "Color", "noChartsTitle": "No charts? Start adding them by clicking the button below!", "noChartsDescription": "Click Customize to add charts from the library and tailor your dashboard to your needs.", "xAxis": "X Axis", "overridden": "Your range exceeded the limit and was adjusted to: {startDate} - {endDate}.", "activityOverTime": "Activity Over Time", "numberOfDefectsToolTip": "This chart is customizable and allows you to toggle between test runs and test folders. Additionally, you can resize the chart to suit your preferences; a larger chart accommodates more data for a comprehensive view.", "averageTimeInStatus": "Average time in Status/Priority", "name": "Dashboard", "advancedFilters": "Advanced filters", "chartBuilder": "Chart Builder", "chartBuilderTooltip": "The Chart Builder allows you to design and customize your data visualizations with ease. Select chart types, adjust filters, and personalize your data display to create insights that matter to you. Fine-tune labels, colors, and groupings to craft the perfect representation of your data, all in a few simple steps.", "customizeYourInsights": "Customize Your Insights", "chartType": "Chart type", "entityType": "Entity type", "selecteEntity": "Select entity", "StatusPriorityDistribution": "Status/Priority Insights", "radarChart": "Radar Chart", "otherCharts": "Other charts", "rerunWithCycle": "Rerun with number of defects", "rerunWithCycleToolTip": "You have the flexibility to customize this chart by choosing the test runs. Additionally, you can resize the chart to suit your preferences; a larger chart accommodates more data for a comprehensive view.", "activityOverTimeTooltip": "You have the flexibility to customize this chart by choosing the indicator(s) and adjusting the date display (daily, weekly, monthly, etc..). Additionally, you can resize the chart to suit your preferences; a larger chart accommodates more data for a comprehensive view.", "averageTimeInStatusToolTip": "You have the flexibility to customize this chart by choosing the indicator(s). Additionally, you can resize the chart to suit your preferences; a larger chart accommodates more data for a comprehensive view.", "barChartBreakdown": "Priority/Status Breakdown", "barChartToolTip": "This chart is customizable and allows you to toggle between priority and status of test cases, test runs or defects. Additionally, you can resize the chart to suit your preferences; a larger chart accommodates more data for a comprehensive view.", "donutToolTip": "This customizable chart offers options to toggle between test cases, test runs, and defects, allowing users to analyze them based on statuses and priorities.", "defaultView": "Default View", "addNewView": "Add new view", "viewName": "View name", "setDefault": "Set as default", "editDashboard": "Edit Dashboard", "placeholderDashboard": "Dashboard name", "selectEntityToAddChart": "Please select an entity to add a chart.", "willLostData": "All charts and data associated with this dashboard will be permanently lost.", "deleteConfirm": "Are you sure you want to delete this dashboard?", "cannotDeleteDefaultDashboard": "The default dashboard cannot be deleted.", "dashboardMarkedAsEditable": "Dashboard has been marked as editable", "dashboardMarkedAsNonEditable": "Dashboard has been marked as non-editable", "dashboardSelected": "This dashboard has already been selected.", "alreadyDefault": "This dashboard is already set as the default.", "nameRequired": "Dashboard name is required.", "editable": "Editable", "default": "<PERSON><PERSON><PERSON>", "dashboardNotEditable": "The dashboard is not editable.", "backToDashboard": "Back to Dashboard", "deleteChart": "You don't have permission to delete charts.", "addChart": "You don't have permission to add charts.", "signleValueChart": "Single value chart", "donutChart": "Donut Chart", "lineChart": "Line Chart", "barChart": "Bar Chart", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "lastMonth": "Last month", "noPermissionToDo": "You don't have permission to {action} this dashboard"}, "DashboardName": "Dashboard name", "orgAccount": "Organization acccount", "testResultConfirmDelete": "Are you sure you want to delete this test result?", "rememberMe": "Remember me", "doNotHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "logintoyouraccount": "Log in here", "terms": "Terms", "privacy": "Privacy", "switchAccount": "Switch to another account", "security": "Security", "signUp": "Sign up", "signUpTo": "Sign up here", "createAccount": "Create your account", "createMyAccount": "Create my account", "createYourOrg": "Create your organization", "createOrgDescription": "Organizations are the workspace where you and your teammates as well can work collaboratively", "sendEmail": "Send Email", "signUpsuccess": "Signup success", "signUpfail": "Failed to create user", "user": "User", "users": "Users", "newOrganization": "New organization", "counts": "Counts", "profile": "Profile", "email": "Email", "emailLabel": "Your email", "testfiesta": "testfiesta", "portalFor": "Portal for", "emailOrUsernameLabel": "Your email or username", "passwordLabel": "Your password", "confirmPasswordLabel": "Confirm password", "importFromCSV": "Import from CSV/XLS/XLSX/XML", "importFromCSVDescription": "Assign test cases to their folders", "dragAndDropTestCases": "Drag and drop test cases to the appropriate folder", "importFromXML": "Import from XML", "importPreview": "Import preview", "exportToCSV": "Export to CSV", "selectTestRuns": "Select test runs", "importTo": "Import to", "exportToSpreadsheet": "Export to Spreadsheet", "goToColumnMapping": "Go to Column Mapping", "backToColumnMapping": "Back to Column Mapping", "importAll": "Import all", "gotoRepository": "Go to Repository", "gotoTestCase": "Go to Test Case", "assignDate": "Assign date", "backToRoles": "Back to Roles", "dontShowAgain": "Don't show again", "roleInfo": "Role info", "roleLevel": "Role level", "name": "Name", "surname": "Surname", "organizationLevel": "Organization level", "projectLevel": "Project level", "surnameLabel": "Your surname", "logout": "Log out", "newStatus": "Default status for new", "newPriority": "Default priority for new", "preferenceDeleteConfirm": "Are you sure you want to delete \"{name}\" {type}?", "thisStatusComplete": "This status indicates that work on a {category} is complete", "enterEmail": "Enter email", "bio": "Bio", "roleHasOrgPermissions": "This role has org permissions not available at project level.", "testActivities": "Test activities", "projectLevelTooltip": "The project-level role overrides the organization-level role.", "tagTooltip": "Tags allow for new ways of filtering and organizing team members.", "enterUsername": "Enter username", "changeUsername": "Change username", "changeOrgName": "Change organzation username", "enterbio": "Enter bio", "enterurl": "Enter URL", "enterlocation": "Enter location", "savechanges": "Save changes", "yes": "Yes", "no": "No", "unassign": "Unassign", "todo": "To do", "completed": "Completed", "youWantToLeaveOrg": "Are you sure you want to leave {orgname}? you will lose all your access and data related to the organization.", "genatenewtoken": "Generate new token", "generating": "Generating", "duplicateRunCreated": "Duplicate run created", "errorCreateDuplicateRun": "Error in creating duplicating run", "editProfile": "Edit profile", "revokeall": "Revoke all", "updateProfile": "Update profile", "userProfileChanged": "User profile changed", "changePassword": "Change password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm password", "updatePassword": "Update Password", "orgPermission": "Organization permissions", "change": "Change", "organizationName": "Organization name", "organizationHandle": "Organization handle", "allYourDataIsStored": "All your data is stored", "reports": "Reports", "deleteTestSuite": "Delete test suite", "unauthorizedAccess": "Unauthorized Access: You don't have permission to access this resource.", "confirmDeleteTestSuite": "Are you sure you want to delete this test suite?", "deleteTestRun": "Delete test run", "duplicate": "Duplicate", "duplicateToProject": "Duplicate to project", "selectedCases": "Selected cases", "selectProjectFirst": "Please select a project first", "createSharedStep": "Create shared step", "addChildStep": "Add child step", "couldnotMoveParentToItsChild": "You cannot move a parent folder to its child folder.", "confirmDeleteTestRun": "Are you sure you want to delete this test run?", "cancel": "Cancel", "actions": "Actions", "projectRequired": "Project is required", "projectRoleRequired": "Project role is required", "defaultRoleRequired": "Default role is required", "projectKey": "Project key", "createNewSharedStep": "Create new shared step", "passwordChanged": "Password changed", "remove": "Remove", "case": "Case", "roleRequired": "role is required", "createQuickPlan": "Create quick plan", "role": "Role", "roleTitle": "Role Title", "selectRoleLevel": "Select role level", "enterRoleTitle": "Enter role title", "userRolesUpdated": "User role(s) have been updated successfully.", "noRoleAssigned": "No role assigned", "defaultRole": "Default role", "joinOrganization": "Join organization", "restoreDefault": "Restore default", "you are sure": "You are sure?", "you cannot reverse this action": "You cannot reverse this action", "user successfully deleted": "The user was successfully deleted", "roleSuccessfullyDeleted": "The roles were successfully deleted", "resetPassword": "Reset password", "changePermission": "Change permission", "changePersonalization": "Change Personalization", "sendResetlink": "Send reset link", "inputEmail": "Input email", "editCase": "Edit case", "editExecution": "Edit Execution", "addCase": "Add case", "caseCreated": "Case created", "invalidTimeFormat": "Invalid time format", "invalidTimeZone": "Invalid time zone", "format": "Format", "caseAdded": "Test case added", "repositories": "Repositories", "password reset email sent": "Password reset email sent", "testFormats": "Test formats", "resultFormats": "Result formats", "values": "Values", "testFormat": "Test format", "resultFormat": "Result format", "password": "Password", "generalInfo": "General info", "noMatchingAssignees": "No matching assignees", "noMatchingTags": "No matching tags", "noMatchingRoles": "No matching roles", "noMatchingProjects": "No matching projects", "defaultValueError": "The default value can't be removed as one must always be selected.", "atLeastOneStatusCounted": "At least one status must be selected to be counted.", "atLeastOneProjectRequired": "At least one project is required to be selected.", "forgotPassword": "Forgot password?", "forgotPasswordHeader": "Password recovery", "loginSuccess": "Login success", "orgAccountName": "Organization account name", "orgHandle": "Organization handle", "reset": "Reset", "createTestSuite": "Create test suite", "createTestCase": "Create test case", "editTestCaseConfirmation": {"title": "Test Case Edit Confirmation", "description_part1": "This test case is not part of your current run. Editing it will change the case for everyone.", "description_part2": "Are you sure you'd like to continue?"}, "editTestCase": "Edit test case", "addTestCaseFromRopository": "Add test case from the Repository", "backToTestCases": "Back to Test Cases", "backToTestExecutions": "Back to Executions", "backToTestRuns": "Back to Test Runs", "backToImportFromCSV": "Back to Import", "goToPreview": "Go to preview", "columnMapping": "Column Mapping", "csvField": "CSV Field", "testFiestaField": "Test Fiesta Field", "repository": "Repository", "chooseUser": "Choose user", "createNewTestCase": "Create new test case", "createQuickTestCase": "Create quick test case", "deleteTestCase": "Delete test case", "confirmDeleteTestCase": "Are you sure you want to delete this test case?", "myOrgs": "My organizations", "createOrg": "Create organization", "tellUsAboutOrg": "Tell us about your organization", "enterOrgname": "Enter organization name", "sessionExpired": "Your session has expired", "create": "Create", "assignTo": "Assign to", "orgCreated": "Created organization", "sendInvite": "Send Invite", "invite link sent": "Invite link sent", "projectNotFound": "Project not found", "inviteUpdated": "The invite for {email} has been successfully updated.", "unableToUpdateInvite": "We were unable to update the invite. Please try again later.", "noProjectLeftToAssign": "No projects left to assign", "grantOrganizationLevelPermissions": "Grant Organization level permissions", "reassignToRole": "You have changed the role of {userCount} users to {newRole}", "unableToReassign": "Unable to reassign roles for the selected users", "receivedInvitationFrom": "You received and invitation from", "leftOrganization": "You have been removed from {orgname} successfully.", "unableToRemoveYou": "We were unable to remove you from {orgname} please try again later.", "to": "to", "duedate": "Due Date", "orgLevelActionDisabled": "This action is disabled based on your organization-level role.", "setDueDate": "Set due date", "toJoin": "to join", "compare": "Compare", "accept": "Accept", "decline": "Decline", "skipNow": "Skip now", "initialVersion": "Initial version", "improvedVersion": "Improved version", "memberDeleted": "{username} was removed successfully from {orgname}", "memberNotDeleted": "Unable to remove {username} from {orgname}", "welcome": "Welcome", "noMembersInvited": "You haven't invited any member yet!", "verifiedResetlink": "Password reset link verified", "membersLeft": "Members left", "memberLeft": "Member left", "signInHeader": "Welcome back", "noPermissionToViewProjectUsers": "You don't have permission to view project users", "signUpHeader": "Create an account", "confirmEmailHeader": "Confirm Your Email", "thisFieldIsRequired": "This field is required", "loginWithSSO": "Log in with SSO", "testfiestaOrganization": "Testfiesta Organization", "testfiestaOrganizationTooltip": "Testfiesta Organization can be found in your invite email.", "continueWithSSO": "Continue with single sign-on", "confirmEmailSuccess": {"part1": "Done! We've sent a confirmation email to", "part2": "Please check and click the link", "part3": "inside to complete the process. Thanks!"}, "tagsPlaceholder": {"title": "Ready to create first tag?", "description": {"part1": "Your tags page is currently empty. Let's change that! Click", "highlight": "Create tag", "part2": "and add tags that reflect the specific topics,", "part3": "themes, or categories relevant to your project."}}, "twoFactorHeader": "Set up two-factor authentication", "twoFactorDrawerHeader": "Two-factor authentication", "twoFactorDrawerStep1SubHeader": "Choose authentication method", "twoFactorDrawerStep1SubContent": "How would you like to receive your authentication codes?", "twoFactorDrawerStep1Radio1Label": "Phone number", "twoFactorDrawerStep1Radio1Description": "Use an authentication app to generate authencation code (e.x. Google Authentication)", "twoFactorDrawerStep1Radio1Labe2": "Authentication app", "twoFactorDrawerStep1Radio2Description": "We will send an authentication code to your mobile device", "twoFactorDrawerStep2SubHeader": "Enter your phone number", "twoFactorDrawerStep2SubContent": "We will send a confirmation code to the phone number you specified", "twoFactorDrawerStep2PhoneLabel": "Phone number", "twoFactorDrawerStep2PhonePlaceholder": "****** 567 890", "twoFactorDrawerStep3SubHeader": "Enter verification code", "twoFactorDrawerStep3SubContentPre": "We sent the verification code to  +", "twoFactorDrawerStep3SubContentSup": "Enter it in the field below.", "twoFactorDrawerStep4SubHeader": "Recovery code", "twoFactorDrawerStep4SubContent": "Please keep this code in a safe place. This code is used to restore access to the application if you cannot obtain authentication codes. Once the two-factor authentication is complete, you will not be able to retrieve this code", "skip": "<PERSON><PERSON>", "twoFactorStepSetupNow": "Set up now", "twoFactorStepWarning": "Your organisation requires two-factor authentication!", "doNotGetEmail": "If you don't get any email", "resendEmail": "Resend it", "backToSignUp": "Back to Create my account", "backToSignIn": "Back to Log In Page", "tagCreationHeader": "Create new tag", "tagEditHeader": "Edit tag", "titleLabel": "Title", "descriptionLabel": "Description", "typeLabel": "Type", "members": "Members", "requestLogin": "Please login", "inviteMember": "Invite member", "filters": "Filters", "usedIn": "Used in", "filterByRole": "Filter by role", "atLeastOneEmailRequired": "At least one email must be provided", "validEmail": "Please input valid email", "validPassword": "Password must be at least 6 characters long.", "passwordUppercase": "Password must contain at least one uppercase letter.", "passwordNumber": "Password must contain at least one number.", "passwordSpecial": "Password must contain at least one special character.", "confirmPasswordMustMatch": "Confirmation password must match your password.", "inviteUsingEmail": "Invite new member using email", "searchNameEmail": "Search users by username or email", "inviteSent": "<PERSON><PERSON><PERSON> sent", "atLeastOneOptionSelected": "At least one option must be selected", "inviteMemberTo": "Invite member to", "unableToUpdateRoleOfUser": "Role update was unsuccessful please try again.", "roleUpdateSuccess": "Role of {username} was updated successfully.", "roleUpdateNotSuccessful": "Unable to update role of {username}.", "systemRoleSelected": "One or more of the selected roles are system roles and cannot be deleted.", "roleHasAssignees": "One or more of the selected roles are currently assigned to users and cannot be deleted.", "inputPlaceholder": "Enter your {field}", "invite": "Invite", "removeMemberConfirm": "Are you sure you want to remove {username}, the user will access to {orgname}.", "deleteOrgWarning": "Once deleted, it will be gone forever. Please be certain.", "deleteOrg": "Delete this organization", "createOrgNoti": "Please create an Organization and select it.", "failedToken": "Failed to create a new token, Please try again later", "sessionDelete": "Delete the selected session", "sessionDeleteConfirm": "Are you sure you want to delete this execution? This action cannot be undone.", "sessionDeleted": "Succefully deleted", "changeAvatar": "Change avatar", "changeYourRole": "Change your role?", "confirmOwnerAssignment": "Confirm owner assignment", "changeOwnerDescription1": "You're about to remove your Owner role from your own account", "changeOwnerDescription2": "By changing your role, you will lose access to Owner-level permissions. Only another Owner will be able to assign the role back to you.", "changeOwnerDescription3": "To proceed, please confirm your identity by entering your password.", "newOwnerDescription1": "You're about to assign the Owner role to this user.", "newOwnerDescription2": "This role has full access to all organization data and settings, including billing, user management, and system configurations.", "newOwnerDescription3": "To proceed, please confirm your identity by entering your password.", "confirmOrgDelete": "Deleting the {orgname} organization will delete all data that is related to it. Are you sure you want to do that?", "orgDeleted": "Organization was deleted successfully", "save": "Save", "generate": "Generate", "regenerate": "Regenerate", "delete": "Delete", "export": "Export", "exportTo": "Export to", "fields": "Fields", "expand": "Expand", "testRuns": "Test Runs", "testPlans": "Test Plans", "testRun": "Test run", "rerunned": "Rerunned", "testPlan": "Test plan", "selectAll": "Select all", "numberOfTestRuns": "Number of test runs", "numberOfTestPlans": "Number of test plans", "addTestRunToTestPlans": "Add test run to test plans", "linkTestRunsToTestPlans": "Link test runs to test plans", "testCases": "Test Cases", "noPermissionToDo": "You don't have permission to {action} this {type}.", "noAssigned": "No one assigned", "testSuite": "Test suite", "noAttachments": "No attachments", "selectStatus": "Select status", "manageTestCaseEasily": "Manage Your Test Cases Easily", "startCreateTestCase": "Keep all your test cases in one place. Start organizing now with 'Create Test Case'", "testFolders": "Test folders", "testCase": "Test case", "testcase_close_dialog": "Are you sure you want to go back without creating your test case?", "numberOfTestCases": "Number of test cases", "keepAssignedTo": "Keep assigned to", "defects": "Defects", "addDefects": "Add Defects", "numberOfDefects": "Number of defects", "applyScopeOfRights": "Copy the rights of an existing user", "administration": "Administration", "integrationsMenu": "Integration", "projectAdmin": "Project Admin", "creationDate": "Creation Date", "testRunsToMilestone": "Test runs to milestone", "testRunsToPlan": "Test runs to test plans", "testPlansToMilestone": "Test plans to milestone", "lastActivity": "Last Activity", "lastUpdate": "Last Update", "lastChanges": "Last Changes", "chooseImage": "Choose image", "selectAStatus": "Select a status", "caseDeleted": "Test case deleted", "avatarChanged": "Avatar changed", "emailRequired": "Email is required", "emailOrUsernameRequired": "Email or username is required", "passwordRequired": "Password is required", "optionGroupsRequires": "A group requires at least one option", "inputRequired": "Input is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "usernameRequired": "Username is required", "usernameInvalidCharacters": "Username can only contain letters, numbers, underscores (_), and hyphens (-).", "usernameConsecutiveInvalid": "Username cannot contain consecutive underscores (__), periods (..). or hyphens (--).", "usernameInvalidEnd": "Username cannot end with an underscore (_), period (.), or hyphen (-).", "usernameLength": "Username must be between 2 and 30 characters long.", "usernameInvalidStart": "Username cannot start with an underscore (_), period (.), or hyphen (-).", "min2Chars": "Minimum 2 characters", "runWithPinata": "Run with <PERSON><PERSON><PERSON> ", "min6Chars": "Minimum 6 characters", "min2max50Chars": "Minimum 2 and maximum 50 characters", "newPersonalAccessToken": "New personal access token", "personalAccessTokensdesc": "Personal access tokens are for programmatic access to the TestFiesta APIs", "tokenFor": "Token is for", "enterName": "Enter name", "expiration": "Expiration", "enterNewUsername": "Enter new username", "invalidUsername": "Invalid username", "noCommentsAvailable": "No comments available", "atLeastOneTestRun": "Please add at least one test run to proceed by clicking actions button.", "apply": "Apply", "applyScope": "Apply the scope of rights of existing user", "statusesn": "Statuses", "Priorities": "Priorities", "statusColors": "Status colors", "priorityColors": "Priority colors", "setAsDefaultForNewItems": "Set as default for new items", "estimateHint": "Estimate in HH:MM,2H, 3M", "chooseTags": "Choose Tags", "replaceExistingTags": "Replace existing tags", "replaceTag": "Replace tag", "confirm": "Confirm", "info": {"selectProject": "Select a project to get started", "addFolder": "Add a new folder", "deleteTestRun": "Delete test run"}, "edit": "Edit", "success": {"addResult": "Result added", "deleteResult": "Result deleted", "updateResult": "Result updated", "runCreated": "Test run created", "runDeleted": "Test run was deleted successfully", "caseCreated": "Case created", "caseAdded": "Test case added", "caseUpdated": "Test case updated", "caseBulkRemoveSuccess": "Test cases were deleted successfully", "caseDeleted": "Test case was deleted successfully", "updateComment": "Comment updated", "deleteComment": "Comment deleted", "folderCreated": "Folder created successfully", "createComment": "Comment created", "folderDeleted": "Folder deleted successfully", "projectDeleted": "Project deleted successfully", "projectCreated": "Project created successfully", "projectUpdated": "Project updated successfully", "folderUpdated": "Folder updated successfully", "executionUpdated": "Execution updated successfully", "executionsUpdated": "{count} Executions updated successfully", "newTagApplied": "New tag is applied", "tagRemoved": "A tag is removed", "executionDeleted": "Execution deleted successfully", "testCaseAssigned": "Test case assigned successfully", "executionDueAt": "Due date set successfully", "testRunUpdated": "Test run updated successfully", "milestoneCreated": "Milestone created successfully", "milestoneUpdated": "Milestone updated successfully", "milestoneDeleted": "Milestone deleted successfully", "orgnizationUpdated": "Organization updated successfully", "orgnizationDeleted": "Organization deleted successfully", "planDeleted": "Test plan was deleted successfully", "planCreated": "Created \"{item}\" test plan ", "duplicatedTestPlans": "Duplicated {item} test plans", "duplicatedTestPlan": "Duplicated {item} test plan", "templateCreated": "Template created successfully", "templateUpdated": "Template updated successfully", "templateDeleted": "Template deleted successfully", "testResultAdded": "The test result has been successfully added", "testResultUpdated": "The test result has been successfully updated", "testResultDeleted": "The test result has been successfully deleted", "deleteAttachment": "Attachment deleted successfully", "uploadedAttachments": "Attachments uploaded successfully.", "stopUpload": "The file upload has been successfully canceled", "testPlanUpdated": "Test plan updated successfully", "defectCreated": "Defect created successfully", "dashboardCreated": "Dashboard created successfully", "dashboardDeleted": "Dashboard deleted successfully", "dashboardUpdated": "Dashboard updated successfully", "defectLinked": "Defect linked successfully", "tagsUpdated": "Tags updated successfully"}, "statuses": {"untested": "Untested", "passed": "Passed", "failed": "Failed", "retest": "Retest", "blocked": "Blocked", "skipped": "Skipped", "inconclusive": "Inconclusive", "pending": "Pending", "paid": "Paid", "draft": "Draft", "active": "Active", "inReview": "In Review", "rejected": "Rejected", "closed": "Closed", "new": "New", "inProgress": "In Progress", "complete": "Complete"}, "priorities": {"high": "High", "medium": "Medium", "low": "Low"}, "inviteStatuses": {"processed": "Processed", "dropped": "Dropped", "delivered": "Delivered", "deferred": "Deferred", "bounce": "<PERSON><PERSON><PERSON>"}, "testConfigurations": {"chrome": "Chrome", "firefox": "Firefox", "safari": "Safari", "edge": "Edge", "ie": "IE", "android": "Android", "ios": "iOS", "linux": "Linux", "macos": "MacOS", "windows": "Windows", "headless": "Headless", "mobile": "Mobile", "desktop": "Desktop", "tablet": "Tablet", "landscape": "Landscape", "portrait": "Portrait", "darkMode": "Dark Mode", "lightMode": "Light Mode", "highContrast": "High Contrast", "lowContrast": "Low Contrast", "noJavaScript": "No JavaScript", "noCSS": "No CSS", "noImages": "No Images", "noAudio": "No Audio", "noVideo": "No Video", "noCookies": "No Cookies", "noFlash": "No Flash", "noPopups": "No Popups", "noReferrer": "No Referrer", "noCache": "No Cache", "noStorage": "No Storage", "noIndexedDB": "No IndexedDB", "noServiceWorker": "No Service Worker", "noGPU": "No GPU", "noWebGL": "No WebGL", "noWebRTC": "No WebRTC", "noWebSockets": "No WebSockets", "noGeolocation": "No Geolocation", "noMicrophone": "No Microphone", "noCamera": "No Camera", "noMIDI": "No MIDI", "noNotifications": "No Notifications", "noBattery": "No Battery"}, "error": {"error": "Error", "fieldTooShort": "Provided data too short for text assist", "resultAddFailed": "Unable to add result", "milestoneCreatedFailed": "Unable to create milestone", "resultDeleteFailed": "Unable to delete result", "resultUpdateFailed": "Unable to update result", "runDeleteFailed": "Unable to delete test run", "projectDeleteFailed": "Unable to delete project", "runUpdateFailed": "Unable to update test run", "runCreateFailed": "Unable to create test run", "projectUpdateFailed": "Unable to update project", "projectAddFailed": "Unable to add project", "caseAddFailed": "Unable to add test case", "caseUpdateFailed": "Unable to update test case", "caseDeleteFailed": "Unable to delete test case", "caseBulkRemoveFailed": "Unable to delete test cases", "folderCreateFailed": "Unable to create folder", "commentCreateFailed": "Unable to create comment", "commentDeleteFailed": "Unable to delete comment", "folderDeleteFailed": "Unable to delete folder", "folderUpdateFailed": "Unable to update folder", "updateComment": "Unable to update comment", "milestoneCreateFailed": "Unable to create milestone", "duplicateEmail": "This email address is already in use", "invalidStepDescription": "Invalid step description", "invalidComment": "Invalid comment", "invalidBranch": "Invalid branch", "invalidAvatar": "Invalid avatar", "invalidFirstName": "Invalid first name", "failedToFetchExecutions": "Failed to fetch executions", "failedToFetchExecution": "Failed to fetch execution", "invalidLastName": "<PERSON>valid last name", "invalidUsername": "Invalid username", "invalidEmail": "Invalid email", "invalidEmailOrUsername": "Invalid email or username", "invalidPassword": "Invalid password", "invalidOrgname": "Invalid organization name", "invalidName": "Invalid name", "invalidUrl": "Invalid URL", "invalidLocation": "Invalid location", "invalidBio": "Invalid bio", "duplicatedTestPlans": "Duplicated {item} test plans", "duplicatedTestPlan": "Duplicated {item} test plan", "invalidCurrentPassword": "Invalid current password", "invalidNewPassword": "Invalid new password", "invalidConfirmPassword": "Invalid confirm password", "invalidRole": "Invalid role", "invalidRoleName": "Invalid role name", "roleCreateFailed": "Unable to create role", "roleUpdateFailed": "Unable to update role", "roleDeleteFailed": "Unable to delete role", "roleNameTooShort": "Role name must be at least 2 characters", "roleNameTooLong": "Role name cannot exceed 100 characters", "roleAlreadyExists": "A role with this name already exists", "cannotDeleteRoleWithMembers": "<PERSON><PERSON> delete a role that has assigned members", "invalidToken": "Invalid token", "invalidTestCase": "Invalid test case", "invalidTestSuite": "Invalid test suite", "invalidTestRun": "Invalid test run", "invalidSession": "Invalid session", "invalidIssue": "Invalid issue", "invalidEstimate": "Invalid estimate", "invalidPriority": "Invalid priority", "invalidStatus": "Invalid status", "invalidDescription": "Invalid description", "invalidDate": "Invalid Date", "invalidExpectedResult": "Invalid expected result", "requiredField": "This field is required", "max255Chars": "Maximum 255 characters", "connection": "Please check your internet connection and try again", "invalidPhoneNumber": "Invalid phone number", "lightColor": "The selected color is too light and may reduce label visibility. Please choose a color with more contrast.", "AIImprovementUnable": "Unable to generate improvement using AI. Please provide more details or review the input to help refine the results.", "executionUpdateFailed": "Unable to update test execution", "executionDeleteFailed": "Unable to delete test execution", "internalServerError": "Internal server error", "planDeleteFailed": "Unable to delete test plan", "fileFormatNotSupported": "One or more files have unsupported formats. Please check and try again.", "uploadedAttachments": "Failed to upload the attachments. Please try again later.", "failedToUploadAvatar": "Failed to upload the avatar image", "failedToAddTestResult": "Failed to add the test result. Please try again later", "failedToUploadAttachment": "Failed to upload the attachment. Please try again later.", "failedToUpdateTestResult": "Failed to update the test result. Please try again later", "failedToDeleteTestResult": "Failed to delete the test result. Please try again later", "failedToDeleteAttachment": "Unable to delete attachment", "failedToUpdateTestPlan": "Failed to update the test plan. Please try again later", "maxFolderDepth": "Maximum folder depth is {max}", "tokenNameLength": "Token name must be between 2 and 64 characters long", "tokenNameInvalidCharacters": "Access token names can only contain characters a-z, A-Z, 0-9, -, _, and ~.", "ssoSigninFailed": "SSO sign-in failed", "failedToFetchUser": "Failed to fetch user data"}, "discard_description": "This is the standard discard dialog description", "discard_cancel": "Cancel", "discard_confirm": "Confirm", "invite_discard_title": "Are you sure you want to revert invite?", "invite_discard_description": "Reverting an invite will withdraw the invite which was sent to the user", "request_delete_title": "Are you sure you want to delete this request?", "user_delete_title": "Are you sure you want to delete user?", "user_delete_description": "Deleting a user will completely remove their access to {projectName}", "user_remove_title": "Are you sure you want to remove user?", "user_remove_description": "Removing a user will remove their access to TestFiesta", "request_decline_title": "Are you sure you want to decline request?", "setAllStepsAsPassed": "Are you sure you want to set all steps as Passed", "allStepsAsPassed": "All steps in the test execution will be set to passed", "subscriptionUpdateSuccess": "Subscription update successful... refresh page if changes haven't taken effect", "manageSeats": "Manage Seats", "confirmUpdateSeatsTitle": "Update Membership Seats", "confirmUpdateSeats": "You are about to {action_phrase}. Depending on your current plan, charges may be incurred. Are you sure about this?", "membershipSeatBillingInfo": "You are currently paying for {n} seats.", "benefitsInclude": "Benefits Include", "currentPlan": "Current Plan", "upgrade": "Upgrade", "downgrade": "Downgrade", "addCardDetails": "Add Your Card Details", "payNow": "Pay Now", "seats": "Seats", "columns": "Columns", "seatsUsed": "{occupied} of {total} seats used", "totalSeats": "Total number of seats", "occupiedSeats": "Total number of occupied seats", "add": "Add", "dataType": "Data type", "active": "Active", "archived": "Archived", "pending": "Pending", "requested": "Requested", "expired": "Expired", "archive": "Archive", "unarchive": "Unarchive", "reassign": "Reassign", "reassignTo": "Reassign to", "unarchived": "Unarchived", "numberOfUsers": "Number of users", "deleteResource": "Archive Resource", "deleteResourceWarning": "Are you sure you want to archive this resource?", "resourceDeleted": "Successfully deleted resource", "id": "ID", "resourceName": "Resource name", "backToList": "Back To List", "resourceUpdated": "Successfully updated resource", "upcomingPaymentNotice": "Subscribed to {qty} seats, next payment of {amount} is scheduled for {date}...", "date": "Date", "tagPage": {"type": "Type", "count": "Count", "placeholderTag": "#tag", "chooseType": "Choose type", "archiveTag": "Archive \"{tagName}\" tag?", "archivedTagNotice": "Archived tags are removed from your active view, but can always be reviewed and all data is stored", "editTag": "Edit tag", "createNewTag": "Create new tag", "deleteConfirm": "Are you sure you want to delete \"{tagName}\" tag?", "deleteTagWillLostData": "Are you sure you want to delete this tag? All data will be lost", "tagAssociatedWithTestRuns": "The \"{tagName}\" tag is currently associated with {count} test runs.", "deleteTagNotice": "You can move this tag to the Archive. Archived tags are removed from the active view, but they can always be viewed and all data is stored", "readyCreateFirstTag": "Ready to create first tag?", "createFirstTagNotice": "Your tags page is currently empty. Let's change that! Click \"Create tag\" and add tags that reflect the specific topics, themes, or categories relevant to your project.", "noPermissionToDo": "You don't have permission to {action} this tag"}, "inviteUser": {"inviteUser": "Invite user", "send": "Send", "noPermissionToDo": "You don't have permission to {action} this invite", "billingDescription": "Organizations are billed <strong>$10/user/month</strong> based on active users for the month. Adding/removing users will impact your monthly subscription."}, "rolePage": {"users": "Users", "readyCreateFirstRole": "Ready to create first role?", "createFirstRoleNotice": "Your role page is currently empty. Let's change that! Click \"Create role\"", "noPermissionToDo": "You don't have permission to {action} this role", "noPermissionForTab": "You don't have permission to access this tab", "noPermissionToCreateOrgRole": "You don't have permission to create organization-level roles", "deleteConfirm": "Are you sure you want to delete {roleName} role{s} {extraRoles}?", "deleteRoleWillLostData": "Are you sure you want to delete this role? All data will be lost", "currentRoleUsersTotal": "{count} users currently have this role", "noProjectRolesYet": "You don't have any Project level roles yet", "noRoleMembers": "You don't have any users with this role yet", "roleNameRequired": "Role name is required", "roleDescriptionRequired": "Role description is required", "roleLevelRequired": "Role level is required", "roleProjectRequired": "Project is required", "selectProject": "Select project", "rolePermissions": "Permissions", "roleProject": "Project", "clickToAddDescription": "Click to add description", "selectRoleLevel": "Select role level", "enterRoleName": "Enter role name", "enterRoleDescription": "Enter role description", "roleHasMembers": "This role has {count} members assigned", "reassignMembersFirst": "Please reassign members before deleting this role", "roleName": "Role name", "roleLevel": "Role level", "organizationLevel": "Organization level", "projectLevel": "Project level", "roleReassignSuccess": "Successfully reassigned Members", "roleReassignMembers": " {count} users currently have this role", "reassignMembersWarning": "You cannot delete this role until these users have had their roles reassigned. No users can be left without a role. Please reassign these users to a different role", "reassignUser": "Reassign", "noAvailableRoles": "No available roles"}, "customFieldPage": {"createCustomField": "Create custom field", "editCustomField": "Edit custom field", "dataType": "Data Type", "dataSource": "Data Source", "chooseDataType": "Choose data type", "deleteConfirm": "Are you sure you want to delete \"{fieldName}\" field?", "willLostData": "All data will be lost", "fieldAssociatedWithTestCases": "This field is currently associated with {count} test cases. This will not effect archived test run/test cases.", "startDate": "Start Date", "endDate": "End Date", "emptyTitle": "You don't have any custom fields yet", "enterLink": "Enter link", "chooseFile": "Choose file", "enterStep": "Enter step", "selectDate": "Select date", "noPermissionToDo": "You don't have permission to {action} this custom field"}, "templatePage": {"deleteConfirm": "Are you sure you want to delete \"{templateName}\" template?", "createCustomTemplate": "Create custom template"}, "sharedStepPage": {"title": "Shared steps", "createSharedStep": "Create shared step", "numberOfSteps": "Number of steps", "referencedBy": "Referenced by", "editSharedStep": "Edit shared step ", "createNewSharedStep": "Create new shared step", "stepDescription": "Step description", "expectedResult": "Expected Result", "addNewStep": "Add new step", "deleteDescription": "Are you sure you want to delete this shared step?", "archiveDescription": "You will no longer be able to add this shared step to newly created test cases, but it will remain in all existing test cases where it is referenced", "unarchiveDescription": "You will be able to add this shared step to newly created test cases, and it will be available in all existing test cases where it is referenced", "deleteConfirm": "Are you sure you want to delete '{name}' shared step?", "archiveConfirm": "Are you sure you want to {action} '{name}' shared step?", "bulkDeleteConfirm": "Are you sure you want to delete selected {count} shared steps?", "bulkArchiveConfirm": "{action} {count} shared steps?", "emptyTitle": "You don't have any shared steps yet", "step": "Step {count}", "noPermissionToDo": "You don't have permission to {action} this shared step"}, "placeHolder": {"searchByName": "Search by name", "searchByTag": "Search by tag", "searchByColumn": "Search by name, id, tag, etc.", "searchByNameId": "Search by name, id", "searchByNameEmail": "Search by name, email, etc.", "createQuickTestCase": "Create quick test case - Enter the test name"}, "settingsPage": {"apiKeys": "API Keys", "createApiKey": "Create API Key", "updateApiKey": "Update API Key", "exportAuditLog": "Export audit log", "noPermissionToDo": "You don't have permission to {action} this {type}", "noActiveProjects": "No active projects available", "permissions": "Permissions", "customDate": "Custom Date", "generateApiKey": "Generate API Key", "regenerateApiKey": "Regenerate API Key", "noExpiration": "No expiration", "neverExpire": "The token will never expire!", "willExpireOn": "The token will expire on {date}", "newApiKey": "New API Key", "copyApiKeyNotice": "Please copy the API key now and securely store it, as you won't be able to access it later", "apiKey": "API Key", "savedApiKeyQuestion": "I've saved my API code", "savedApiKey": "Saved API Key", "lastUsedAt": "Last Used", "deleteConfirm": "Are you sure you want to delete \"{tokenName}\" API Key?", "willLostData": "All data will be lost", "account": "Account", "organizations": "Organizations", "notifications": "Notifications", "authentication": "Authentication", "billing": "Billing", "personalization": "Personalization", "about": "About", "organization": "Organization", "storage": "Storage", "auditLog": "<PERSON>t Log", "steps": {"migratingMessage": "Your files are being\nmigrated...", "analyzingMessage": "Analyzing the size\nof your files...", "migratingWarning": "Your files are too large to\nautomatically migrate,\nplease contact", "errorWarning": "An error occurred during migration.\nPlease contact support.", "migrationRunningMessage": "The migration will run in the background. You can continue working and come back to check the progress anytime.", "migrationCompleteMessage": "Migration completed successfully!"}}, "notifications": {"emailNotifications": "Email Notifications", "newsEmailList": "TestFiesta news and updates email list", "systemNotifications": "System Notifications", "notifyMe": "Notify me", "testCaseAssignedToMe": "When a test case is assigned to me", "newCommentAssignedToMe": "New comments on test cases I am assigned to", "testRunAssignedToMe": "The test runs I was assigned to", "newCommentDefects": "New comments on defects I created", "updateNotifications": "Update Notifications", "updatingNotifications": "Updating Notifications", "notificationsUpdated": "Notifications are updated successfully!", "settingNotifications": "{NotificationType} notifications were updated", "inPlatformOnly": "In the platform only", "inPlatformEmailPerDay": "In the platform + One email per day", "inPlatformEmailImmediate": "In the platform + Immediate email"}, "account": {"timeZone": "Timezone", "deleteAccount": "Delete Account", "deleteAccountNotice": "Once you delete your account, there is no going back. Please be certain.", "deleteAccountConfirm": "Are you sure you want to delete the account?", "willDeleteAccount": "This will delete your account.", "willLostData": "All data will be lost.", "mustConfirmOperation": "To perform this action, you must confirm the operation with your current password", "updatingProfile": "Updating Profile", "accountHasBeenDeleted": "Account has been deleted successfully!", "deletingAccount": "Deleting Account"}, "organization": {"createOrganization": "Create Organization", "startFreeTrial": "Start free trial", "freeTrial": "Free trial", "afterTrial": "After Trial:", "days": "days", "perUserPerMonth": "/user/month", "skipForNow": "Skip for now", "createOrganizationTitle": "Create your organization", "createOrganizationDescription": "Organizations allow you to invite others to collaborate in a shared workspace", "updateOrganization": "Update Organization", "updatingOrganization": "Updating Organization", "organizationUpdated": "Organization has been updated successfully!", "deleteOrganization": "Delete Organization", "leaveOrganization": "Leave organization", "organizationLeft": "You have left the organization successfully!", "leaveOrganizationConfirm": "Are you sure want to leave the organization?", "deletingOrganization": "Deleting Organization", "deleteOrganizationNotice": "Once you delete your organization, there is no going back. Please be certain.", "organizationDeleted": "Organization has been deleted successfully!", "readyCreateFirstOrganization": "Ready to create first organization?", "organizationAreWorkspace": "Organizations are the workspace where you and your team-mates as well can work collaboratively", "backToOrganizations": "Back to Organizations", "deleteOrganizationConfirm": "Are you sure you want to delete the organization?", "willDeleteOrganization": "This will delete your organization.", "willLostData": "All data will be lost.", "mustConfirmOperation": "To perform this action, you must confirm the operation with your current password", "organization_name": "Organization name", "owner": "Owner", "join_date": "Join date"}, "about": {"companyIntro": "Testfiesta LLC. specializes in providing cutting-edge software solutions tailored for QA engineers, enhancing the testing experience through innovative tools and technologies. ", "contactUs": "Contact Us", "lookForward": "We look forward to your feedback and suggestions at", "legalDocuments": "Legal documents", "privacyPolicy": "Privacy policy", "termsOfUse": "Terms of use"}, "storage": {"name": "Name", "enterName": "Enter name", "type": "Type", "storageConfiguration": "Storage configuration", "enableCloudStorage": "Enable cloud storage configuration", "storageInfo": "Activate this option to connect and configure your own cloud storage. This feature allows you to integrate your preferred cloud storage service, ensuring that your data is securely stored and easily accessible", "selectConfig": "Select Storage Configuration", "credentials": {"region": "Region", "bucket": "Bucket", "SASToken": "SAS token", "account": "Account", "endpoint": "Endpoint", "container": "Container", "useSSL": "User SSL", "useShare": "User Share", "project_id": "Project ID", "client_email": "Client email", "access_key_id": "Access key ID", "secret_access_key": "Secret access key", "client_id": "Client ID", "token_uri": "Token URI", "private_key": "Private Key", "private_key_id": "Private key ID", "type": "Type"}, "enterCredentials": {"region": "Enter region", "bucket": "Enter bucket", "SASToken": "Enter SAS token", "account": "Enter Account", "endpoint": "Enter Endpoint", "container": "Enter Container", "project_id": "Enter project id", "client_email": "Enter client email", "access_key_id": "Enter access key ID", "secret_access_key": "Enter Secret access key", "private_key": "Private Key", "private_key_id": "Private key ID", "client_id": "Enter client id", "token_uri": "Enter token uri", "type": "Enter type"}}, "dataColors": {"color": "Color", "restoreDefault": "Restore default", "primaryChartColors": "Primary chart colors", "statusColors": "Status colors", "priorityColors": "Priority colors", "typeColors": "Type colors", "hex": "HEX", "updatingDataColors": "Updating Data Colors", "colorsUpdated": "Data colors are updated successfully!", "updateColors": "Update Colors", "addStatus": "Add Status", "aliases": "Aliases", "aliasesDescription": "Enter a list of aliases for API automation calls related to this status.", "addPriority": "Add Priority", "category": "Category", "name": "Status Name", "isSuccess": "Is considered a success", "isSuccessTooltip": "Will be marked as a successful test for statistics and reporting purposes.", "isFailure": "Is considered a failure", "isFailureTooltip": "Will be marked as a failed test for statistics and reporting purposes and will trigger a Defect creation dialogue box.", "isComplete": "Is considered complete", "isCompleteTooltip": "Will be marked as complete for statistics and reporting purposes.", "assignDefault": "Assign as default", "colorUpdatedNewMessage": "The { name } '{ status }' {personalizationType} color has been {add}."}, "authentication": {"twoFactorAuthentication": "Two-factor authentication", "enableTwoFactorAuthentication": "Enable two-factor authentication", "chooseAuthenticationMethod": "Choose authentication method", "howWouldYouReceiveAuthenticationCode": "How would you like to receive your authentication codes?", "authenticationApp": "Authentication app", "willSendAuthenticationToMobile": "We will send an authentication code to your mobile device", "useAuthenticationAppToGenerateCode": "Use an authentication app to generate authentication code (e.g. Google Authenticator)", "enterPhoneNumber": "Enter your phone number", "willSendConfirmationCodeToPhoneNumbmer": "We will send a confirmation code to the phone number you specified", "enterVerificationCode": "Enter verification code", "sentVerificationCodeToPhoneNumber": "We sent the verification code to {phoneNumber}. Enter it in the field below", "verificationCode": "Verification Code", "step": "Step {currentStep}/{totalSteps}", "recoveryCodes": "Recovery codes", "pleaseKeepTheseCodes": "Please keep these codes in a safe place.", "codesAreUsedToRestore": "These codes are used to restore access to the application if you cannot obtain authentication codes.", "onceAuthenticationIsComplete": "Once the two-factor authentication is complete, you will not be able to retrieve these codes.", "savedRecoveryCode": "Saved Recovery Code", "twoFactorAuthenticationEnabled": "Two-factor authentication enabled", "scanQRCode": "Scan QR Code", "scanQRCodeNotice": "Scan the QR code with your authentication application. After scanning, a 6-digit code will appear on the screen and must be entered in the field below", "enterSixDigitCode": "Enter 6-digit code generated by your authentication app", "verifyingCode": "Verifying Code", "invalidCode": "Invalid Otp Code", "singleSignOn": "Single sign-on (SSO)", "enableSingleSignOn": "Enable single sign-on (SSO)", "sso": {"protocol": "Select Your Authentication Protocol", "protocolHint": "Currently supporting OpenID Connect only", "clientId": "Client ID", "clientSecret": "Client Secret", "issuerUrl": "IDP Issuer URL", "issuerUrlHint": "The base URL of your identity provider (e.g., https://accounts.google.com)", "callbackUrl": "Callback URL (Redirect URI)", "whitelistDomains": "Whitelist Domains", "whitelistDomainsPlaceholder": "Please enter one email domain per line", "ssoSetupSuccess": "SSO setup has been successfully completed", "deleteSuccess": "SSO configuration deleted successfully", "groupMappingInfo": "Group mapping allows users to be automatically assigned to a role based on their group membership in the identity provider. This is a convenient way to manage user roles and permissions without manual intervention.", "defaultRole": "Default Role", "onlyAllowInvited": "Only allow invited accounts", "useGroupMapping": "Use Group Mapping", "groupMappings": "Group to Role Mappings", "groupMappingsInfo": "Map your SSO groups to TestFiesta roles. Users will be assigned roles based on their SSO group membership.", "mapping": "Mapping", "groupName": "Group Name", "role": "Role", "addMapping": "Add Group Mapping", "save": "Save Configuration", "delete": "Delete Configuration", "cancel": "Cancel", "accessControl": "Access Control"}}, "billing": {"card": "Card", "subscription": "Subscription", "billingHistory": "Billing history", "yourCurrentPlan": "Your current plan", "upgradePlan": "Upgrade Plan", "downgradePlan": "Downgrade Plan", "nextPayment": "Next payment estimate", "nextPaymentDate": "Next payment date", "currentUserCount": "Current user count", "paymentMethod": "Payment method", "addPaymentMethod": "Add a payment method", "amount": "Amount", "selectedPlan": "Selected Plan", "cardNumber": "Card number", "expirationDate": "Expiration date", "securityCode": "Security code", "billingAddress": "Billing address", "updatedPlan": "Your plan has been updated successfully", "loadingCurrentSubscription": "Loading Current Subscription", "updatingPlan": "Updating Plan", "availablePlans": "Available Plans", "loadingSubscriptionHistory": "Loading Subscription History", "perMonth": "/month", "perUserMonth": "per user/month", "unknownPlan": "Unknown Plan", "setAsPrimary": "Set as primary", "deletePaymentMethodConfirm": "Are you sure you want to delete your payment method {name}?", "cancelSubscriptionConfirm": "Are you sure you want to cancel your subscription?", "deletePaymentMethodDescription": "This will delete your payment method. All data will be lost.", "cancelSubscriptionDescription": "This will end your subscription effective at the end of today. Your primary payment method will be charged for your user count during the current billing cycle, prorated for the number of days that have passed since your last bill.", "basic": "BASIC"}, "common": {"gotIt": "Got it", "copy": "Copy", "createdBy": "Created By", "createdAt": "Created At", "continue": "Continue", "phoneNumber": "Phone Number", "phoneNumberExample": "Example: ******-567-7890, +****************, (*************", "update": "Update", "success": "Success", "days": "{days} days"}, "address": {"country": "Country", "addressLine": "Address line {index}", "city": "City", "postalCode": "Postal code"}, "defect": {"overview": "Overview", "creator": "Creator", "assignedTo": "Assigned to", "showLess": "Show less", "showMore": "Show more", "updatingData": "Updating data...", "editDefect": "Edit Defect", "sendUpdate": "Send Update", "fetchSuccess": "Defect fetched successfully", "fetchError": "Error: Defect fetch failed. Please try again.", "success": "Defect updated successfully", "error": "Error: Defect update failed. Please try again.", "active": "Active", "closed": "Closed", "state": "Status", "issueType": "Issue Type", "attachments": "Attachments", "parentTicket": "Parent Ticket", "fixVersions": "Fix Versions", "restrictTo": "Restrict To", "assignee": "Assignee", "integration": "Integration", "summary": "Summary", "execution": "Execution", "title": "Title", "repository": "Repository", "addNewDefectDialog": {"title": "Add a new defect?", "content": "You can create a new defect for this test case or select it from your integration.", "addDefect": "Add defect", "skipBtn": "<PERSON><PERSON>"}, "createNewDefectDialog": {"title": "Create a new defect", "addDefect": "Add a defect", "attachmentsLabel": "Add attachments", "uploadInstructions": "Upload files to add additional context or support for the defect", "integrationLabel": "Integration", "selectIntegration": "Select Integration", "itemLabel": "Choose {item}", "typeLabel": "Issue Type", "selectType": "Select Issue Type", "summary": "Summary", "typesummary": "Type Summary", "priorityLabel": "Priority", "libraryLabel": "{service} Library", "searchPlaceholder": "Search by key or by title", "noResults": "Search did not find anything", "browseFiles": "Browse files", "jiraOrgLabel": "Jira Organization", "selectJiraOrg": "Select Jira Organization", "noIntegrations": "No integrations found", "addIntegrationMessage": "Add integration to create a defect", "addIntegration": "Add integration", "nameLabel": "Title", "enterName": "Enter title of the defect", "integrationSelectError": "Please select an integration first", "itemSelectError": "Please select a {item} first", "typeSelectError": "Please select a type first", "prioritySelectError": "Please select a priority first", "jiraOrgSelectError": "Please select a Jira organization first", "nameError": "Please enter a {item} for the defect", "descriptionError": "Please enter a description for the defect", "defectSelectError": "Please select a defect first", "descriptionLabel": "Description", "enterDescription": "Enter description of the defect", "projectNotFound": "Project not found", "defectCreateFailed": "Failed to create defect", "failedToFetchDefects": "Failed to fetch defects", "failedToFetchIssueTypes": "Failed to fetch issue types", "failedToFetchPriorities": "Failed to fetch priorities", "failedToFetchIntegrations": "Failed to fetch integrations", "selectExistingDefect": "Select existing defect", "selectDefect": "Select defect", "tagsLabel": "Labels", "selectTags": "Select labels for defect", "failedToFetchTags": "Failed to fetch tags", "fieldRequired": "This field is required", "enterValue": "Enter value", "enterNumber": "Enter number", "selectValue": "Select value", "selectValues": "Select values", "selectDate": "Select date", "enterText": "Enter text", "linkTitle": "Select an existing defect", "failedToUploadAttachments": "Failed to upload attachments", "issueKeyPlaceholder": "Enter issue key (e.g., MDP-1)", "boardIdLabel": "Board ID", "selectBoard": "Select a board to fetch sprints", "selectSprint": "Select sprint", "failedToFetchSprints": "Failed to fetch sprints", "failedToFetchAssignees": "Failed to fetch assignees", "failedToFetchReporters": "Failed to fetch reporters", "requiredFieldsError": "Please fill in all required fields", "uploadingData": "Uploading Data to {service}...", "projectIntegration": "Project Integration", "change": "Change Integration", "customDefectIds": "Custom Defect IDs", "enterCustomDefectIds": "Type defect ID and press Enter", "customDefectIdsHelp": "Add multiple defect IDs from your custom integration. Type each ID and press Enter to add it.", "customDefectIdsRequired": "Please enter custom defect IDs", "noIntegrationsForService": "No integrations available for {service}", "customIntegrationLabel": "Custom Integrations", "customIntegrationDescription": "Choose an integration to navigate to external source to create defect", "openExternalSource": "Open External Source", "noAddUrlConfigured": "No add URL configured for this integration", "selectCustomIntegration": "Select custom integration", "customIntegrationOpened": "External defect creation page opened"}, "viewDefect": {"serviceId": "{service} ID", "integration": "Integration", "serviceProject": "{service} {entity}", "testExecution": "Test Execution", "testRun": "Test Run", "labels": "Labels", "attachments": "Attachments", "comments": "Comments", "createdAt": "Created At", "updatedAt": "Updated At", "createdBy": "Created By", "updatedBy": "Updated By"}}, "testruns": {"completeRun": "Are you sure you want to complete run?", "noPermissionToDo": "You don't have permission to {action} this test run", "testCases": "Test cases", "selectPriority": "Select priority", "selectTags": "Select tags", "confirm": {"deleteFolder": "Are you sure you want to delete this folder?", "confirmDeleteTestRun": "Are you sure you want to delete this test run?"}, "duplicateApplyConfig": "Duplicate and apply configuration", "changeDueDate": "Change due date", "empty_state": {"title": "Begin Testing Now", "description_part1": "Welcome to Test Runs. Start organizing and executing your tests. Test", "description_part2": "Runs are comprised of Test Cases. We recommend you create some Test ", "description_part3": "Cases before creating a new Test Run."}, "create_testrun": {"back_to_testrun": "Back to Test runs", "back_to_create_testrun": "Back to Create test run", "testRunName": "Test run name", "title": "Create test run", "configurations": "Configurations", "egOSorBrowsers": "E.g.: OS or Web Browsers", "egOSorBrowsersDescription": "E.g.: Windows 11, IOS 16, or Firefox", "testrun_name": "Name", "description": "Description", "rerunTestRun": "Rerun test run", "milestone": "Milestone", "priority": "Priority", "status": "Status", "tags": "Tags", "tag": "Tags", "assign": "Assigned to", "actions": "Actions", "duplicate_testrun": "Duplicate test run", "add_testcase": "Add test cases", "remove_testcase": "Remove test cases", "close_dialog": {"title": "Are you sure you want to go back without creating your test run?", "content": "All progress will be lost. You would need to start creation once again from scratch"}}, "duplicate_testrun": {"success": "Your test run has been successfully duplicated", "error": "Error: Test run duplication unsuccessful. Please try again."}, "edit_testrun": {"back_to_testrun": "Back to Test runs", "title": "Are you sure you want to go back without saving the changes?", "content": "All progress will be lost. You will need to start the editing process again from the beginning", "btn_label": "Confirm", "not_found": "Test run not found"}, "milestone_dialog": {"title": "Add test runs to milestones", "link": "Link test runs with milestones", "content": "Select milestones", "success": "Test runs has been attached to milestone successfully", "failed": "Failed to attach test runs to the milestone.", "btn_label": "Add", "no_selected_milestones": "No milestones selected. Please choose at least one milestone to link.", "no_selected_plans": "No test plans selected. Please choose at least one test plan to link."}, "testplan_dialog": {"title": "Add test runs to test plans", "content": "Select test plans", "success": "Test runs has been attached to test plan successfully", "failed": "Failed to attach test runs to the test plan.", "btn_label": "Add"}, "delete_dialog": {"title": "Are you sure you want to delete selected test runs?", "content": "All data will be lost.", "content_part2": "You can move test runs to the Archive.Archived test runs are removed from the active view, but they can always be viewed and all data is stored", "btn_label": "Delete"}, "archive_dialog": {"title": "Are you sure you want to archive ", "title_multi": "Are you sure you want to archive selected test runs?", "content": "Archived test runs are removed from your active view, but can always be reviewed and all data is stored", "btn_label": "Archive"}, "unarchive_dialog": {"title": "Are you sure you want to unarchive ", "title_multi": "Are you sure you want to unarchive selected test runs?", "content": "Unarchiving these test runs will restore them to an active state", "btn_label": "Unarchive"}, "test_case": {"add_test_cases_to_test_runs": "Add test cases to test runs", "export_test_cases": "Export test cases", "addcase": {"title": "Add these test cases to test run", "content": "These test cases will be added to the test run"}, "removecase": {"title": "Remove these test cases to test run", "content": "These test cases will be removed from the test run"}, "bulk_remove": {"title": "Are you sure you want to remove {count} test cases from the project?", "content": " The selected test cases are currently being used in other test runs. This action will not effect existing test runs, but will remove these test cases from being available in future test runs"}}, "addtestcases": "Add test cases", "archive": "Archive", "addToMilestone": "Add to milestones", "linkToMilestone": "Link to milestones", "addToTestPlans": "Add to test plans", "addToTestRuns": "Add to test runs", "linkToTestPlans": "Link to test plans", "unarchive": "Unarchive", "delete": "Delete", "createTestRun": "Create test run", "status": "Status:", "test_cases": "Test cases", "all": "All", "linked": "Linked", "unlinked": "Unlinked", "toPlans": "To plans", "ongoing": "Ongoing", "active": "Active", "archived": "Archived", "selected": "Selected", "progress": "Progress", "testcase_from_repo": "Add test case from the Repository", "manageTestCaseEasily": "Manage Your Test Cases Easily", "startCreateTestCase": "We recommend you create some Test Cases before creating", "startCreateTestCase_part2": "a new Test Run.", "rerun": "<PERSON><PERSON>", "complete": "Complete"}, "milestone": {"title": "Milestones", "startdate": "Start date", "choose": "Choose milestones", "create": "Create milestone", "noPermissionToDo": "You don't have permission to {action} this milestone", "empty_state": {"title": "Ready to Set Milestones?", "description_part1": "Your milestone page is curently empty. Let's change that!", "description_part2": "Click 'Create Milestone' and start mapping out your journey toward", "description_part3": "success!"}, "revert_changes": {"title": "Are you sure you want to go back without saving?", "description": "All progress will be lost.You would need to restart the update process from scratch"}, "create_milestone": {"back_to_milestones": "Back to Milestones", "title": "Milestone info", "milestone_name": "Name", "description": "Description", "milestone": "Milestone", "startDate": "Start date", "startDateError": "Start date can not be after due date", "dueDate": "Due date", "status": "Status", "actions": "Actions", "action": "Action", "addTestActivities": "Add Test Activities", "duplicate_testrun": "Duplicate test run", "addTestRuns": "Add Test Runs", "addTestPlans": "Add Test Plans", "close_dialog": {"cancel_button": "Cancel", "continue_button": "Continue", "save_button": "Save", "confirm_button": "Confirm", "title": "Are you sure you want to go back without creating milestone?", "content": "All progress will be lost. You would need to start creation once again from scratch"}, "dueDateError": "Due date can not be before start date"}, "edit_milestone": {"back_to_milestones": "Back to Milestones", "title": "Milestone info", "milestone_name": "Name", "description": "Description", "milestone": "Milestone", "startdate": "Start Date", "duedate": "Due Date", "status": "Status", "actions": "Actions", "duplicate_testrun": "Duplicate test run", "add_testruns": "Add test runs", "close_dialog": {"cancel_button": "Cancel", "continue_button": "Continue", "save_button": "Save", "confirm_button": "Confirm", "title": "Are you sure you want to go back without saving these changes?", "content": "All progress will be lost. You would need to start creation once again from scratch"}}, "delete_dialog": {"title": "Are you sure you want to delete this milestone?", "content": "All data will be lost.\nRather than Delete this milestone you may change the status to 'Closed' in the edit menu to remove it from the Open tab without losing any data", "content_part2": "All data will be lost", "btn_label": "Delete milestone"}, "close_dialog": {"title": "Are you sure you want to archive this milestone?", "content_part1": "Are you sure you want to archive", "content_part2": "Milestone? Not all planned test runs have been completed yet", "btn_label": "Archive milestone", "dont_show_again": "Don't show again"}, "close_delete_dialog": {"title": "Are you sure you want to close this milestone?", "content_part1": "All data will be lost.", "content_part2": "Rather than Delete this milestone you may change the status to 'Closed' in the edit menu to remove it from the Open tab without losing any data", "btn_label": "Delete milestone", "dont_show_again": "Don't show again"}, "close_testrun_view": {"title": "Are you sure you want to close without creating milestone?", "content_part1": "All progress will be lost. You would need to start creation once again from scratch", "btn_label": "Close milestone", "dont_show_again": "Don't show again"}, "close_remove_dialog": {"title": "Are you sure you want to remove these {name} from milestone", "content_part1": "These {name} will no longer appear in this milestone", "btn_label": "Remove", "dont_show_again": "Don't show again"}, "close_add_dialog": {"title": "Add these {name} from milestone", "content_part1": "These {name} will be added to the milestone, you can edit the list later", "btn_label": "Remove", "dont_show_again": "Don't show again"}, "reopen_dialog": {"title": "Are you sure you want to unarchive this milestone?", "content": "Unarchiving restore them to an active state", "btn_label": "Unarchive"}, "archive": "Archive", "delete": "Delete", "createMilestone": "Create milestone", "create_milestone_success": "Created milestone successfully", "update_milestone_success": "Updated milestone successfully", "addTestPlan": {"testRuns": "test runs"}}, "account_menu": {"personal_account": "Personal account", "switch_account": "Switch orgs", "switch_workspace": "Switch workspace", "add_workspace": "Add workspace", "sign_out": "Sign out...", "add_account": "Add account"}, "deleteFolderTitle": "Are you sure you want to delete '{name}' folder?", "deleteFolderContent": "Are you sure you want to delete this folder? This will delete all contents currently inside the folder as well", "deleteCaseContent": "Are you sure you want to delete this case? This will delete all contents currently inside the case as well", "test_folder": {"create_failed": "folder create failed", "update_failed": "folder update failed", "delete_failed": "folder delete failed", "refresh_failed": "folder refresh failed", "failed_to_fetch_children": "Failed to fetch children for folder {folderUid}"}, "plans": {"title": "Test plans", "titleWithCount": "{count} Test plans", "title_single": "Test plan", "createTestPlan": "Create test plan", "addToMilestone": "Add to milestone", "createTestRuns": "Create new test runs", "createTestRun": "Create new test run", "archived": "Archived", "active": "Active", "archive": "Archive", "unarchive": "Unarchive", "deletedSuccessToast": "Deleted 2 test plans", "createdSuccessToast": "Created Functionality Test Plan test plan ", "noPermissionToDo": "You don't have permission to {action} this test plan", "beginTestNewTitle": "Begin Testing Now", "beginTestNewDescripiton": "We recommend you create some Test Runs before creating a new Test Plan.", "addRuns": {"description": {"part1": "We recommend you create some Test Runs before creating", "part2": "a new Test Plan."}}, "dontShowAgain": "Don't show again", "bulkDeleteConfirm": {"title": "Are you sure you want to delete selected test plans?"}, "unArchiveConfirmation": {"title": "Are you sure you want to unarchive selected test runs?", "description": "Unarchiving these test runs will restore them to an active state"}, "placeholder": {"title": "Start Planning Your Test Runs", "description": "Welcome to Test Plans. Test Plans are comprised of Test Cases inside of Test Runs. We recommend you create some Test Cases and Test Runs before creating a new Test Plan."}, "list": {"testRuns": "Test runs", "testCases": "Test cases"}, "archived_empty_state": {"description": {"part1": "To maintain organized workflows, consider archiving completed test plans", "part2": "for easy access when needed in the future."}, "title": "No Archived Test Plans Available"}, "create": {"backToPlans": "Back to Test plans", "backToCreatePlans": "Back to Create test plan", "duplicate": "Duplicate test plan", "backToRerunPlan": "Back to Rerun test plan", "rerun": "Rerun test plan", "addTestRuns": "Add test runs", "discussConfirmation": {"title": "Are you sure you want to go back without creating your test plan?", "description": "All progress will be lost. You would need to start creation once again from scratch"}, "testRuns": {"tableHeader": {"testCases": "Test cases"}, "addConfiguration": "Add configuration", "addConfigurations": "Add configurations", "addGroup": "Add group"}}, "edit": {"new": "New", "inProgress": "In progress", "passed": "Passed", "rerun": "<PERSON><PERSON>", "failed": "Failed", "milestone": "Milestone", "priority": "Priority", "tags": "Tags", "discussConfirmation": {"title": "Are you sure you want to go back without editing your test plan?", "description": "All progress will be lost."}}, "rerun": {"title": "Select test run statuses", "label": "<PERSON><PERSON>"}, "duplicate": {"title": "Duplicate test plan", "success": "Plan successfully duplicated", "error": "Plan duplication unsuccessful. Please try again.", "rerun": {"title": "Add test runs", "back": "Back to Duplicate test plan"}, "selectPlan": "Please select a test plan to duplicate", "discussConfirmation": {"title": "Are you sure you want to go back without duplicating your test plan?", "description": "All progress will be lost. You would need to start creation once again from scratch"}}, "editTestPlan": {"backToTestPlan": "Back to Test plans", "title": "Are you sure you want to go back without creating your test plan?", "content": "All progress will be lost. You would need to start creation once again from scratch", "btnLabel": "Confirm"}, "deleteDialog": {"title": "Are you sure you want to delete selected test plans?", "content": "All data will be lost.", "contentPart2": "You can move test plans to the Archive. Archived test plans are removed from the active view, but they can always be viewed and all data is stored", "btnLabel": "Delete"}, "archiveDialog": {"title": "Are you sure you want to archive ", "titleMulti": "Are you sure you want to archive selected test plans?", "content": "Archived test plans are removed from your active view, but can always be reviewed and all data is stored", "btnLabel": "Archive"}, "unArchiveDialog": {"title": "Are you sure you want to unarchive ", "titleMulti": "Are you sure you want to unarchive selected test plans?", "content": "Unarchiving these test plans will restore them to an active state", "btnLabel": "Unarchive"}, "confirm": {"confirmDeleteTestPlan": "Are you sure you want to delete this test plan?"}, "milestone": {"addTestPlansToMilestones": "Add test plans to milestones", "addTestPlan": "Add", "toMilestones": "to milestones", "selectMilestones": "Select milestones"}, "delete1TestPlan": "Delete 1 Test plan", "deleteTestPlans": "Delete {count} Test plans", "testRuns": {"testCases": " test case(s)", "testRuns": " test run(s)"}}, "auditLog": {"title": "<PERSON>t Log", "date": "Date", "projectName": "Project Name", "entityType": "Entity Type", "entityId": "Entity ID", "entityName": "Entity Name", "action": "Action", "actor": "Actor", "mode": "Mode", "dateRange": "Date Range", "actions": "Actions", "exportCSV": "Export to CSV", "exportXLSX": "Export to XLSX", "today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 Days", "last7Days": "Last 7 Days", "revert_dialog": {"title": "Are you sure you want to recover {name} entity?", "recommend": "You're about to restore this entity. Confirm your action to bring it back to its original location"}}, "progress": "Progress", "browsers": "Browsers", "chrome": "Chrome", "opera": "Opera", "safari": "Safari", "firefox": "Firefox", "edge": "Edge", "searchByName": "Search by name", "ongoing": "Ongoing", "pagination": {"rowsPerPage": "Rows per page", "previous": "Previous", "next": "Next"}, "templatesPage": {"generalInfoTooltip": "These fields will appear in the Overview tab of the test case.", "detailsTooltip": "These fields will appear in the Details tab of the test case.", "field": "Field", "templates": "Templates", "template": "Template", "noCustomFields": "No custom fields yet.", "createTemplate": "Create template", "editTemplate": "Edit template", "template_name": "Template name", "add_field": "Add field", "add_custom_field": "Add custom field", "add_exist_fields": "Add existing field", "add_new_custom_field": "Add new custom field", "custom_field": "Custom field {index}", "data_type": "Data type", "creator": "Creator", "provide_default_value": "Provide default value", "default_value": "Default value", "enter_default_value": "Enter default value", "assign_as_default": "Assign as <PERSON><PERSON><PERSON>", "emptyTitle": "You don't have any templates yet", "mandatory": "Mandatory", "reassign": "Reassign", "default": "<PERSON><PERSON><PERSON>", "generalInfoDescription": "This information will appear in the Overview tab of the test case.", "detailsDescription": "This information will appear in the Details tab of the test case.", "selectField": "Select field", "makeDefault": "Make default", "reassignDefaultTitle": "Which template would you like to reassign as the default?", "reassignPermission": "You don't have permission to reassign this template", "makeDefaultPermission": "You don't have permission to make this template default", "noPermissionToDo": "You don't have permission to {action} this template", "rule": "Rule", "addRule": "Add rule", "appliesToStatus": "Applies to status", "linkDefect": "Create defect", "manageTags": "Manage tags", "testCase": "testCase", "testResult": "testResult", "included": "Included", "excluded": "Excluded", "isConsideredSuccess": "Is considered a success", "isConsideredFailure": "Is considered a failure", "appliesToStatusDescription": "Choose the status that will require user to complete the configured fields before the status can be applied.", "defaultTemplateDescription": "This will remove {name} as the Default Template.", "statusDisabledDescription": "You've already applied a rule to this status. Remove or edit the existing rule to reuse it.", "failureStatusRestriction": "Selecting a failure status restricts additional selections to failure statuses only."}, "toast": {"deleteSuccess": "{item} deleted successfully!", "deleteError": "Failed to delete {item}.", "closeSuccess": "{item} closed successfully!", "reopenSuccess": "{item} reopened successfully!", "closeError": "Failed to close {item}.", "reopenError": "Failed to reopen {item}.", "archiveSuccess": "{item} archived successfully!", "archiveError": "Failed to archive {item}.", "unarchiveSuccess": "{item} unarchived successfully!", "unarchiveError": "Failed to unarchive {item}.", "updateSuccess": "{item} updated successfully!", "updateError": "Failed to update {item}.", "duplicateNameError": "A {item} with the same name already exists", "downloadError": "Failed to download {item}.", "createSuccess": "{item} created successfully!", "revertError": "Failed to revert {item}.", "revertSuccess": "Recovered {item} successfully!", "exportError": "Failed to export {item}.", "createError": "Failed to create {item}.", "fetchError": "Failed to load {item}", "partialError": "Some test cases failed to update", "noActivity": "No activity", "inviteSuccess": "Invitation sent successfully", "genericError": "An error occurred: {message}", "inviteError": "Failed to send invitation: {error}", "invitationUpdate": "Invitation {action} successfully", "addError": "Failed to add {item}", "removeError": "Failed to remove {item}", "linkedError": "Failed to add {item}", "unlinkedError": "Failed to remove {item}", "addSuccess": "Added {item} successfully", "addConfigSuccess": "Added {item} configuration", "removeConfigSuccess": "Removed {item} configuration", "addRemoveConfigSuccess": "Added {added} and Removed {removed} configuration", "linkedSuccess": "Linked {item} successfully", "unlinkedSuccess": "Unlinked {item} successfully", "linkedWithSuccess": "Linked {item1} with {item2}", "unlinkedWithSuccess": "Unlinked {item1} with {item2}", "linkedWithError": "Failed to link {item1} with {item2}", "unlinkedWithError": "Failed to unlink {item1} with {item2}", "passwordResetEmailSent": "Password reset email sent", "passwordResetError": "Error sending password reset email: {error}", "resetPasswordSuccess": "Password reset successfully", "resetPasswordError": "Error resetting password: {error}", "duplicateSuccess": "{item} duplicated successfully!", "duplicateError": "Failed to duplicate {item}.", "noOtherProjectsAvailable": "No other projects available", "planRunsRerunError": "No test runs available to rerun for this plan", "resendSuccess": "Invite resent successfully", "resendError": "Failed to resend invite", "permissionError": "You don't have permission to {action} {resource}.", "noTitleMapping": "Please map the Title field to continue", "processingError": "Error processing CSV data: {error}", "copySuccess": "Copied to clipboard", "copyError": "Failed to copy to clipboard", "failedToUpdateStepStatus": "Failed to update step status", "stepStatusUpdated": "Step status updated successfully", "connectAnAccount": "Connect an account", "storageLimit": "You've reached your TestFiesta cloud storage limit.", "storageCapacity": "in BYOS to increase your storage capacity."}, "notFoundPage": {"title": "Oops! You've stumbled into the wild world of 404-land", "description": "But don't worry, let's get back on track and keep the fiesta going!", "loginSuggestion": "Maybe you need to log in?", "backButton": "Go back"}, "maintenance": {"title": "Your database is currently under maintenance", "beforeEmail": "Please contact", "afterEmail": "if this persists for more than a few minutes."}, "setup": {"databaseCreation": "Your database is being created, this may take a few minutes.", "redirectNotice": "Once it is ready you will automatically be redirected from this page"}, "close": "Close", "re-open": "Re Open", "declineReasons": {"alreadyPartOfOrganization": "Already part of another organization", "notInterestedInJoining": "Not interested in joining at this time", "preferDifferentPlatform": "Prefer to use a different platform", "other": "Other (please specify)", "specifyReason": "Specify your reason", "specifyReasonPlaceholder": "Enter your reason here"}, "unnamedFolder": "Unnamed Folder", "changePlan": "Change plan", "saveAsTemplate": "Save as template", "selectExistingCustomField": "Select existing custom field", "noCustomFieldsFound": "No custom fields found", "fillAllFields": "Please fill all the fields", "noFileSelected": "No file selected", "newFolder": "New Folder", "selectCSVFile": "Please select a CSV file", "templateNameRequired": "Template name is required", "aiAssist": {"aiCreate": "AI Create", "improveWithAi": "Improve with AI", "suggestedPrompts": "Suggested prompts", "fieldEmpty": "{input} field shouldn't be empty.", "suggestedImprovments": "Suggested improvements", "whatDoYouWantToImprove": "What do you want to improve?", "caseImprovementFailed": "Failed to improve case due to insufficient details provided.", "title": "Assist", "reject": "Reject", "accept": "Accept", "typeYourRequirements": "Type your requirements", "InitialMessage": "How would you like me to refine or improve your field value?", "Send": "Send", "analyzingRequirements": "Analyzing your requirements..."}, "fileEncodingInfo": "The file must be UTF-8 encoded, with a maximum size of {size} MB.", "dragAndDrop": "Drag&Drop", "browseFiles": "Browse files", "csvSeparator": "CSV separator", "firstRow": "First row", "tagsSeparator": "Tags separator", "rowFormat": "Row format", "singleRowTestCase": "Each row is a single test case", "multiRowTestCase": "Test case can span across multiple rows", "identifiedFields": "Identified {name} fields", "testFiestaFields": "TestFiesta fields", "back": "Back", "requirements": "Requirements", "TestCaseAssignment": "All test cases are successfully assigned", "TestCaseAssignmentSubText": "You can check all imported test cases in their respective folders", "continueWithGoogle": "Continue with Google", "step": "Step {index}", "childStep": "Step {index}.{childIndex}", "stepDefaultTitle": "Step {index}", "childStepDefaultTitle": "Step {index}.{childIndex}", "backToLogin": "Back to Login", "invalidOrgHandle": "Invalid organization handle", "newStatusTooltip": "Set this field as a default status, only one field may be selected for each default status.", "newPriorityTooltip": "Set this field as a default priority, only one field may be selected for each default priority.", "expectedResultByStep": "Expected result by step", "includeCustomFields": "Include custom fields", "bulkEdit": "Bulk edit", "testFolder": "Test folder", "chooseFolder": "Choose folder", "newStatusSuccess": "Set this status as the default status when a user select \"{name}\" ?", "currentDefaultStatusText": "Current default status", "currentDefaultStatus": "{name}", "reassignDefaultTooltipStart": "This status is set as the default. You can't delete it until you", "reassignDefaultTooltipEnd": "another status as the default.", "reassignLinkText": "reassign", "confirmSuccessStatus": "Set this status as the default status when a user selects 'Mark all as Passed'?", "confirmFailureStatus": "Set this status as the default status when a user selects 'Mark all as Failed'?", "confirmCompletedStatus": "Set this status as the default status when a user selects 'Mark all as Completed'?", "setDefaultStatus": "Set this status as the default status for new entities when they are first created?", "willMarkAsSuccess": "Will mark as success:", "willMarkAsFailure": "Will mark as failure:", "willMarkAsCompleted": "Will mark as completed:", "currentDefaultWillBeReassigned": "Current default status will be reassigned:", "successLabel": "Success", "failureLabel": "Failed", "completedLabel": "Completed", "defaultLabel": "<PERSON><PERSON><PERSON>", "reAssignStatus": "Select status to reassign it as the default for new entities when they are first created?", "statusSetAsDefault": "The {statusName} status is set as the default.", "anotherStatusAsDefault": " another status as default.", "dataManagement": {"title": "Data management", "autoArchiveAfter": "Auto-Archive after", "autoDeleteAfter": "Auto-Delete After", "data": "Data", "days": "Days", "daysOfInactivity": "Days of inactivity", "enterNumberOfDays": "Enter №", "and": "And", "or": "Or", "selectConvenientTime": "Select a convenient time of day to process data management jobs", "update": "Update", "delete": "Delete", "deleteEntity": "Failed to delete data management configuration.", "entitySuccessfulDelete": "Data management has been deleted successfully", "dataDelete": "Are you sure you want to delete this data?", "scheduleDelete": "Are you sure you want to delete this schedule?", "emptyUpdateWithoutUid": "Cannot update without a UID.", "failedToLoad": "Failed to load settings:", "updateFailed": "Update failed", "errorTimeZone": "Error generating timezones:", "frequencyAndTimeRequired": "Frequency, time and timezone are required", "noJobFound": "No job ID found for update", "utc": "UTC (Coordinated Universal Time)", "archiveDays": "Archive days", "archiveInactivity": "Archive inactivity days", "deleteDays": "Delete days", "deleteInactivity": "Delete inactivity days", "deleteFailed": "Failed to Delete", "milestone": "milestones", "testPlan": "testPlans", "testRun": "testRuns", "testCase": "testCases", "frequency": "Frequency", "time": "Time", "timezone": "Timezone", "testCases": "Test cases", "testRuns": "Test runs", "testPlans": "Test plans", "milestones": "Milestones"}, "backToCurrentPlan": "Back to Current Plan", "searchFolders": "Search folders...", "noTestCasesFound": "No test cases found", "noTestCasesFoundDescription": "This test run doesn't have any test cases or executions yet. Add test cases to get started.", "formatting": {"bold": "Bold", "italic": "Italic", "strikethrough": "Strikethrough", "code": "Code", "codeBlock": "Code Block", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "bulletList": "Bullet List", "numberedList": "Numbered List", "link": "Link", "table": "Table", "preview": "Preview", "markdownView": "Markdown View", "markdownSupported": "<PERSON><PERSON> supported", "help": "Help", "markdownHelp": "Markdown Help", "basicFormatting": "Basic Formatting", "testRailFormatting": "TestRail Formatting", "enterUrl": "Enter URL", "linkText": "Link text", "imageUpload": "Upload Image", "dragDropImage": "Drag and drop images here or click to upload", "uploadProgress": "Uploading...", "uploadSuccess": "Image uploaded successfully", "uploadError": "Failed to upload image", "invalidFileType": "Invalid file type. Please upload an image.", "fileTooLarge": "File is too large. Maximum size is {size}MB", "testRailImage": "TestRail Image"}, "backToWorkspace": "Back to WorkSpace", "emailSent": "<PERSON>ail sent successfully", "unableToUploadAttachment": "Unable to upload attachment", "contactUs": "Contact Us", "subject": "Subject", "gotQuestion": "Got question? We'd love to hear from you", "topic": "Topic", "message": "Message", "contactAttachment": "Please add at least one attachment before submitting", "emailCooldown": "Please wait 20 seconds before sending another email.", "uploadingTestCasesToAssignedFolders": "Uploading test cases to assigned folders", "assignedFolders": "assigned folders", "rootFolder": "root folder", "assigningTestCasesToFolder": "Assigning {count} test cases to {folderName} folder"}