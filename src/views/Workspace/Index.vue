<template>
  <v-main>
    <div
      v-if="!selectedExecutions.length"
      class="view-page"
    >
      <WorkspaceHeader
        v-if="filterData"
        :key="selectedFilters.projectUids?.length"
        :filter="filterView"
        :todo-count="todoCount"
        :completed-count="completedCount"
        :filter-data.sync="filterData"
        :default-projects="selectedFilters.projectUids"
        @update:searchRuns="debounceGetWorkspaceTestRuns"
        @update:searchMilestones="debounceGetWorkspaceMilestones"
        @update:searchPlans="debounceGetWorkspaceTestPlans"
        @updateView="updateView"
        @update-filters="setFilters"
      />
      <div
        class="mt-3 d-flex justify-center project-placeholder-height rounded-lg"
        :class="{ white: !executions.length, 'align-start': executions.length, 'align-center': !executions.length }"
      >
        <v-row
          align-content="start"
          justify="start"
          dense
          class="white app-height-global mt-0 rounded-lg"
        >
          <v-row>
            <v-col cols="12">
              <WorkspaceList
                :key="`${workspaceKey}-${filterView}`"
                :relations-loading="relationsLoading"
                :case-items="executions"
                :projects="projects"
                :tags="tags"
                :selected-execution.sync="selectedExecution"
                :filter-view="filterView"
                :selected-executions="selectedRows"
                :workspace-users="workspaceUsers"
                :current-page="currentPage"
                :items-per-page="limit"
                :total-pages="totalPages"
                :total-executions="totalExecutions"
                :table-loading="tableLoadingState"
                :search-term="selectedFilters.searchTerm || ''"
                @expandDetail="openDetailView"
                @selectedExecutions="viewExecutions"
                @updateRows="updateRows"
                @update-pagination="onUpdatePagination"
                @applyExecutionFilter="applyExecutionFilter"
                @searchExecution="searchExecution"
                @updateExecution="updateTestExecution"
              />
            </v-col>
            <v-row class="position-sticky bottom-0">
              <v-col
                v-if="selectedRows.length > 0"
                cols="12"
                class="d-flex justify-end"
              >
                <v-menu
                  top
                  offset-y
                  :nudge-top="4"
                  transition="slide-y-transition"
                  origin="top right"
                >
                  <template #activator="{ on }">
                    <v-btn
                      dark
                      large
                      color="blue darken-3"
                      class="text-capitalize font-weight-bold white--text mr-4 mt-2"
                      :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
                      v-on="on"
                    >
                      {{ $t('actions')
                      }}<v-icon>
                        {{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list
                    dense
                    class="text-left"
                  >
                    <v-list-item @click="isExecutionsUpdateDialogOpen = true">
                      <v-icon class="pr-2">
                        mdi-square-edit-outline
                      </v-icon>{{ $t('edit') }}
                    </v-list-item>
                    <v-list-item @click="multiAddResults">
                      <v-icon class="pr-2">
                        mdi-plus
                      </v-icon>{{ $t('addResult') }}
                    </v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
          </v-row>
        </v-row>
        <v-col
          v-if="isDetailViewCollapsed && selectedExecution"
          cols="3"
          class="pr-0 py-0 sticky-div"
        >
          <div class="">
            <ExecutionDetailView
              class="app-height-global overflow-auto h-500px"
              :execution="selectedExecution"
              :test-results.sync="testResults"
              :execution-defects="executionDefects"
              :write-activity="_writeActivity"
              :project-list="projects"
              :user-list="workspaceUsers"
              :is-selected-execution-first-index="isCurrentSelectedExecutionFirstIndex"
              :is-selected-execution-last-index="isCurrentSelectedExecutionLastIndex"
              @closeDetail="closeDetailView"
              @updateExecution="updateTestExecution"
              @addResult="addTestResult"
              @updateResult="updateTestResult"
              @deleteResult="deleteTestResult"
              @moveItem="moveSelectedItem"
              @reloadExecutionResults="reloadExecutionResults"
              @updateStepStatus="updateStepStatus"
              @updateAllStepStatus="updateAllStepStatus"
              @setAllStepsPassed="setAllStepsPassed"
              @refetchExecution="updateTestExecution"
              @openDefectDialog="handleOpenDefectDialog"
            >
              <template #attachment>
                <fileInputWithSlider
                  :project-key="selectedExecution?.project?.key"
                  class="mt-3"
                  :files.sync="selectedExecution.attachments"
                  @uploadAttachments="uploadAttachments"
                  @removeFile="removeFile"
                />
              </template>
            </ExecutionDetailView>
          </div>
        </v-col>
      </div>
    </div>
    <div
      v-else
      class="execution-page"
    >
      <v-row>
        <v-col
          cols="12"
          class="py-0"
        >
          <div class="rounded-lg white pa-6 d-flex align-center justify-space-between">
            <div class="d-flex">
              <div
                class="d-flex align-center cursor-pointer"
                @click="resetExecutions"
              >
                <v-icon>mdi-chevron-left</v-icon>
                <h2 class="__execution_name mx-2">
                  {{ selectedExecution?.name }}
                </h2>
              </div>
            </div>
            <div
              v-if="selectedExecutions.length > 1"
              class="__top-header-right d-flex align-center"
            >
              <v-btn
                v-if="currentExecution > 1"
                :elevation="0"
                small
                @click="moveExecution('previous')"
              >
                <v-icon>mdi-chevron-left</v-icon>
                <span>{{ $t('previous') }}</span>
              </v-btn>
              <p class="mb-0 mx-2">
                {{ currentExecution }} of {{ selectedExecutions.length }}
              </p>
              <v-btn
                v-if="currentExecution < selectedExecutions.length"
                :elevation="0"
                small
                @click="moveExecution('next')"
              >
                <span>{{ $t('next') }}</span>
                <v-icon>mdi-chevron-right</v-icon>
              </v-btn>
            </div>
          </div>
        </v-col>
        <v-col cols="3">
          <result-detail-view
            class="app-height-global"
            :execution.sync="selectedExecution"
            :test-results="testResults"
            :execution-defects="executionDefects"
            :show-actions="false"
            :show-name="false"
            @closeDetail="closeDetailView"
            @updateExecution="updateTestExecution"
            @updateStepStatus="updateStepStatus"
            @addResult="addTestResult"
            @updateResult="updateTestResult"
            @deleteResult="deleteTestResult"
            @moveItem="moveSelectedItem"
            @uploadAttachments="uploadAttachments"
            @removeFile="removeFile"
          />
        </v-col>
        <v-col
          cols="6"
          class="px-0"
        >
          <v-card
            :elevation="0"
            class="pa-6 rounded-lg app-height-global"
          >
            <h2 class="fs-24px text-theme-base fw-semibold mb-4">
              {{ selectedExecution.name }}
            </h2>
            <v-form
              ref="form"
              class="text-left"
            >
              <label class="font-weight-medium d-block result-form-label mb-1">
                {{ $t('status') }}<strong class="danger--text">*</strong>
              </label>
              <v-select
                v-model="selectedExecution.resultState"
                :placeholder="$t('selectStatus')"
                class="rounded-lg mb-4 field-theme custom-prepend"
                :rules="requiredRule"
                :items="resultStatuses"
                item-text="name"
                item-value="id"
                dense
                height="38px"
                :menu-props="{ 'offset-y': true }"
                background-color="#F9F9FB"
                append-icon="mdi-chevron-down"
              />
              <label class="font-weight-medium d-block result-form-label mb-1">
                {{ $t('tags') }}
              </label>
              <TagSelector
                v-model="selectedExecution.resultTags"
                :items="availableResultsTags"
                class="mb-4 w-full"
              />
              <label class="font-weight-medium d-block result-form-label mb-1">
                {{ $t('addAttachment') }}
              </label>
              <fileInput
                ref="attachmentFileInput"
                :files.sync="selectedExecution.files"
                class="w-full mb-4"
              />
              <label class="font-weight-medium d-block result-form-label mb-1">{{ $t('addComment') }} </label>
              <tiptap-editor
                v-model="selectedExecution.resultComment"
                class="mb-8"
                @files-added="updateFiles"
              />
              <div class="d-flex justify-end">
                <v-checkbox
                  v-if="selectedExecutions.length > 1"
                  v-model="goToNextTest"
                  class="field-theme mr-4"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">Go to next Test</span>
                  </template>
                </v-checkbox>

                <v-btn
                  height="40px"
                  width="140px"
                  :elevation="0"
                  color="#f2f4f7"
                  class="text-capitalize black--text mr-4 mt-2 rounded-lg"
                  @click="clearResult"
                >
                  {{ $t('clear') }}
                </v-btn>
                <v-btn
                  height="40px"
                  width="140px"
                  :elevation="0"
                  color="blue"
                  class="text-capitalize white--text mt-2 rounded-lg"
                  @click="addResult"
                >
                  {{ $t('addResult') }}
                </v-btn>
              </div>
            </v-form>
          </v-card>
        </v-col>
        <v-col cols="3">
          <v-card
            :elevation="0"
            class="pa-6 rounded-lg app-height-global"
          >
            <v-text-field
              v-model="search"
              class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
              :placeholder="$t('search')"
              height="40"
              background-color="#F9F9FB"
              hide-details
            >
              <template #prepend-inner>
                <SearchIcon />
              </template>
            </v-text-field>

            <div class="mt-4 d-flex">
              <v-chip
                :class="{
                  'blue--text': filter === 'history',
                  'fw-semibold': filter === 'history',
                  'font-weight-medium': filter != 'history',
                }"
                width="200px"
                :color="filter === 'history' ? '#e6ecff' : '#f9fafb'"
                label
                @click="updateFilter('history')"
              >
                <div class="px-2">
                  {{ $t('resultHistory') }} <span class="ml-2">{{ filterResults.length }}</span>
                </div>
              </v-chip>
            </div>

            <div v-if="filter === 'history'">
              <v-timeline
                v-if="filterResults && filterResults.length"
                dense
                align-top
                class="timeline-history"
              >
                <div
                  v-for="(result, index) in filterResults"
                  :key="index"
                  class="result-content"
                >
                  <v-timeline-item
                    right
                    medium
                    class="timeline-item"
                    color="grey lighten-2"
                  >
                    <template #icon>
                      <span class="grey--text text--darken-1 caption font-weight-medium">{{ index + 1 }}</span>
                    </template>

                    <div
                      v-if="getResultReporter(result)"
                      class="d-flex justify-space-between mb-3"
                    >
                      <div class="d-flex align-center gap-2">
                        <Avatar
                          :size="32"
                          :avatar="getResultReporter(result)?.avatar"
                        />
                        <div class="lh-normal">
                          <p class="mb-0 text-theme-label fs-14px font-weight-regular">
                            {{ getResultReporterDisplayName(result) }}
                          </p>
                        </div>
                      </div>
                      <div class="d-flex align-center">
                        <span class="fs-12px text-theme-secondary">{{ result.resultCreatedAt }}</span>
                      </div>
                    </div>
                    <div class="step-item">
                      <div class="d-flex justify-space-between mb-3">
                        <p class="mb-0 text-theme-label fs-14px fw-semibold">
                          {{ result.title }}
                        </p>
                        <div class="step-item-status">
                          <h5
                            class="fw-semibold text-capitalize"
                            :style="{ color: getStatusColor(result.status, resultStatuses) }"
                          >
                            {{ getStatusName(result.status, resultStatuses) }}
                          </h5>
                        </div>
                      </div>
                      <div class="step-item-comment">
                        <div
                          class="fs-14px text-theme-label align-start"
                          v-html="result.comment"
                        />
                      </div>

                      <div class="d-flex flex-flow-wrap align-center gap-2">
                        <div
                          v-for="(item, index) in result.tags"
                          :key="index"
                        >
                          <v-chip
                            :ripple="false"
                            class="chip-theme"
                          >
                            {{ item.name }}
                          </v-chip>
                        </div>
                      </div>

                      <SliderGroup
                        :files="result.files"
                        is-preview
                      />
                      
                      <!-- Defects for this result -->
                      <div
                        v-if="getDefectsForResult(result.resultUid).length > 0"
                        class="mt-3"
                      >
                        <div class="d-flex align-center mb-2">
                          <span class="fs-12px text-theme-secondary">{{ $t('defects') }}</span>
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                          <div
                            v-for="defect in getDefectsForResult(result.resultUid)"
                            :key="defect.externalLink"
                            class="defect-chip d-flex align-center px-2 py-1 rounded-lg"
                            style="background: #F2F4F7; border: 1px solid #E4E7EC;"
                          >
                            <div
                              class="d-flex align-center"
                              style="min-width: 0; flex: 1;"
                            >
                              <DefectIcon class="mr-1 flex-shrink-0" />
                              <span
                                class="fs-12px text-theme-label mr-1 text-truncate"
                                style="min-width: 0; flex: 1; font-weight: 600;"
                              >{{ defect.name }}</span>
                            </div>
                            <a
                              :href="defect.externalLink"
                              target="_blank"
                              class="defect-link"
                            >
                              <v-icon
                                size="14"
                                class="text-theme-secondary"
                              >mdi-arrow-top-right</v-icon>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </v-timeline-item>
                </div>
              </v-timeline>
              <div
                v-else
                class="mt-3"
              >
                {{ $t('noResultFound') }}
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <EditExecutionsDialog
      v-if="isExecutionsUpdateDialogOpen"
      v-model="isExecutionsUpdateDialogOpen"
      :tags="tags"
      :selected-executions="selectedRows"
      @closeDialog="isExecutionsUpdateDialogOpen = false"
      @refetchExecution="updateTestExecution"
    />
    <AddNewDefectDialog
      v-model="showAddDefectDialog"
      :title="$t('defect.addNewDefectDialog.title')"
      :content="$t('defect.addNewDefectDialog.content')"
      :color="'primary'"
      :write-defect="_writeDefect"
      @close="showAddDefectDialog = false"
      @handle-actions-type="handleDefectActionType"
    />
    <CreateDefect
      v-if="showCreateDefectDialog"
      :create-defect-dialog="showCreateDefectDialog"
      :execution="failedExecution"
      :result-uid="failedResultUid"
      :action-selected="selectedDefectAction"
      :selected-project-key="$route.params.key || getProjectKeyByUid(selectedExecution.projectUid) || executionProjectKey"
      @closeDialog="closeDefectDialog"
      @defectCreated="handleDefectCreated"
      @defectLinked="handleDefectLinked"
    />
    <RunDiscardDialog
      v-model="showConfirmDialog"
      :title="$t('setAllStepsAsPassed')"
      :content="$t('allStepsAsPassed')"
      :btn_label="$t('confirm')"
      :color="'primary'"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmClick"
    />
  </v-main>
</template>
<script>
import WorkspaceHeader from '@/components/Workspace/WorkspaceHeader';
import EditExecutionsDialog from '@/components/Execution/EditExecutionsDialog.vue';
import workspaceService from '@/services/api/workspace';
import WorkspaceList from '@/components/Workspace/WorkspaceList';
import makeResultService from '@/services/api/result';
import makeExecutionService from '@/services/api/execution';
import makeDefectService from '@/services/api/defect';
import ResultDetailView from '@/components/Workspace/ResultDetailView.vue';
import ExecutionDetailView from '@/components/Execution/Index.vue';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { mapActions } from 'vuex';
import TagService from '@/services/api/tag';
import ProjectsService from '@/services/api/project';
import fileInputWithSlider from '@/components/base/FileInputWithSlider.vue';
import fileInput from '@/components/base/FileInput.vue';
import TiptapEditor from '@/components/base/TipTapEditor.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
import SliderGroup from '@/components/base/SliderGroup.vue';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { formattedDate } from '@/utils/util';
import handleLoading from '@/mixins/loader.js';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import CreateDefect from '@/components/Defect/CreateDefect.vue';
import AddNewDefectDialog from '@/components/Defect/AddNewDefectDialog.vue';
import TagSelector from '@/components/base/TagSelector.vue';
import { mapState } from 'vuex';
import { generateDateUid } from '@/utils/util';
import Avatar from '@/components/base/Avatar.vue';
import DefectIcon from '@/assets/svg/left-menu/defect.svg';
import { debounce } from 'debounce';

let makeProjectService;
let makeTagService;

export default {
  components: {
    WorkspaceHeader,
    WorkspaceList,
    ResultDetailView,
    ExecutionDetailView,
    fileInputWithSlider,
    fileInput,
    TiptapEditor,
    SearchIcon,
    SliderGroup,
    CreateDefect,
    AddNewDefectDialog,
    TagSelector,
    Avatar,
    EditExecutionsDialog,
    DefectIcon
  },
  mixins: [handleLoading, colorPreferencesMixin],
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      showConfirmDialog: false,
      stepsObj: {},
      attachments: [],
      filter: 'history',
      loading: false,
      filterData: {},
      filterView: 'todo',
      menuOpen: false,
      selectedFilters: {},
      limit: 10,
      currentPage: 1,
      offset: 0,
      sortBy: [],
      sortDesc: [false],
      executions: [],
      totalExecutions: 0,
      selectedExecution: {},
      selectedExecutionTag: [],
      isDetailViewCollapsed: false,
      testResults: [],
      executionDefects: [],
      selectedExecutions: [],
      selectedRows: [],
      customToolbar: ['#bold', '#italic', '#strike', '|', '#bulletList', '#orderedList'],
      editorContent: '',
      comments: '',
      tiptapFiles: [],
      commentsFiles: [],
      resultStatuses: [],
      statuses: [],
      runStatuses: [],
      tableLoadingState: false,
      percentage: 0,
      statusExecutions: [],
      search: '',
      requiredRule: [(v) => !!v || this.$t('error.requiredField')],
      relationsLoading: false,
      tags: [],
      projects: [],
      showAddDefectDialog: false,
      showCreateDefectDialog: false,
      selectedDefectAction: null,
      failedExecution: null,
      failedResultUid: null,
      availableResultsTags: [],
      goToNextTest: true,
      workspaceKey: generateDateUid(),
      workspaceUsers: [],
      completedStatus: [],
      todoStatus: [],
      todoCount:0,
      completedCount:0,
      isExecutionsUpdateDialogOpen: false,
      executionsCache: {
        data: [],
        totalCount: 0,
        lastFetchedParams: {
          currentPage: 1,
          limit: null,
          offset: null,
          filterView: '',
          searchTerm: '',
          status: [],
          userUids: [],
          projectUids: [],
          milestoneUids: [],
          testPlanUids: [],
          testRunUids: [],
          sortBy: [],
          sortDesc: [],
          orderBy: [],
          selectedFilters: {}
        },
        hasInitiallyLoaded: false,
        relationsCache: {
          tags: {},
          testRun: {},
          milestone: {},
          testPlan: {},
          project: {},
          lastFetchedAt: null
        }
      }
    }
  },
  computed: {
    ...mapState('workspace', ['workspaceFilter']),
    ...mapState('user', ['user', 'currentAccount']),

    cachedExecutions() {
      return this.executionsCache.data;
    },
    
    cachedTotalExecutions() {
      return this.executionsCache.totalCount;
    },
    
    currentPageExecutions() {
      return this.getExecutionsForPage(this.currentPage, this.limit);
    },
    
    totalPages() {
      return Math.ceil(this.cachedTotalExecutions / this.limit);
    },

    _writeActivity() {
      return this.authorityTo('write_activity');
    },
    _writeDefect() {
      return this.authorityTo('write_defect');
    },
    _readMember() {
      return this.authorityTo('read_member');
    },
    isExecutionFailed() {
      return this.getStatusName(this.selectedExecution.resultState, this.resultStatuses).toLowerCase() == 'failed';
    },
    testRunExecutions() {
      return this.executions || [];
    },
    progressStatus() {
      return this.runStatuses.reduce((acc, item) => {
        acc[item.name] = 0;
        return acc;
      }, {});
    },
    currentExecution() {
      return this.selectedExecutions.findIndex((element) => element.uid == this.selectedExecution.uid) + 1;
    },

    currentSelectedExecutionIndex() {
      return this.executions.indexOf(this.selectedExecution);
    },
    isCurrentSelectedExecutionFirstIndex() {
      return this.currentSelectedExecutionIndex == 0;
    },
    isCurrentSelectedExecutionLastIndex() {
      return this.currentSelectedExecutionIndex == this.executions.length - 1;
    },
    nextExecution() {
      return this.executions[this.currentSelectedExecutionIndex + 1];
    },
    previousExecution() {
      return this.executions[this.currentSelectedExecutionIndex - 1];
    },
    executionUids() {
      return this.executions.map((execution) => execution.uid);
    },
    filterResults() {
      return this.testResults
        .filter(({ status }) => !this.search || status === this.getStatusId(this.search))
        .map((item) => {
          const files = [];
          const createdAt = item.resultCreatedAt ? formattedDate(item.resultCreatedAt) : null;

          if (item.attachments?.length) {
            item.attachments.forEach((element) => {
              const { url, name, fileType, uid } = element;
              files.push({
                previewUrl: url,
                name,
                type: fileType,
                uid,
              });
            });
          }

          return {
            ...item,
            resultCreatedAt: createdAt,
            files,
          };
        });
    },
    activeResultStatuses() {
      return this.resultStatuses?.filter((element) => !element.archived);
    },
    executionsTotalCount() {
      return this.executionsCache.totalCount;
    },
    executionsLastParams() {
      return this.executionsCache.lastFetchedParams;
    },
    executionsHasLoaded() {
      return this.executionsCache.hasInitiallyLoaded;
    },
    executionsRelationsCache() {
      return this.executionsCache.relationsCache;
    },
    isFilterViewTodo() {
      return this.filterView === 'todo';
    },
  },
  watch: {
    selectedExecution(newVal) {
      if (!newVal) return;
      if (!newVal.resultTags) {
        this.$set(newVal, 'resultTags', []);
      }
      this.selectedExecutionTag = newVal.tags?.map((tag) => tag.uid);
    },
  },
    async mounted() {
      this.statuses = this.getStatuses('testCase');
      this.completedStatus = this.getEntityCompletedStatuses('testCase');
      this.todoStatus = this.getEntityTodoStatuses('testCase');
      try {
        this.showSkeletonLoader();
   
        await this.getProjects()
        
        if (this.projects?.length) {
          let recentUid = this.workspaceFilter?.recentProjects?.length
            ? this.workspaceFilter.recentProjects[this.workspaceFilter.recentProjects.length - 1]
            : null;

          const isRecentUidExists = recentUid && this.projects.find(p => p.uid === recentUid);

          this.selectedFilters = {
            ...this.selectedFilters,
            projectUids: isRecentUidExists ? [recentUid] : [this.projects[0].uid]
          };

        } else {
          // Navigate the user to the Projects view if no projects are available
          this.$router.push({
            name: 'ProjectsView'
          });
        }
        
        const coreDataPromises = [
          this.getExecutions()
        ];
        
        await Promise.all(coreDataPromises);
        
        // Enhance executions with project data immediately after loading
        this.enhanceExecutionsWithProjectData();
        
        // Hide skeleton loader as soon as core data is loaded
        this.hideSkeletonLoader();

        // Load relations in background without blocking
        this.loadRelations();
        
        // Load filter data in background without blocking
        const backgroundFetchers = [
          this.getTags(),
          this.getResultsTags(),
          this.getWorkspaceProjects(),
          this.getWorkspaceMilestones(),
          this.getWorkspaceTestPlans(),
          this.getWorkspaceTestRuns(),
          this.getWorkspaceUsers(),
        ];
        
        // Load background data without blocking UI or showing errors to user
        Promise.allSettled(backgroundFetchers).then(results => {
          const errors = results
            .map((r, i) => ({ ...r, index: i, methodName: backgroundFetchers[i].name }))
            .filter(r => r.status === 'rejected');

          if (errors.length) {
            console.warn('Some background filter data failed to load:', errors);
            // Don't show error toasts for background data failures
            // as they don't affect core functionality
          }
        }).catch(err => {
          console.warn('Background filter data loading failed:', err);
          // Background failures should not affect the UI
        });

      } catch (err) {
        console.error('Core workspace data loading failed:', err);
        showErrorToast(
          this.$swal,
          'fetchError',
          { item: 'workspace' },
          err?.response?.data || err
        );
        this.hideSkeletonLoader();
      }
    },
  
  created(){
    makeProjectService = ProjectsService(this.$api);
    makeTagService = TagService(this.$api);
    this.runStatuses = this.getStatuses('testRun');
    this.resultStatuses = this.getStatuses('testCase');
    this.selectedFilters = { status: this.getEntityTodoStatuses('testCase') };
    this.addAssignTo(this.user.uid);
    this.selectedFilters.userUids =
      this.currentAccount.type == 'org' ? [...this.workspaceFilter.assignToList] : [this.user.uid];
  },
  methods: {
    ...mapActions({
      uploadToServer: 'attachment/uploadToServer',
      addAssignTo: 'workspace/addAssignTo',
    }),
    async uploadAttachments(newFiles) {
      const handle = this.$route.params.handle;
      const mediaType = 'attachment';
      const executionService = makeExecutionService(this.$api);
      const params = {
        handle,
        projectKey: this.getProjectKeyByUid(this.selectedExecution.projectUid),
        executionUid: this.selectedExecution.uid,
      };
      const baseUrl = `${import.meta.env.VITE_APP_SERVER_BASEURL}/${handle}`;

      if (newFiles?.length)
        await Promise.all(
          newFiles.map(async (file) => {
            await this.uploadToServer({ handle, mediaType, file, apiService: executionService, params }).then(
              (objectData) => {
                const img = {
                  uid: objectData.uid,
                  previewUrl: `${baseUrl}/executions/attachments/${objectData.uid}/object`,
                  type: file.type,
                  name: file.name,
                };
                if (!this.selectedExecution.attachments) {
                  this.$set(this.selectedExecution, 'attachments', []);
                }
                this.selectedExecution.attachments.push(img);
              }
            );
          })
        )
          .then(() => {
            showSuccessToast(this.$swal, this.$t('success.uploadedAttachments'));
          })
          .catch((err) => {
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data);
          });
      else showSuccessToast(this.$swal, this.$t('noAttachments'));
    },
    needsDataFetch(currentParams){
      const lastParams = this.executionsCache.lastFetchedParams;
      const normalizedLastSortDesc = JSON.stringify(currentParams.sortDesc) === '[]' && JSON.stringify(lastParams.sortDesc) === '[false]' ? [] : lastParams.sortDesc;
      const contextChanged = 
        currentParams.filterView !== lastParams.filterView ||
        currentParams.searchTerm !== lastParams.searchTerm ||
        JSON.stringify(currentParams.status) !== JSON.stringify(lastParams.status) ||
        JSON.stringify(currentParams.userUids) !== JSON.stringify(lastParams.userUids) ||
        JSON.stringify(currentParams.projectUids) !== JSON.stringify(lastParams.projectUids) ||
        JSON.stringify(currentParams.milestoneUids) !== JSON.stringify(lastParams.milestoneUids) ||
        JSON.stringify(currentParams.testPlanUids) !== JSON.stringify(lastParams.testPlanUids) ||
        JSON.stringify(currentParams.testRunUids) !== JSON.stringify(lastParams.testRunUids) ||
        JSON.stringify(currentParams.sortBy) !== JSON.stringify(lastParams.sortBy) ||
        JSON.stringify(currentParams.sortDesc) !== JSON.stringify(normalizedLastSortDesc) ||
        JSON.stringify(currentParams.orderBy) !== JSON.stringify(lastParams.orderBy) || 
        JSON.stringify(currentParams.selectedFilters) !== JSON.stringify(lastParams.selectedFilters);
      
      
      if (contextChanged) {
        return true;
      }
      
      const requiredEndIndex = currentParams.currentPage * currentParams.currentPerPage;
      const currentCachedCount = this.executionsCache.data.length;
      
      return requiredEndIndex > currentCachedCount && currentCachedCount < this.executionsCache.totalCount;
    },
    getExecutionsForPage(page, limit){
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      return this.executionsCache.data.slice(startIndex, endIndex);
    },
    setExecutionsCache({ data, totalCount, params, replace = false }){
      if (replace) {
        this.executionsCache.data = data;
      } else {
        this.executionsCache.data.push(...data);
      }
      this.executionsCache.totalCount = totalCount;
      this.executionsCache.lastFetchedParams = { ...params };
      this.executionsCache.hasInitiallyLoaded = true;
    },
    clearExecutionsCache() {
      this.executionsCache.data = [];
      this.executionsCache.totalCount = 0;
      this.executionsCache.lastFetchedParams = {
        limit: null,
        offset: null,
        filterView: '',
        searchTerm: '',
        status: [],
        userUids: [],
        projectUids: [],
        milestoneUids: [],
        testPlanUids: [],
        testRunUids: [],
        sortBy: [],
        sortDesc: [],
        orderBy: []
      };
      this.executionsCache.hasInitiallyLoaded = false;
    },
    updateExecutionInCache(updatedExecution) {
      const index = this.executions.findIndex(exec => exec.uid === updatedExecution.uid);
      if (index !== -1) {
        this.executions.splice(index, 1, updatedExecution);
      }
      const cacheIndex = this.executionsCache.data.findIndex(exec => exec.uid === updatedExecution.uid);
      if (cacheIndex !== -1) {
        this.executionsCache.data.splice(cacheIndex, 1, updatedExecution);
      }
    },
    setExecutionRelations({ type, relations }) {
      this.executionsCache.relationsCache[type] = { ...relations };
      this.executionsCache.relationsCache.lastFetchedAt = new Date().toISOString();
      this.executionsCache.data = this.executionsCache.data.map(execution => {
        const relationData = relations[execution.uid];
        if (relationData) {
          const relationProperty = type === 'tag' ? 'tags' : type;
          return {
            ...execution,
            [relationProperty]: relationData,
            [`${relationProperty}Timestamp`]: new Date().toISOString()
          };
        }
        return execution;
      });
    },
    async loadRelations() {
      if (!this.executions?.length) {
        console.warn('No executions available for loading relations');
        return;
      }
      
      try {
        this.relationsLoading = true;
        await this.loadAllExecutionRelations([
          'tag',
          'testRun', 
          'milestone',
          'testPlan',
          'project',
        ]);
      } catch (error) {
        console.warn('Failed to load execution relations:', error);
      } finally {
        this.relationsLoading = false;
      }
    },
    enhanceExecutionsWithProjectData() {
      if (this.executions?.length && this.projects?.length) {
        this.executions = this.executions.map(execution => {
          const project = this.projects.find(p => p.uid === execution.projectUid);
          if (project && !execution.project) {
            return {
              ...execution,
              project: {
                key: project.key,
                name: project.name,
                uid: project.uid
              }
            };
          }
          return execution;
        });
      }
    },
    searchExecution: debounce(async function(search) {
      this.selectedFilters = {...this.selectedFilters, searchTerm: search}
      await this.getExecutions({loadCounts: true});
      this.enhanceExecutionsWithProjectData();
      this.loadRelations();
    }, 1000),
    debounceGetWorkspaceTestRuns: debounce(async function (options) {
      await this.getWorkspaceTestRuns(options);
    }, 500),
    debounceGetWorkspaceMilestones: debounce(async function (options) {
      await this.getWorkspaceMilestones(options);
    }, 500),
    debounceGetWorkspaceTestPlans: debounce(async function (options) {
      await this.getWorkspaceTestPlans(options);
    }, 500),
    clearResult() {
      this.selectedExecution.files = [];
      this.selectedExecution.resultComment = '';
      this.selectedExecution.resultState = null;
      this.selectedExecution.resultTags = [];
    },
    getStatusId(status) {
      return this.resultStatuses.find((element) => element.name.toLowerCase().startsWith(status.toLowerCase()))?.id;
    },
    removeFile() {
      this.reloadExecution(this.selectedExecution.projectUid,this.selectedExecution.uid);
    },
    async reloadExecution(executionProjectKey, executionUid) {
      try {
        const executionService = makeExecutionService(this.$api);
        const projectKey =
          this.$route.params.key || this.getProjectKeyByUid(this.selectedExecution.projectUid) || executionProjectKey;
        const response = await executionService.getExecution(this.$route.params.handle, projectKey, executionUid);
        const testRunExecution = this.executions.find((element) => element.uid == executionUid);
        this.selectedExecution = {
          ...testRunExecution,
          steps: response.data?.steps || [],
          attachments: response.data?.attachments,
          references: response.data?.references || [],
        };
        
        // Also reload results and defects
        await this.getExecutionResults(projectKey, executionUid);
        await this.getExecutionDefects(projectKey, executionUid);
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchExecution'), {}, error?.response?.data);
      }
    },
    updateFiles(files) {
      this.tiptapFiles = files;
    },
    updateCommentsFiles(files) {
      this.commentsFiles = files;
    },
    updateFilter(filter) {
      this.filter = filter;
    },
    moveExecution(direction) {
      const currentIndex = this.selectedExecutions.findIndex((element) => element.uid == this.selectedExecution.uid);
      if (direction == 'next') this.selectedExecution = this.selectedExecutions[currentIndex + 1];
      else if (direction == 'previous') this.selectedExecution = this.selectedExecutions[currentIndex - 1];

      const projectKey = this.selectedExecution.project?.key;
      const uid = this.selectedExecution.uid;
      this.getExecutionResults(projectKey, uid);
      this.getExecutionDefects(projectKey, uid);
    },
    resetExecutions() {
      this.selectedExecutions = [];
      this.testResults = [];
      this.selectedExecution = null;
      this.isDetailViewCollapsed = false;
    },
    async addResult() {
      if (this.$refs.form.validate()) {
        const payload = {
          files: this.selectedExecution.files,
          status: this.selectedExecution.resultState,
          comment: this.selectedExecution.resultComment,
          tagUids: this.selectedExecution.resultTags.map((tag) => tag.uid),
        };
        try {
          await this.addTestResult(payload, this.isExecutionFailed);
          await this.getExecutionResults(
            this.getProjectKeyByUid(this.selectedExecution.projectUid),
            this.selectedExecution.uid
          );
          await this.getExecutionDefects(
            this.getProjectKeyByUid(this.selectedExecution.projectUid),
            this.selectedExecution.uid
          );
          this.selectedExecution.files = [];
          this.selectedExecution.resultComment = '';
          this.selectedExecution.resultState = null;
          this.selectedExecution.resultTags = [];

          if (this.goToNextTest && this.currentExecution < this.selectedExecutions.length) {
            this.moveExecution('next');
          }
        } catch (error) {
          showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, error?.response?.data);
        } finally {
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.resetValidation();
            }
            if (this.$refs.attachmentFileInput) {
              this.$refs.attachmentFileInput.resetFileInput();
            }
          });
        }
      }
    },
    updateRows(rows) {
      this.selectedRows = rows;
    },
    multiAddResults() {
      this.viewExecutions(this.selectedRows);
    },
    async getWorkspaceExecutionsCounts() {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        const statusArray = Array.isArray(this.statuses) ? this.statuses.map((status) => status.id) : [];
        const assignedTo =  Array.isArray(this.selectedFilters.userUids) ? this.selectedFilters.userUids : [];
        const projectUids = Array.isArray(this.selectedFilters.projectUids) ? this.selectedFilters.projectUids : [];
        const response = await makeWorkspaceService.getWorkspaceExecutionsCounts(handle, {statusArray, assignedTo, projectUids});
        const counts = Array.isArray(response.data) ? response.data : [];

        this.todoCount = counts.reduce((sum, { status, total }) => {
          const n = Number(total) || 0;
          return this.todoStatus.includes(status) ? sum + n : sum;
        }, 0);

        this.completedCount = counts.reduce((sum, { status, total }) => {
          const n = Number(total) || 0;
          return this.completedStatus.includes(status) ? sum + n : sum;
        }, 0);

      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('counts') }, error?.response?.data);
      }
    },
    async getWorkspaceUsers() {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        const response = await makeWorkspaceService.getWorkspaceUsers(handle);
        this.filterData.users = response.data?.users || [];
        this.workspaceUsers = response.data?.users || [];
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('users') }, error?.response?.data);
      }
    },
    async getExecutions({ loadCounts = true, forceRefresh = false } = {}) {
        try {
          this.tableLoadingState = true;
          const makeWorkspaceService = workspaceService(this.$api);
          const handle = this.$route.params.handle;

          const currentPage = this.currentPage || 1;
          const currentPerPage = this.limit || 10;
          const filterView = this.filterView;
          const searchTerm = this.selectedFilters.searchTerm || '';
          const status = this.selectedFilters.status || [];
          const userUids = this.selectedFilters.userUids || [];
          const projectUids = this.selectedFilters.projectUids || [];
          const milestoneUids = this.selectedFilters.milestoneUids || [];
          const testPlanUids = this.selectedFilters.testPlanUids || [];
          const testRunUids = this.selectedFilters.testRunUids || [];
          const sortBy = this.sortBy;
          const sortDesc = this.sortDesc;
          const orderBy = [];
          const selectedFilters = this.selectedFilters || {};

          if (this.sortBy && this.sortBy.length) {
            const primary = `${this.sortBy[0]}:${this.sortDesc[0] ? 'desc' : 'asc'}`;
            orderBy.push(primary);
          }
          orderBy.push('uid:desc');

          const currentParams = {
            currentPage,
            currentPerPage,
            filterView,
            searchTerm,
            status,
            userUids,
            projectUids,
            milestoneUids,
            testPlanUids,
            testRunUids,
            sortBy,
            sortDesc,
            orderBy: orderBy,
            selectedFilters
          };

          // Check if we need to fetch new data
          if (!forceRefresh && !this.needsDataFetch(currentParams)) {
            if (loadCounts) {
              this.getWorkspaceExecutionsCounts().catch(console.warn);
            }
            // Set executions from cache for current page
            this.executions = this.getExecutionsForPage(currentPage, currentPerPage);
            return;
          }

          const contextChanged = this.needsDataFetch(currentParams);

          // Clear cache if context changed
          if (contextChanged || forceRefresh) {
            this.clearExecutionsCache();
          }

          // Calculate what data to fetch
          const cachedData = this.executionsCache.data;
          const startIndex = contextChanged ? 0 : cachedData.length;
          const fetchOffset = startIndex;
          const fetchLimit = contextChanged ? currentPage * currentPerPage : currentPerPage;

          const filters = {
            limit: fetchLimit,
            offset: fetchOffset,
            orderBy: currentParams.orderBy,
            ...this.selectedFilters
          };

          const [response, totalExecutions] = await Promise.all([
            makeWorkspaceService.getWorkspaceExecutions(handle, filters),
            makeWorkspaceService.getWorkspaceExecutions(handle, {
              queryType: 'count',
              ...filters,
            })
          ]);
          
          const freshExecutions = response.data?.items.map(e => ({
            ...e, 
            dueAt: e.dueAt ? this.formatDate(e.dueAt, 'YYYY-MM-DD') : null 
          })) || [];
          
          const totalCount = Number(totalExecutions.data?.count) || 0;

          this.setExecutionsCache({
            data: freshExecutions,
            totalCount,
            params: currentParams,
            replace: contextChanged || forceRefresh
          });

          // Set component data from cache
          this.executions = this.getExecutionsForPage(currentPage, currentPerPage);
          this.totalExecutions = totalCount;

          if (loadCounts) {
            this.getWorkspaceExecutionsCounts().catch(console.warn);
          }
          
        } catch (error) {
          console.error('Failed to load executions:', error);
          showErrorToast(this.$swal, 'fetchError', { item: 'executions' }, error?.response?.data);
          this.executions = [];
          this.totalExecutions = 0;
        } finally {
          this.tableLoadingState = false;
        }
      },
    async getProjects() {
      try {
        const params = {
          status: 'active',
        };
        const queryString = new URLSearchParams(params).toString();
        const response = await makeProjectService.getProjects(this.$route.params.handle, queryString);
        this.projects = response.data?.items || [];
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      }
    },
    setAllStepsPassed(data) {
      this.showConfirmDialog = data.status;
      this.stepsObj = {
        steps: data.steps,
        completedId: data.completedId,
      };
    },
    handleConfirmClick() {
      this.updateAllStepStatus(this.stepsObj);
      this.showConfirmDialog = false;
    },
    handleCloseClick() {
      this.showConfirmDialog = false;
    },
    async updateView(data){
      
      this.filterView = data
      if(data == 'todo'){
        this.selectedFilters = {...this.selectedFilters, status: this.getEntityTodoStatuses('testCase')}
      }else if(data == 'completed'){
        this.selectedFilters = {...this.selectedFilters, status: this.getEntityCompletedStatuses('testCase')}
      }

      await this.getExecutions()
      this.enhanceExecutionsWithProjectData();
      this.loadRelations();
      this.selectedRows = [];
    },
    async viewExecutions(selectedExecutions) {
      this.selectedExecutions.push(...selectedExecutions);
      this.selectedExecution = this.selectedExecutions[0];
      await this.getExecutionResults(
        this.getProjectKeyByUid(this.selectedExecution.projectUid),
        this.selectedExecution.uid
      );
      await this.getExecutionDefects(
        this.getProjectKeyByUid(this.selectedExecution.projectUid),
        this.selectedExecution.uid
      );
    },
    async setFilters(filters){
      this.selectedFilters = { ...this.selectedFilters, ...filters}
      await this.getExecutions(); 
      this.enhanceExecutionsWithProjectData();
      this.loadRelations();
    },
    async applyExecutionFilter (filters){
      this.selectedFilters = {...this.selectedFilters, ...filters}
      await this.getExecutions()
      this.enhanceExecutionsWithProjectData();
      this.loadRelations();
    },
    async getExecutionResults(projectKey, executionUid) {
      const handle = this.$route.params.handle;
      const resultService = makeResultService(this.$api);
      
      try {
        const response = await resultService.getTestResults(handle, projectKey, executionUid);
        this.testResults = response.data;
      } catch (error) {
        console.warn('Failed to fetch execution results:', error);
      }
    },
    
    async getExecutionDefects(projectKey, executionUid) {
      const handle = this.$route.params.handle;
      const defectService = makeDefectService(this.$api);
      
      try {
        const response = await defectService.getExecutionDefects(handle, projectKey, executionUid);
        this.executionDefects = response.data || [];
      } catch (error) {
        console.warn('Failed to fetch execution defects:', error);
        this.executionDefects = [];
      }
    },
    getDefectsForResult(resultUid) {
      return this.executionDefects.filter(defect => defect.resultUid === resultUid) || [];
    },
    closeDetailView() {
      this.selectedExecution = {};
      this.isDetailViewCollapsed = false;
    },
    async openDetailView(item) {
      const projectKey = this.getProjectKeyByUid(item.projectUid);
      this.isDetailViewCollapsed = true;
      await this.getWorkspaceExecution(projectKey, item.uid);
      await this.getExecutionResults(projectKey, item.uid);
      await this.getExecutionDefects(projectKey, item.uid);
    },
    async loadAllExecutionRelations(types = []) {
        try {
          const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes
          const now = new Date();
          const cachedRelations = this.executionsRelationsCache || {
            tags: {},
            testRun: {},
            milestone: {},
            testPlan: {},
            project: {},
            lastFetchedAt: null
          };
          
          for (const type of types) {
            const relationProperty = this.camelCaseRelation(type);
            const executionsNeedingFetch = [];
            const cachedRelationsData = {};
            
            // Check cache expiry
            const lastFetchedAt = cachedRelations.lastFetchedAt;
            const isCacheValid = lastFetchedAt && 
              (now - new Date(lastFetchedAt)) < CACHE_EXPIRY_MS;
            
            // Check which executions need fresh data
            for (const execution of this.executions) {
              const executionUid = execution.uid;
              
              // Use store cache if available and valid
              if (isCacheValid && cachedRelations[type] && cachedRelations[type][executionUid]) {
                cachedRelationsData[executionUid] = cachedRelations[type][executionUid];
                continue;
              }
              
              // Check execution-level cache
              if (this.hasValidCachedRelation(execution, relationProperty, now, CACHE_EXPIRY_MS)) {
                cachedRelationsData[executionUid] = execution[relationProperty];
                continue;
              }
              
              executionsNeedingFetch.push(executionUid);
            }
            
            // Fetch missing relations
            let fetchedRelationsData = {};
            if (executionsNeedingFetch.length > 0) {
              try {
                const response = await workspaceService(this.$api)
                  .getWorkspaceExecutionRelations(
                    this.$route.params.handle,
                    type,
                    executionsNeedingFetch
                  );
                fetchedRelationsData = response.data;

                this.setExecutionRelations({
                  type,
                  relations: { ...cachedRelations[type], ...fetchedRelationsData }
                });

              } catch (error) {
                console.warn(`Failed to fetch ${type} relations:`, error);
                continue;
              }
            }
            
            // Merge all relations data
            const allRelationsData = { ...cachedRelationsData, ...fetchedRelationsData };
            
            // Update executions with relations
            this.executions = this.executions.map(exec => {
              const relationData = allRelationsData[exec.uid] ?? (type === 'tag' ? [] : null);
              return {
                ...exec,
                [relationProperty]: relationData,
                [`${relationProperty}Timestamp`]: new Date().toISOString(),
              };
            });
          }
          
        } catch (error) {
          console.warn('Failed to load execution relations:', error);
          throw error;
        }
      },

      hasValidCachedRelation(execution, relationProperty, currentTime, cacheExpiryMs) {
        if (!Object.prototype.hasOwnProperty.call(execution, relationProperty)) {
          return false;
        }
          
        const relationTimestamp = execution[`${relationProperty}Timestamp`];
        if (relationTimestamp) {
          const relationTime = new Date(relationTimestamp);
          const timeDiff = currentTime - relationTime;
          return timeDiff < cacheExpiryMs;
        }

        const generalTimestamp = execution.timeStamp || execution.timeStap;
        if (generalTimestamp) {
          const generalTime = new Date(generalTimestamp);
          const timeDiff = currentTime - generalTime;
          return timeDiff < cacheExpiryMs;
        }

        return false;
      },

    camelCaseRelation(key) {
      switch (key) {
        case 'testRun':   return 'testRun';
        case 'testPlan':  return 'testPlan';
        case 'milestone': return 'milestone';
        case 'project':   return 'project';
        case 'tag':       return 'tags';
        default:          return key;
      }
    },

    async reloadExecutionResults(resultUid) {
      await this.getExecutionResults(this.getProjectKeyByUid(this.selectedExecution.projectUid), resultUid);
      await this.getExecutionDefects(this.getProjectKeyByUid(this.selectedExecution.projectUid), resultUid);
    },
    async getWorkspaceExecution(projectKey, uid) {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        const response = await makeWorkspaceService.getWorkspaceExecution(handle, projectKey, uid);
        const project = this.projects.find((p) => p.key === projectKey);
        if (project) {
          this.selectedExecution = {
            ...response.data,
            project: {
              key: project.key,
              name: project.name,
              uid: project.uid
            }
          };
        }
        
        // Also load results and defects
        await this.getExecutionResults(projectKey, uid);
        await this.getExecutionDefects(projectKey, uid);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'execution' }, error?.response?.data);
      }
    },
    async performStepStatusUpdate(steps) {
      const handle = this.$route.params.handle;
      const executionService = makeExecutionService(this.$api);
      const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid);
      const executionId = this.selectedExecution.uid;
      const payload = { steps };
      await executionService.updateExecution(handle, projectKey, executionId, payload);
      const response = await executionService.getExecution(handle, projectKey, executionId);
      if (response.status === 200) {
        this.selectedExecution = {
          ...this.selectedExecution,
          steps: response.data.steps,
          references: response.data?.references || [],
        };
        return response;
      }
      throw new Error(`Unexpected status ${response.status}`);
    },

    async updateStepStatus(stepStatus) {
      const { steps } = this.selectedExecution;
      let step;

      if (stepStatus.parentStepUid) {
        const parent = steps.find((s) => s.uid === stepStatus.parentStepUid);
        step = parent?.children?.find((c) => c.uid === stepStatus.stepItemId);
      } else {
        step = steps.find((s) => s.uid === stepStatus.stepItemId);
      }
      if (!step) return;

      step.status = stepStatus.statusUid;

      const stepsPayload = [
        {
          uid: step.uid,
          position: step.position,
          status: stepStatus.statusUid,
        },
      ];

      try {
        await this.performStepStatusUpdate(stepsPayload);
        showSuccessToast(this.$swal, this.$t('toast.stepStatusUpdated'));
      } catch (error) {
        showErrorToast(this.$swal, this.$t('toast.failedToUpdateStepStatus'), {}, error?.response?.data);
      }
    },

    flattenSteps(steps = []) {
      return steps.reduce((acc, step) => {
        acc.push(step);
        if (Array.isArray(step.children) && step.children.length) {
          acc.push(...this.flattenSteps(step.children));
        }
        return acc;
      }, []);
    },

    async updateAllStepStatus(data) {
      if (!Array.isArray(data.steps) || data.steps.length === 0) return;

      const all = this.flattenSteps(data.steps);
      const stepsPayload = all.map((s) => ({
        uid: s.uid,
        position: s.position,
        status: data.completedId,
      }));

      try {
        await this.performStepStatusUpdate(stepsPayload);
        showSuccessToast(this.$swal, this.$t('toast.stepStatusUpdated'));
      } catch (error) {
        showErrorToast(this.$swal, this.$t('toast.failedToUpdateStepStatus'), {}, error?.response?.data);
      }
    },
    async moveSelectedItem(direction) {
      let itemIndex = this.executions?.findIndex((element) => element.uid == this.selectedExecution.uid);
      if (itemIndex == -1) {
        this.selectedExecution = null;
        this.isDetailViewCollapsed = false;
      }
      if (direction == 'next' && itemIndex < this.executions.length - 1)
        this.selectedExecution = this.executions[itemIndex + 1];
      else if (direction == 'previous' && itemIndex > 0) this.selectedExecution = this.executions[itemIndex - 1];

      await this.getExecutionResults(
        this.getProjectKeyByUid(this.selectedExecution.projectUid),
        this.selectedExecution.uid
      );
      await this.getExecutionDefects(
        this.getProjectKeyByUid(this.selectedExecution.projectUid),
        this.selectedExecution.uid
      );
    },
    async addTestResult(data, isExecutionFailed) {
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid);
      const selectedExecution = this.selectedExecution.uid;
      const mediaType = 'attachment';
      const payload = {
        status: data.status,
        tagUids: data.tagUids,
      };

      data.comment && (payload.comment = data.comment);

      await resultService
        .addTestResult(handle, projectKey, selectedExecution, payload)
        .then(async (response) => {
          const result = response.data;
          const params = {
            handle,
            projectKey,
            resultUid: result.uid,
          };
          const findIndex = this.executions.findIndex((element) => element.uid == this.selectedExecution.uid);
          this.executions.splice(findIndex, 1, { ...this.selectedExecution, status: payload.status });
          this.selectedExecution = { ...this.selectedExecution, ...{ status: payload.status } };

          if (data.files?.length)
            await Promise.all(
              data.files.map(async (file) => {
                await this.uploadToServer({ handle, mediaType, file, apiService: resultService, params });
              })
            )
              .then(() => {
                showSuccessToast(this.$swal, this.$t('success.testResultAdded'));
                if (isExecutionFailed) {
                  this.failedExecution = this.selectedExecution;
                  this.failedResultUid = result.uid;
                  this.showAddDefectDialog = true;
                }
              })
              .catch((err) => {
                showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, err?.response?.data);
              });
          else {
            if (isExecutionFailed) {
              this.failedExecution = this.selectedExecution;
              this.failedResultUid = result.uid;
              this.showAddDefectDialog = true;
            }
            showSuccessToast(this.$swal, this.$t('success.testResultAdded'));
          }
        })
        .catch((err) => {
          showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, err?.response?.data);
        });

      await this.getExecutionResults(projectKey, selectedExecution);
      await this.getExecutionDefects(projectKey, selectedExecution);
      await this.getExecutions();
      this.loadRelations();
    },
    async updateTestResult(resultUid, data) {
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid);

      const payload = {
        status: data.status,
        comment: data.comment,
      };

      await resultService
        .updateTestResult(handle, projectKey, resultUid, payload)
        .then(async () => {
          const resultIndex = this.testResults.findIndex((element) => element.resultUid == resultUid);
          this.testResults[resultIndex].status = payload.status[0].toUpperCase() + payload.status.slice(1);
          this.testResults[resultIndex].comment = payload.comment;
          const mediaType = 'attachment';
          const params = {
            handle,
            projectKey,
            resultUid,
          };
          if (data.files.length)
            await Promise.all(
              data.files.map(async (file) => {
                await this.uploadToServer({ handle, mediaType, file, apiService: resultService, params });
              })
            )
              .then(() => {
                showSuccessToast(this.$swal, this.$t('success.testResultUpdated'));
              })
              .catch((err) => {
                showErrorToast(this.$swal, this.$t('success.testResultUpdated'), {}, err?.response?.data);
              });
          else showSuccessToast(this.$swal, this.$t('success.testResultUpdated'));
        })
        .catch((err) => {
          showErrorToast(this.$swal, this.$t('success.testResultUpdated'), {}, err?.response?.data);
        });
    },
    async deleteTestResult(resultUid) {
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid);

      await resultService
        .deleteTestResult(handle, projectKey, resultUid)
        .then(() => {
          showSuccessToast(this.$swal, this.$t('success.testResultDeleted'));
          const resultIndex = this.testResults.findIndex((element) => element.resultUid == resultUid);
          this.testResults.splice(resultIndex, 1);
        })
        .catch((err) => {
          showErrorToast(this.$swal, this.$t('error.failedToDeleteTestResult'), {}, err?.response?.data);
        });
    },
    tagFomation(originalTags, newTags) {
      const tagIdsToRemove = originalTags.filter((tagId) => !newTags.includes(tagId));
      const tagIdsToAdd = newTags.filter((tagId) => !originalTags.includes(tagId));

      const tagReplacements = [];

      if (tagIdsToRemove.length > 0) {
        tagReplacements.push({
          existingTagUids: tagIdsToRemove,
          newTagUids: [],
        });
      }

      if (tagIdsToAdd.length > 0) {
        tagReplacements.push({
          existingTagUids: [],
          newTagUids: tagIdsToAdd,
        });
      }

      return {
        tagUids: tagIdsToAdd,
        tagReplacements,
      };
    },
    async updateTestExecution(updatedData) {
      try {
        const executionService = makeExecutionService(this.$api);
        const handle = this.$route.params.handle;
        const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid) || updatedData?.projectKey;
        const selectedExecution = this.selectedExecution.uid || updatedData?.selectedExecution;
        let payload = {};

        if (updatedData.property == 'tags') {
          const originalTags = this.selectedExecutionTag || [];
          const newTags = updatedData?.value?.map((tag) => tag.uid);
          const { tagUids, tagReplacements } = this.tagFomation(originalTags, newTags);
          payload = {
            tagUids,
            tagReplacements,
          };
        } else if (typeof updatedData.value === 'object') {
          Object.assign(payload, {
            [updatedData.property]: updatedData.value.uid,
          });
        } else {
          Object.assign(payload, {
            [updatedData.property]: updatedData.value,
          });
        }

        const response = await executionService.updateExecution(handle, projectKey, selectedExecution, payload);
        const updatedExecution = response.data;

        this.getWorkspaceExecutionsCounts();

        if (updatedExecution.uid) {
          // Find the existing execution to preserve all properties
          const existingExecution = this.executionsCache.data?.find(exec => exec.uid === updatedExecution.uid);
          
          // Merge updated data with existing execution, preserving relations and other cached data
          const mergedExecution = {
            ...existingExecution, // Keep existing properties
            ...updatedExecution,  // Apply updates
            // Preserve relation data that might not be in the API response
            tags: updatedExecution.tags ?? existingExecution?.tags,
            testRun: updatedExecution.testRun ?? existingExecution?.testRun,
            milestone: updatedExecution.milestone ?? existingExecution?.milestone,
            testPlan: updatedExecution.testPlan ?? existingExecution?.testPlan,
            project: updatedExecution.project ?? existingExecution?.project,
            // Preserve relation timestamps
            tagsTimestamp: existingExecution?.tagsTimestamp,
            testRunTimestamp: existingExecution?.testRunTimestamp,
            milestoneTimestamp: existingExecution?.milestoneTimestamp,
            testPlanTimestamp: existingExecution?.testPlanTimestamp,
            projectTimestamp: existingExecution?.projectTimestamp,
          };

          this.updateExecutionInCache(mergedExecution);

          // Update selected execution if it matches
          if (this.selectedExecution?.uid === updatedExecution.uid) {
            this.selectedExecution = { ...this.selectedExecution, ...mergedExecution };
          }

          const statusDetails = this.getStatusDetails(updatedExecution.status, this.resultStatuses);

          if (updatedData.property == 'status' && statusDetails?.isFailure) {
            this.failedExecution = mergedExecution;
            this.failedResultUid = null;
            this.showAddDefectDialog = true;
          } else {
            this.showAddDefectDialog = false;
            this.showCreateDefectDialog = false;
            this.failedExecution = null;
            this.failedResultUid = null;
          }

          // Force reactivity update
          this.workspaceKey = generateDateUid();
          
          if (updatedData.property == 'tags' && updatedData.isAddTags) {
            showSuccessToast(this.$swal, this.$t('success.newTagApplied'));
          } else if (updatedData.property == 'tags' && !updatedData.isAddTags) {
            showSuccessToast(this.$swal, this.$t('success.tagRemoved'));
          } else {
            showSuccessToast(this.$swal, this.$t('success.executionUpdated'));
          }

          // Check if execution should move to a different tab
          const shouldMoveToCompleted = this.filterView === 'todo' && statusDetails.isFailure || this.filterView === 'todo' && statusDetails.isCompleted;
          const shouldMoveToTodo = this.filterView === 'completed' && !statusDetails.isFailure || this.filterView === 'completed' && !statusDetails.isCompleted;
          
          if (shouldMoveToCompleted || shouldMoveToTodo) {
            this.removeExecutionFromCache(this.selectedExecution?.uid);
            await this.fetchReplacementForRemovedExecution();     
            this.totalExecutions = this.executionsCache.totalCount;
            this.isDetailViewCollapsed = false;
          }

        }
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.executionUpdateFailed'), {}, error?.response?.data);
      }
    },

    async fetchReplacementForRemovedExecution() {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        
        // Check if we need to fetch more data
        const currentCacheSize = this.executionsCache.data.length;
        const targetSize = Math.min(this.currentPage * this.limit, this.executionsCache.totalCount);
        
        // Only fetch if we don't have enough data for the current view
        if (currentCacheSize >= targetSize) {
          // We have enough data, just update the current page view
          this.executions = this.getExecutionsForPage(this.currentPage, this.limit);
          return;
        }
        
        const fetchOffset = currentCacheSize; // Start from where our cache ends
        const fetchLimit = 1; // Only fetch 1 execution to replace the removed one
        
        const filters = {
          limit: fetchLimit,  // Fetch only 1 execution
          offset: fetchOffset,
          orderBy: this.executionsCache.lastFetchedParams.orderBy || ['uid:desc'],
          ...this.selectedFilters
        };
        
        const response = await makeWorkspaceService.getWorkspaceExecutions(handle, filters);
        const newExecutions = response.data?.items.map(e => ({
          ...e, 
          dueAt: e.dueAt ? this.formatDate(e.dueAt, 'YYYY-MM-DD') : null 
        })) || [];
        
        if (newExecutions.length > 0) {
          // Add to cache
          this.executionsCache.data.push(...newExecutions);
          
          // Enhance with project data
          if (this.projects?.length) {
            const enhancedExecutions = newExecutions.map(execution => {
              const project = this.projects.find(p => p.uid === execution.projectUid);
              if (project && !execution.project) {
                return {
                  ...execution,
                  project: {
                    key: project.key,
                    name: project.name,
                    uid: project.uid
                  }
                };
              }
              return execution;
            });
            
            // Update cache with enhanced data - replace the newly added items
            const startIndex = this.executionsCache.data.length - newExecutions.length;
            enhancedExecutions.forEach((enhancedExecution, index) => {
              this.executionsCache.data.splice(startIndex + index, 1, enhancedExecution);
            });
          }
          
          // Update the current page executions
          this.executions = this.getExecutionsForPage(this.currentPage, this.limit);
          
          // Load relations for ONLY the new executions
          try {
            const newExecutionUids = newExecutions.map(exec => exec.uid);
            await this.loadRelationsForSpecificExecutions(newExecutionUids, [
              'tag',
              'testRun', 
              'milestone',
              'testPlan',
              'project',
            ]);
          } catch (error) {
            console.warn('Failed to load relations for new executions:', error);
          }
        } else {
          // No more executions available, just update the current view
          this.executions = this.getExecutionsForPage(this.currentPage, this.limit);
        }

      } catch (error) {
        console.warn('Failed to fetch replacement execution:', error);
        // Even if fetch fails, update the current view with available data
        this.executions = this.getExecutionsForPage(this.currentPage, this.limit);
      }
    },

    async loadRelationsForSpecificExecutions(executionUids, types = []) {
      if (!executionUids?.length || !types?.length) return;
      
      try {
        for (const type of types) {
          try {
            const response = await workspaceService(this.$api)
              .getWorkspaceExecutionRelations(
                this.$route.params.handle,
                type,
                executionUids
              );
            const fetchedRelationsData = response.data;

            this.setExecutionRelations({
              type,
              relations: { ...this.executionsRelationsCache[type], ...fetchedRelationsData }
            });

            const relationProperty = this.camelCaseRelation(type);
            this.executionsCache.data = this.executionsCache.data.map(exec => {
              if (executionUids.includes(exec.uid)) {
                const relationData = fetchedRelationsData[exec.uid] ?? (type === 'tag' ? [] : null);
                return {
                  ...exec,
                  [relationProperty]: relationData,
                  [`${relationProperty}Timestamp`]: new Date().toISOString(),
                };
              }
              return exec;
            });
            
            this.executions = this.executions.map(exec => {
              if (executionUids.includes(exec.uid)) {
                const relationData = fetchedRelationsData[exec.uid] ?? (type === 'tag' ? [] : null);
                return {
                  ...exec,
                  [relationProperty]: relationData,
                  [`${relationProperty}Timestamp`]: new Date().toISOString(),
                };
              }
              return exec;
            });

          } catch (error) {
            console.warn(`Failed to fetch ${type} relations for specific executions:`, error);
          }
        }
        
      } catch (error) {
        console.warn('Failed to load relations for specific executions:', error);
      }
    },

    removeExecutionFromCache(executionUid) {
      this.executionsCache.data = this.executionsCache.data.filter(exec => exec.uid !== executionUid);
      this.executionsCache.totalCount = Math.max(0, this.executionsCache.totalCount - 1);
      this.executions = this.executions.filter(exec => exec.uid !== executionUid);
    },
    async getWorkspaceProjects() {
      const handle = this.$route.params.handle;
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const response = await makeWorkspaceService.getProjects(handle);
        this.filterData.projects = response.data?.projects;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      }
    },
    async getWorkspaceMilestones({ query, projectUids = [] } = {}) {
      const params = {};
      const handle = this.$route.params.handle;

      if (query) {
        params.query = query;
      }

      params.projectUids = [...(this.selectedFilters?.projectUids || []), ...projectUids];

      if (this.selectedFilters?.milestoneUids?.length) {
        params.selectedMilestones = this.selectedFilters.milestoneUids;
      }

      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const response = await makeWorkspaceService.getMilestones(handle, params);
        this.filterData = {
          ...this.filterData,
          milestones: response.data?.milestones || [],
        };
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, error?.response?.data);
      }
    },
    async getWorkspaceTestPlans({ query, projectUids = [], milestoneUids = [] } = {}) {
      const params = {};

      if (query) {
        params.query = query;
      }

      params.projectUids = [...(this.selectedFilters?.projectUids || []), ...projectUids];

      params.milestoneUids = [...(this.selectedFilters?.milestoneUids || []), ...milestoneUids];

      if (this.selectedFilters?.planUids?.length) {
        params.selectedPlans = this.selectedFilters.planUids;
      }

      const handle = this.$route.params.handle;
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const response = await makeWorkspaceService.getTestPlans(handle, params);
        this.filterData = {
          ...this.filterData,
          testPlans: response.data?.testPlans || [],
        };
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test plans' }, error?.response?.data);
      }
    },
    async getWorkspaceTestRuns({ query, projectUids = [], planUids = [], milestoneUids = [] } = {}) {
      const params = {};

      if (this.selectedFilters?.runUids?.length) {
        params.selectedRuns = this.selectedFilters.runUids;
      }

      params.selectedMilestones = [...milestoneUids, ...(this.selectedFilters?.milestoneUids || [])];

      if (query) {
        params.query = query;
      }

      params.projectUids = [...(this.selectedFilters?.projectUids || []), ...projectUids];

      params.planUids = [...(this.selectedFilters?.planUids || []), ...planUids];

      const handle = this.$route.params.handle;
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const response = await makeWorkspaceService.getTestRuns(handle, params);
        this.filterData = {
          ...this.filterData,
          testRuns: response.data?.testRuns || [],
        };
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
      }
    },
    getProjectKeyByUid(projectUid) {
      const project = this.projects.find((p) => p.uid === projectUid);
      return project ? project.key : null;
    },
    async getTags() {
      const handle = this.$route.params.handle;
      try {
        const response = await makeTagService.getTags(handle, 'executions');
        this.tags = response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
        return [];
      }
    },
    handleDefectActionType(actionType) {
      this.showAddDefectDialog = false;
      this.selectedDefectAction = actionType;

      if (actionType === 'Create new defect' || actionType === 'Link existing defect') {
        this.showCreateDefectDialog = true;
      }
    },
    handleOpenDefectDialog(execution) {
     this.failedExecution = execution;
     this.failedResultUid = null;
     this.showAddDefectDialog = true;
    },
    handleDefectCreated() {
      this.showCreateDefectDialog = false;
      this.showAddDefectDialog = false;
      this.failedExecution = null;
      this.failedResultUid = null;
     this.reloadExecutions({ preserveFilters: true });
    },

    handleDefectLinked() {
      this.showCreateDefectDialog = false;
      this.showAddDefectDialog = false;
      this.failedExecution = null;
      this.failedResultUid = null;
      this.reloadExecutions({ preserveFilters: true });
    },
    async reloadExecutions({ preserveFilters = false } = {}){

      if (!preserveFilters) {
        this.selectedFilters = { 
          ...this.selectedFilters, 
          searchTerm: '' 
        };
      }

      await this.getExecutions();
      this.enhanceExecutionsWithProjectData();
      this.loadRelations();

      if (this.selectedExecution?.uid) {
        const projectKey = this.getProjectKeyByUid(this.selectedExecution.projectUid);
        await this.getExecutionDefects(projectKey, this.selectedExecution.uid);
      }
    },
    closeDefectDialog() {
      this.showCreateDefectDialog = false;
      this.selectedDefectAction = null;
      this.failedExecution = null;
      this.failedResultUid = null;
    },
    async getResultsTags() {
      try {
        const handle = this.$route.params.handle;
        const response = await makeTagService.getTags(handle, 'results');
        this.availableResultsTags = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'results tags' }, error?.response?.data);
        return [];
      }
    },
    async onUpdatePagination(options) {

      const newPage = options.page || this.currentPage;
      const newItemsPerPage = options.itemsPerPage || this.limit;
      const newSortBy = options.sortBy || [];
      const newSortDesc = options.sortDesc || [];

      const pageChanged = newPage !== this.currentPage;
      const itemsPerPageChanged = newItemsPerPage !== this.limit;
      const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(this.sortBy);
      const normalizedCurrentSortDesc = JSON.stringify(newSortBy) === '[]' && JSON.stringify(this.sortDesc) === '[false]' ? [] : this.sortDesc;
      const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(normalizedCurrentSortDesc);

      if (!pageChanged && !itemsPerPageChanged && !sortByChanged && !sortDescChanged) {
        return;
      }

      this.currentPage = newPage;
      this.limit = newItemsPerPage;
      this.sortBy = newSortBy;
      this.sortDesc = newSortDesc;

      if(pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) {
        await this.getExecutions();
        this.enhanceExecutionsWithProjectData();
        this.loadRelations();
      }

    },
    handleEditExecutions() {
      this.isExecutionsUpdateDialogOpen = true;
    },
    getResultReporter(result) {
      if (result.reporter) {
        return result.reporter;
      } else if (result.customFields?.reporter) {
        return result.customFields.reporter;
      }
      return null;
    },
    getResultReporterDisplayName(result) {
      const reporter = this.getResultReporter(result);
      if (!reporter) return '';
      
      if (reporter.firstName || reporter.lastName) {
        return `${reporter.firstName || ''} ${reporter.lastName || ''}`.trim();
      }

      if (reporter.name) {
        return reporter.name;
      }
      
      return '';
    }
  },
};
</script>
<style scoped>
#input-file-browser {
  display: block;
  height: 120px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d0d5dd;
  background-color: #f9fafb;
  color: #0c2ff3;
  font-weight: bold;
  cursor: pointer;
}
#input-file-browser:hover {
  background-color: #eeeeee;
  transition: all 0.5s;
}
h2.__execution_name {
  color: #101828;
  font-weight: 500;
  font-size: 24px;
  font-weight: 600;
}
h2.result-execution {
  font-size: 20px;
}
.chip-theme {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  height: 24px !important;
  border-radius: 8px !important;
}
</style>
