<template>
  <div
    class="ml-3"
  >
    <CustomTemplatesHeader
      :write-template="_writeTemplate"
      :entity-type="entityType"
      :test-formats-count="testFormatsCount"
      :result-formats-count="resultFormatsCount"
      @create-custom-template="onCreateTemplates"
      @update-entity-type="updateEntityType"
    />
    <v-card
      v-if="totalTemplates == 0 && !skeletonLoaderState && !hasActiveFilters && !tableLoadingState && !filter.name"
      class="mt-3 app-height-global d-flex align-center justify-center"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <TemplateActiveEmptyState
        :write-entity="_writeTemplate"
        :image-src="require('@/assets/png/empty-step.png')"
        image-max-width="323px"
        :title="$t('templatesPage.emptyTitle')"
        :button-text="$t('templatesPage.createTemplate')"
        :is-project-archived="isProjectArchived"
        button-color="primary"
        @button-click="onCreateTemplates"
      />
    </v-card>

    <v-row v-else>

      <div
        :class="[isDetailViewCollapsed ? 'col-8' : 'col-12']"
      >
        <v-card
          class="py-6 px-6 mt-3 app-height-global"
          rounded="lg"
          elevation="0"
          width="100%"
        >
          <v-row
            justify="space-between"
            class="align-center"
          >
            <v-col
              cols="12"
              md="auto"
              class="d-flex align-center"
            >
              <SearchComponent
                :search="filter.name"
                :placeholder="$t('placeHolder.searchByName')"
                class="mr-3"
                @update:search="filter.name = $event"
              />

              <TemplateFilterDialog
                :data="filter"
                :data-sources="dataSources"
                :creators="creators"
                :current-account="currentAccount"
                @update-filter-condition="updateFilterCondition"
              />
            </v-col>

            <v-col
              cols="12"
              md="auto"
            >
              <v-row
                justify="end"
                class="align-center"
              >
                <SettingsMenu table-type="customTemplates" />
              </v-row>
            </v-col>
          </v-row>

          <TemplateTable
            :headers="filteredHeaders"
            :items="filteredTemplates"
            :total="totalTemplates"
            :write-template="_writeTemplate"
            :delete-template="_deleteTemplate"
            :active-row-uid="activeTemplateUid"
            item-key="uid"
            @edit="onEditTemplate"
            @update-pagination="onUpdatePagination"
            @delete="onDeleteTemplate"
            @viewDetail="onViewDetails"
            @make-default="onMakeDefault"
            @reassign="onReassignDefault"
          />
          <Pagination
            v-if="totalTemplates > 0"
            :page="currentPage"
            :total-pages="totalPages"
            :items-per-page="perPage"
            :total-items="totalTemplates"
            @update:pagination="onUpdatePagination"
          />
        </v-card>
      </div>
      <div
        v-if="isDetailViewCollapsed"
        class="col-4 pl-0"
      >
        <TemplateDetailView
          :key="templateDetails?.uid"
          showActions
          :data="templateDetails"
          :is-first-template="isFirstTemplate"
          :is-last-template="isLastTemplate"
          @editTemplate="onEditTemplate"
          @viewPreviousTemplate="onViewPreviousTemplate"
          @viewNextTemplate="onViewNextTemplate"
          @closeDetail="() => isDetailViewCollapsed = false"
        />
      </div>
    </v-row>
    <TemplateCreateUpdateCustomTemplateDialog
      v-model="showCreateUpdateCustomTemplateDialog"
      :data="selectedTemplates"
      :loading.sync="createBtnLoading"
      :entity-type="entityType"
      :default-filtered-template="filteredTemplatesData"
      @create-template="createTemplate"
      @update-template="updateTemplate"
      @close-dialog="showCreateUpdateCustomTemplateDialog = false"
    />

    <TemplateViewDetailDialog
      v-model="showViewDetailDialog"
      :data="templateDetails"
      @close-dialog="showViewDetailDialog = false"
    />

    <TemplateDeleteConfirmDialog
      v-model="showDeleteConfirmDialog"
      :template="selectedTemplates"
      @delete="deleteTemplate"
    />

    <TemplateAssignDefaultDialog
      v-model="showReassignDialog"
      :templates="templates"
      :current-template="selectedTemplates"
      @reassign-default="reassignDefault"
    />

  </div>
</template>

<script>
import _ from 'lodash';
import makeTemplateService from '@/services/api/template';
import makeOrgService from '@/services/api/org';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import CustomTemplatesHeader from '@/components/Admin/CustomTemplates/CustomTemplatesHeader';
import TemplateFilterDialog from '@/components/Admin/CustomTemplates/FilterDialog.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import TemplateTable from '@/components/Admin/CustomTemplates/TemplateTable.vue';
import TemplateCreateUpdateCustomTemplateDialog from '@/components/Admin/CustomTemplates/CreateUpdateCustomTemplateDialog.vue';
import TemplateDeleteConfirmDialog from '@/components/Admin/CustomTemplates/DeleteConfirmDialog.vue';
import TemplateViewDetailDialog from '@/components/Admin/CustomTemplates/ViewDetailDialog.vue';
import TemplateAssignDefaultDialog from '@/components/Admin/CustomTemplates/AssignDefaultTemplateDialog.vue';
import { handleNetworkStatusError } from '@/mixins/redirect';
import { mapGetters, mapActions } from 'vuex';
import TemplateActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js'
import Pagination from '@/components/base/Pagination.vue';
import { entityTypes, entityTypeNames } from '@/constants/templates';
import TemplateDetailView from '@/components/Admin/CustomTemplates/TemplateDetailView.vue';

export default {
  name: 'Templates',
  components: {
    CustomTemplatesHeader,
    TemplateFilterDialog,
    SearchComponent,
    SettingsMenu,
    TemplateTable,
    TemplateCreateUpdateCustomTemplateDialog,
    TemplateDeleteConfirmDialog,
    TemplateViewDetailDialog,
    TemplateActiveEmptyState,
    TemplateAssignDefaultDialog,
    Pagination,
    TemplateDetailView
  },
  mixins: [handleNetworkStatusError, projectStatus, handleLoading],

  data() {
    return {
      projectKey: this.$route.params.key,
      handle: this.$route.params.handle,
      templateService: makeTemplateService(this.$api),
      tableLoadingState: false,
      orgService: makeOrgService(this.$api),
      filter: {
        name: '',
        creators: [],
        startDate: null,
        endDate: null,
      },
      createBtnLoading: false,
      headers: [],
      showCreateUpdateCustomTemplateDialog: false,
      showDeleteConfirmDialog: false,
      showViewDetailDialog: false,
      showReassignDialog: false,
      selectedTemplates: {
        uid: '',
        name: '',
        type: '',
        source: '',
        options: [],
        isDefault: false,
      },
      customFieldService: null,
      templateDetails: null,
      isLoading: false,
      errorMessage: '',
      dataSources: [],
      
      templatesData: {
        testCase: [],
        testResult: []
      },
      filteredTemplatesData: {
        testCase: [],
        testResult: []
      },
      totalTemplatesData: {
        testCase: 0,
        testResult: 0
      },

      creators: [],
      currentPage: 1,
      perPage: 10,
      entityType: entityTypeNames.testCase,
      entityTypes: entityTypes,
      isDetailViewCollapsed: false,
      template: null,
    };
  },

  computed: {
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
      currentAccount: 'user/currentAccount'
    }),
    _writeTemplate() {
      return this.authorityTo('write_template');
    },
    _deleteTemplate() {
      return this.authorityTo('delete_template');
    },
    filteredHeaders() {
      const filtered = this.filteredMenuHeaders.filter((header) => header.checked);
      return filtered;
    },
    filteredMenuHeaders() {
      return this.headers.filter((header) => header.text !== 'Actions');
    },
    activeTemplateUid() {
      return this.isDetailViewCollapsed && this.templateDetails ? this.templateDetails.uid : null;
    },
    hasTemplates() {
      return this.totalTemplates > 0;
    },
    templates() {
      return this.templatesData[this.entityType] || [];
    },
    filteredTemplates() {
      return this.filteredTemplatesData[this.entityType] || [];
    },
    defaultFilteredTemplate() {
      return this.filteredTemplates.find((template) => template.isDefault) || {};
    },
    totalTemplates() {
      return this.totalTemplatesData[this.entityType] || 0;
    },
    totalPages() {
      return Math.ceil(this.totalTemplates / this.perPage);
    },
    testFormatsCount() {
      return this.totalTemplatesData?.testCase || 0;
    },
    resultFormatsCount() {
      return this.totalTemplatesData?.testResult || 0;
    },
    hasActiveFilters() {
      return !!(
        this.filter.startDate || 
        this.filter.endDate
      );
    },
    currentTemplateIndex() {
      if (!this.templateDetails) return -1;
      return this.filteredTemplates.findIndex(template => template.uid === this.templateDetails.uid);
    },

    isFirstTemplate() {
      return this.currentTemplateIndex <= 0;
    },

    isLastTemplate() {
      return this.currentTemplateIndex === -1 || this.currentTemplateIndex >= this.filteredTemplates.length - 1;
    },
  },

  watch: {
    'filter.name': {
      handler: _.debounce(function () {
        this.initTemplates(this.entityType);
      }, 500),
    },
  },

  created() {
    if (!this.dynamicHeaders.customTemplates) {
      this.initializeHeaders({ type: 'customTemplates' });
    }
    this.headers = this.dynamicHeaders.customTemplates;
  },

  async mounted() {
    try {
      this.showSkeletonLoader();
      
      await this.fetchTemplates();
      await this.fetchUsers();

      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
  },

  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    updateEntityType(newType) {
      this.entityType = newType;
      this.isDetailViewCollapsed = false;
      if (!this.templatesData[newType] || this.templatesData[newType].length === 0) {
        this.initTemplates(newType);
      }
    },
    async fetchTemplates(){
      try {
        const templatePromises = this.entityTypes.map(entityType => 
          this.initTemplates(entityType)
        );

        const allResults = await Promise.allSettled([
          ...templatePromises,
        ]);

        const templateResults = allResults.slice(0, this.entityTypes.length);
        templateResults.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`Failed to fetch templates for ${this.entityTypes[index]}:`, result.reason);
            showErrorToast(this.$swal, 'fetchError', { 
              item: `${this.entityTypes[index]} templates` 
            }, result.reason?.response?.data);
          }
        });
      } catch (error) {
        console.error('Error fetching templates:', error);
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      }
    },
    async initTemplates(entityType, paginationOptions) {
      const searchParams = new URLSearchParams();
      searchParams.set('current_page', paginationOptions ? paginationOptions.page : this.currentPage);
      searchParams.set('per_page', paginationOptions ? paginationOptions.itemsPerPage : this.perPage);
      searchParams.set('entityType', entityType)

      if (this.filter.name) {
        searchParams.set('name', this.filter.name);
      }

      if (this.filter.startDate) {
        searchParams.set('creationStartDate', this.filter.startDate);
      }

      if (this.filter.endDate) {
        searchParams.set('creationEndDate', this.filter.endDate);
      }

      try {
        this.tableLoadingState = true;
        const response = await this.templateService.getTemplates(
          this.$route.params.handle,
          this.projectKey,
          searchParams.toString()
        );
        
        this.templatesData[entityType] = response.data?.templates;
        this.filteredTemplatesData[entityType] = _.cloneDeep(
          response.data?.templates.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        );
        this.totalTemplatesData[entityType] = Array.isArray(response.data?.templates) ? response.data?.templates?.length : 0;

      } catch (error) {
        this.redirectOnError(error.response?.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } finally {
        this.tableLoadingState = false;
      }
    },
    async fetchUsers() {
      try {
        const response = await this.orgService.getUsers({handle: this.handle});
        this.creators = response.data.users;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'users' }, error?.response?.data);
      }
    },
    async onViewDetails(template) {
      await this.fetchAndViewTemplate(template);
      await this.$nextTick();
      if (this.templateDetails?.uid === template.uid) {
        this.isDetailViewCollapsed = !this.isDetailViewCollapsed;
        return;
      }
      this.isDetailViewCollapsed = true;
      
    },
    onCreateTemplates() {
      this.selectedTemplates = {
        uid: '',
        name: '',
        type: '',
        source: '',
        options: [],
        isDefault: false,
        rules: [],
        customFields: {},
      };

      this.showCreateUpdateCustomTemplateDialog = true;
    },

    async createTemplate(customField) {
      if (!this._writeTemplate) {
        this.unauthorizedToast;
        return;
      }
      try {
        this.showSkeletonLoader();
        this.createBtnLoading = true;
        const payload = {
          name: customField.name,
          templateFields: customField.templateFields || [],
          isDefault: customField.isDefault || false,
          entityType: customField.entityType,
          rules: customField.rules,
        }
        await this.templateService.createTemplate(this.handle, this.projectKey, payload);
        this.showCreateUpdateCustomTemplateDialog = false;
        showSuccessToast(this.$swal, 'createSuccess', { item: 'templates' });
        this.entityType = customField.entityType || this.entityType;
        this.initTemplates(customField.entityType);
      } catch (err) {
        showErrorToast(this.$swal, 'createError', { item: 'templates' }, err?.response?.data);
        this.isLoading = true;
        this.errorMessage = err.message;
      } finally {
        this.createBtnLoading = false;
        this.hideSkeletonLoader();
        this.isDetailViewCollapsed = false;
      }

      this.filterCustomFields();
    },

    onEditTemplate(template) {
      this.selectedTemplates = {
        uid: template.uid || '',
        name: template.name || '',
        dataType: template.dataType || '',
        customFields: template?.customFields || {},
        isDefault: template.isDefault || false,
        entityType: template.entityType || '',
        rules: template.rules || [],
      };
      this.showCreateUpdateCustomTemplateDialog = true;
    },

    onViewPreviousTemplate() {
      if (!this.templateDetails) return;
      
      const currentTemplates = this.filteredTemplates;
      const currentIndex = currentTemplates.findIndex(template => template.uid === this.templateDetails.uid);
      
      if (currentIndex > 0) {
        const previousTemplate = currentTemplates[currentIndex - 1];
        this.fetchAndViewTemplate(previousTemplate);
      }
    },

    onViewNextTemplate() {
      if (!this.templateDetails) return;
      
      const currentTemplates = this.filteredTemplates;
      const currentIndex = currentTemplates.findIndex(template => template.uid === this.templateDetails.uid);
      
      if (currentIndex !== -1 && currentIndex < currentTemplates.length - 1) {
        const nextTemplate = currentTemplates[currentIndex + 1];
        this.fetchAndViewTemplate(nextTemplate);
      }
    },

    async fetchAndViewTemplate(template) {
      try {
        const response = await this.templateService.getTemplate(
          this.handle,
          this.projectKey,
          template.uid
        );
        const result = response.data;
        this.templateDetails = {
          ...result,
          creator: {
            firstName: result?.creator?.firstName || template.creator?.firstName || '',
            lastName: result?.creator?.lastName || template.creator?.lastName || '',
          },
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'template details' }, error?.response?.data);
      }
    },

    async updateTemplate(template) {
      if (!this._writeTemplate) {
        this.unauthorizedToast;
        return;
      }
      try {
        this.showSkeletonLoader();

        const payload = {
          name: template.name,
          templateFields: template.templateFields || [],
          isDefault: template.isDefault || false,
          rules: template.rules?.length > 0 ? template.rules?.map(rule => ({
            ruleId: rule.ruleId,
            statusIds: rule.statusIds,
            name: rule.name,
            manageTags: rule.manageTags || false,
            createDefects: rule.createDefects || false,
           })) : [],
          entityType: template.entityType || this.entityType,
        }

        await this.templateService.updateTemplate(
          this.handle,
          this.projectKey,
          template.uid,
          payload
        );
        await this.initTemplates(this.entityType);
        this.showCreateUpdateCustomTemplateDialog = false;
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'templates' });
      } catch (err) {
        showErrorToast(this.$swal, 'updateError', { item: 'templates' }, err?.response?.data);
        this.isLoading = true;
        this.errorMessage = err.message;
      } finally {
        this.hideSkeletonLoader();
        this.isDetailViewCollapsed = false;
      }

      this.filterCustomFields();
    },

    onDeleteTemplate(template) {
      this.selectedTemplates = {
        uid: template.uid || '',
        name: template.name || '',
        isDefault: template.isDefault || false,
      };

      this.showDeleteConfirmDialog = true;
    },

    async deleteTemplate() {
      this.showDeleteConfirmDialog = false;
      if (!this._deleteTemplate) {
        this.unauthorizedToast;
        return;
      }
      try {
        this.showSkeletonLoader();
        await this.templateService.deleteTemplate(
          this.handle,
          this.projectKey,
          this.selectedTemplates.uid
        );
        await this.initTemplates(this.entityType);
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'templates' });
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'templates' }, err?.response?.data);
        this.isLoading = true;
        this.errorMessage = err.message;
      } finally {
        this.hideSkeletonLoader();
        this.isDetailViewCollapsed = false;
      }
    },

    onMakeDefault(template) {
      if (!this._writeTemplate) {
        this.unauthorizedToast;
        return;
      }
      this.updateTemplateDefault(template);
    },

    onReassignDefault(template) {
      if (!this._writeTemplate) {
        this.unauthorizedToast;
        return;
      }
      this.selectedTemplates = template;
      this.showReassignDialog = true;
    },

    async reassignDefault(updatedTemplate) {
      if (!this._writeTemplate) {
        this.unauthorizedToast;
        return;
      }
      try {

        const payload = {
          ...updatedTemplate,
          entityType: updatedTemplate.entityType || this.entityType,
        }

        await this.templateService.updateTemplate(
          this.handle,
          this.projectKey,
          updatedTemplate.uid,
          payload
        );

        await this.initTemplates(this.entityType);
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'Default template reassigned' });
      } catch (err) {
        showErrorToast(this.$swal, 'updateError', { item: 'templates' }, err?.response?.data);
      }
    },

    async updateTemplateDefault(template) {
      try {

       const customTemplate = {
        uid: template.uid || '',
        name: template.name || '',
        customFields: {
          templateFields: template.customFields?.templateFields || []
         },
        templateFields: template.customFields?.templateFields || [],
        isDefault: true,
        entityType: template.entityType || this.entityType,
      };

        await this.templateService.updateTemplate(
          this.handle,
          this.projectKey,
          template.uid,
          customTemplate
        );

        await this.initTemplates(this.entityType);
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'Default template' });
      } catch (err) {
        showErrorToast(this.$swal, 'updateError', { item: 'templates' }, err?.response?.data);
      } 
    },

    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
        this.initTemplates(this.entityType, { page: newPage, itemsPerPage: newItemsPerPage });
      }
    },

    updateFilterCondition(data) {
      this.filter = {
        ...this.filter,
        creators: data.creators,
        startDate: data.startDate,
        endDate: data.endDate,
      };

      this.filterCustomFields();
    },

    filterCustomFields() {
        const currentEntityTemplates = this.templatesData[this.entityType] || [];
        let filteredTemplates = _.cloneDeep(currentEntityTemplates);

        if (this.filter.name) {
          filteredTemplates = filteredTemplates.filter((item) =>
            item.name.toLowerCase().includes(this.filter.name.toLowerCase())
          );
        }

        if (this.filter.creators && this.filter.creators.length > 0) {
          filteredTemplates = filteredTemplates.filter((item) => {
            return this.filter.creators.includes(item.creator.uid);
          });
        }

        if (this.filter.endDate) {
          const filterDate = this.filter.endDate;
          filteredTemplates = filteredTemplates.filter((item) => {
            const itemDate = new Date(item.createdAt).toISOString().split('T')[0];
            return itemDate <= filterDate;
          });
        }

        if (this.filter.startDate) {
          const filterDate = this.filter.startDate;
          filteredTemplates = filteredTemplates.filter((item) => {
            const itemDate = new Date(item.createdAt).toISOString().split('T')[0];
            return itemDate >= filterDate;
          });
        }

        this.filteredTemplatesData[this.entityType] = filteredTemplates;
      },

  },
};
</script>