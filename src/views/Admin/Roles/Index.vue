<template>
  <div class="pl-3 pt-3">
    <RoleHeader
      :write-role="showCreateRole"
      :read-role="_readRole"
      :roles-level="rolesLevel"
      :org-roles-count="orgRolesCount"
      :project-roles-count="projectRolesCount"
      @create-new-role="onCreateRole"
      @update-roles-level="updateRolesLevel"
    />
    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <template>
        <v-row
          justify="space-between"
          class="align-center"
        >
          <v-col
            cols="12"
            md="auto"
            class="d-flex align-center"
          >
            <SearchComponent
              :search="filter.name"
              :placeholder="$t('placeHolder.searchByName')"
              @update:search="onSearch"
            />
          </v-col>
        </v-row>

        <EmptyRole
          v-if="scopeRolesCount === 0 && !skeletonLoaderState"
          :write-role="_writeRole"
          :role-level="rolesLevel"
          @create-new-role="onCreateRole"
        />

        <template v-else-if="skeletonLoaderState">
          <v-row
            justify="space-between"
            class="align-center mb-4"
          >
            <v-col
              cols="12"
              md="auto"
              class="d-flex align-center"
            >
              <v-skeleton-loader
                type="button"
                height="40"
                width="300"
              />
            </v-col>
          </v-row>
          <RoleTableSkeleton />
        </template>

        <template v-else>
          <RoleTable
            :headers="filteredHeaders"
            :items="originalRoles"
            item-key="uid"
            :clear-selection="clearSelection"
            :delete-role="_deleteRole"
            @edit="onEditRole"
            @delete="onDeleteRole"
            @select-row="handleRowClick"
            @select-item="setselected"
          />
          <v-row
            v-if="selectedItems.length > 0"
            justify="end"
            class="action-btn-wrapper px-6 py-4"
          >
            <v-tooltip
              bottom
              :disabled="allRolesDeletable && !rolesIncludeSystem"
            >
              <template #activator="{ on }">
                <div
                  style="display: inline-block"
                  v-on="on"
                >
                  <v-btn
                    width="140"
                    color="danger"
                    :class="{ 'text-capitalize rounded-lg mr-3 white--text btn-theme': true, 'disabled-action': !allRolesDeletable || rolesIncludeSystem }"
                    depressed
                    @click="onDeleteRole({}, true)"
                  >
                    {{ $t('delete') }}
                  </v-btn>
                </div>
              </template>
              <span v-if="rolesIncludeSystem">{{ $t('systemRoleSelected') }}</span>
              <span v-else>{{ $t('roleHasAssignees') }}</span>
            </v-tooltip>
          </v-row>
          <Pagination
            v-if="scopeRolesCount > 0"
            :page="currentPage"
            :total-pages="totalPages"
            :items-per-page="perPage"
            :total-items="scopeRolesCount"
            @update:pagination="onUpdatePagination"
          />
        </template>
      </template>
      <RoleDeleteConfirmDialog
        v-model="showDeleteConfirmDialog"
        :role="selectedRole"
        :roles="selectedItems"
        @delete-role="deleteRole"
        @delete-roles="removeRoles"
        @close="handleCloseClick"
      />

      <ReassignConfirmDialog
        v-model="showReassignConfirmDialog"
        :role="selectedRole"
        @delete-role="deleteRole"
        @close="handleCloseClick"
      />

      <RoleReassignMemberDialog
        v-model="showRoleDeleteWithMembersDialog"
        :role="selectedRole"
        :available-roles="availableRoles"
        @reassign-role="onReassignRole"
        @close="handleCloseClick"
      />
    </v-card>
  </div>
</template>

<script>
import { debounce } from 'debounce';
import { createNamespacedHelpers } from 'vuex';

import makeTagService from '@/services/api/tag';
import makeRoleService from '@/services/api/role';
import RoleHeader from '@/components/Admin/Role/RoleHeader';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import RoleTable from '@/components/Admin/Role/Table.vue';
import RoleDeleteConfirmDialog from '@/components/Admin/Role/DeleteConfirmDialog.vue';
import ReassignConfirmDialog from '@/components/Admin/Role/ReassignConfirmDialog.vue';
import RoleReassignMemberDialog from '@/components/Admin/Role/RoleReassignMemberDialog.vue';
import EmptyRole from '@/components/Admin/Role/EmptyRole.vue';
import RoleTableSkeleton from '@/components/Skeletons/Role/RoleTableSkeleton.vue';
import { handleNetworkStatusError } from '@/mixins/redirect';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import handleLoading from '@/mixins/loader.js'
import Pagination from '@/components/base/Pagination.vue';
import makeProjectService from '@/services/api/project'

const { mapState, mapActions, mapGetters } = createNamespacedHelpers('user');

export default {
  name: 'Roles',

  components: {
    RoleHeader,
    SearchComponent,
    RoleTable,
    RoleDeleteConfirmDialog,
    ReassignConfirmDialog,
    RoleReassignMemberDialog,
    Pagination,
    EmptyRole,
    RoleTableSkeleton,
  },
  mixins: [handleNetworkStatusError, handleLoading],

  data() {
    return {
      filter: {
        name: '',
      },
      filteredRoles: [],
      originalRoles: [],
      isLoading: false,
      errorMessage: '',
      showReassignConfirmDialog: false,
      showDeleteConfirmDialog: false,
      showRoleDeleteWithMembersDialog: false,
      clearSelection: false,
      availableRoles: [],
      selectedRole: {
        uid: '',
        name: '',
        description: '',
        users: [],
      },
      selectedItems: [],
      rolesLevel: 'org',
      orgRolesCount: 0,
      projectRolesCount: 0,
      scopeRolesCount: 0,
      currentPage: 1,
      perPage: 10,
      scopedAccess: {},
    }
  },

  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters(['isToastDismissed', 'dismissedToasts']),
    headers(){
      return [
        {
          text: this.$t('name'),
          align: 'start',
          sortable: true,
          value: 'name',
          class: 'elevation-0 rounded-l-lg',
          checked: true,
        },
        ...(this.rolesLevel == 'project' ? [
        {
          text: this.$t('project'),
          align: 'start',
          sortable: true,
          value: 'project',
          class: 'elevation-0 rounded-l-lg',
          checked: true,
        },
        ] : []),
        {
          text: this.$t('description'),
          value: 'description',
          sortable: true,
          checked: true,
        },
        {
          text: this.$t('rolePage.users'),
          value: 'users',
          sortable: true,
          checked: true
        },
        {
          text: '',
          value: 'uid',
          sortable: false,
          class: 'rounded-r-lg',
          width: 60,
          checked: true
        },
      ]
    },
    allRolesDeletable(){
      return this.selectedItems?.every(role => role.assignees?.length === 0)
    },
    rolesIncludeSystem(){
      return this.selectedItems?.some(role => role.system)
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.value != 'actions');
      return filtered;
    },
    filteredHeaders() {
      const filtered = this.filteredMenuHeaders.filter((header) => header.checked);
      return filtered;
    },
    showCreateRole(){
      return !!(this.scopedAccess['write_role']?.org || this.scopedAccess['write_role']?.projects.length)
    },
    _writeRole(){
      return this.authorityTo('write_role')
    },
    _readRole(){
      return this.authorityTo('read_role')
    },
    _deleteRole(){
      return this.authorityTo('delete_role')
    },
    hasRoles() {
      return this.originalRoles.length > 0
    },
    isRoleHasMembers() {
      return this.selectedRole?.members?.length > 0
    },
    getSelectedMembersCount() {
      return this.selectedItems?.length
    },
    selectedItemsExist() {
      return this.selectedItems.length > 0
    },
    getSelectedRoleIds() {
      return this.selectedItems.map(item => item.uid)
    },
    getSelectedItemsNoMembersCount() {
      return this.selectedItems.filter(item => item.members.length === 0).length;
    },
    getSelectedItemsNoMembersHasData() {
      return this.getSelectedItemsNoMembersCount > 0;
    },
    isSelectedItemsContainsMembers() {
      return this.selectedItems.some(item => item.members.length > 0)
    },
    isProjectRole(){
      return this.currentAccount.type == 'org' && this.$route.params.key;
    },
    totalPages() {
      return Math.ceil(this.scopeRolesCount / this.perPage);
    },
    hasDismissedDeleteRoleToast(){
      return this.isToastDismissed('deleteRoleToast');
    }
  },

  mounted() {
    let handle = this.$route.params.handle;
    
    // Check if we need to show a specific role level from query params
    if (this.$route.query.tab && !this.isProjectRole) {
      this.rolesLevel = this.$route.query.tab;
      // Clear the query parameter to keep URL clean
      this.$router.replace({ name: this.$route.name, params: this.$route.params });
    }
    
    // If user doesn't have read_role permission, force project level
    if (!this._readRole && !this.isProjectRole) {
      this.rolesLevel = 'project';
      this.$router.replace({ 
        name: this.$route.name, 
        params: this.$route.params,
        query: { level: 'project' }
      });
    } else if (!this.isProjectRole && !this.$route.query.level) {
      // Add level query param if not present and on admin page
      this.$router.replace({ 
        name: this.$route.name, 
        params: this.$route.params,
        query: { level: 'organization' }
      });
    }
    
    this.init(handle);
  },
  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      next();
    }
  },

  methods: {
    ...mapActions(['setDismissedToasts']),
    async init(handle) {
      try {
        this.showSkeletonLoader();
        await this.getPermissionScope(handle);
        await this.getRoles(handle);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async updateRolesLevel(level) {
      // Prevent switching to org level if user doesn't have read_role permission
      if (level === 'org' && !this._readRole) {
        return;
      }
      
      this.rolesLevel = level;
      this.currentPage = 1; // Reset to first page when changing level
      
      // Update query param
      const query = level === 'org' ? { level: 'organization' } : { level: 'project' };
      this.$router.replace({ 
        name: this.$route.name, 
        params: this.$route.params,
        query
      });
      
      this.showSkeletonLoader();
      await this.getRoles(this.currentAccount.handle);
      this.hideSkeletonLoader();
    },
    async getProjects(handle) {
      if (this.isProjectRole) return; 
      
      const projectService = makeProjectService(this.$api);
      try {
        const params = {
          status: 'active',
        };
        const queryString = new URLSearchParams(params).toString();
        const response = await projectService.getProjects(handle, queryString);
        this.projects = response.data?.items || [];
      } catch (err) {
        console.error('Error fetching projects:', err);
      }
    },
    onProjectSelect(project) {
      this.selectedProject = project;
      this.currentPage = 1;
      this.getRoles(this.currentAccount.handle);
    },
    async getRoles(handle, offset, q) {
      const roleService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;

      try {
        const params = {
         ...(this.rolesLevel === 'project' ? { includeProjectLevelRoles: true} : {}),
         ...(!this.isProjectRole ? { includeRoleCounts: true } : {}),
         includeUsers: true,
         limit: this.perPage,
         offset: offset,
         ...(q ? { q } : {}),
        }
        const response = await roleService.getRoles(handle, projectKey, params);
        const roles = response.data?.items;
        this.orgRolesCount = response.data?.orgRolesCount || 0;
        this.projectRolesCount = response.data?.projectRolesCount || 0;
        this.originalRoles = roles.map(r => ({
          ...r,
          writeAccess: r.projectUid ? this.scopedAccess['write_role'].projects.includes(r.projectUid) : this.scopedAccess['write_role'].org,
          deleteAccess: r.projectUid ? this.scopedAccess['delete_role'].projects.includes(r.projectUid) : this.scopedAccess['delete_role'].org,
        }))
        this.scopeRolesCount = response?.data?.count || 0;

      } catch (err) {
        this.redirectOnError(err.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, err?.response?.data);
      }
    },
    async removeRoles(dontShowAgainToast, roleId) {
      const roleService = makeRoleService(this.$api);
      try {
        const handle = this.$route.params.handle;
        this.showSkeletonLoader();
        const payload = {
          roleIds: roleId ? [roleId] : this.selectedItems.map(role => role.uid)
        }
        await roleService.deleteRoles(handle, payload);
        
        // Remove deleted roles from the DOM
        this.originalRoles = this.originalRoles.filter(
          role => !payload.roleIds.includes(role.uid)
        );

        this.scopeRolesCount -= payload.roleIds.length;

        if (this.roleLevel === 'project') {
          this.projectRolesCount -= payload.roleIds.length;
        } else {
          this.orgRolesCount -= payload.roleIds.length;
        }

        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'roles' });
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'roles' }, err?.response?.data);
      } finally {
        if(dontShowAgainToast){
          this.setDismissedToasts('deleteRoleToast');
        }
        this.selectedItems = []
        this.hideSkeletonLoader();
        this.showDeleteConfirmDialog = false;
      }
    },
    handleRowClick(item) {
      const name = this.isProjectRole ? `ProjectRolesMember` : `AdminRolesMember`;
      this.$router.push({
        name,
        params: {
          id: item.uid,
          handle: this.$route.params.handle,
        }
      });
    },
    handleCloseClick() {
      this.showDeleteConfirmDialog = false;
      this.clearSelection = true;
      this.selectedRole = null;
    },
    setselected(selectedItems) {
      this.clearSelection = false
      this.selectedItems = selectedItems
    },
    onSearch: debounce(async function(q){
      this.currentPage = 1; // Reset to first page when searching
      this.filter.name = q; // Store the search query
      await this.getRoles(this.currentAccount.handle, 0, q)
    }, 500),
    
    onCreateRole() {
      const name = this.isProjectRole ? `ProjectRoleCreate` : `AdminRoleCreate`;
      const routeConfig = {
        name,
        params: {
          handle: this.$route.params.handle,
        }
      };
      
      // For admin routes, pass the current tab as query parameter
      if (!this.isProjectRole) {
        routeConfig.query = { level: this.rolesLevel === 'project' ? 'project' : 'organization' };
      }
      
      this.$router.push(routeConfig);
    },

    async createRole(tag) {

      const tagService = makeTagService(this.$api);
      this.isLoading = true
      try {
        const response = await tagService.createRole(this.currentAccount.handle, tag);
        this.originalRoles.push(response.data)
      } catch (err) {
        this.isLoading = true
        this.errorMessage = err.message;
      }
    },

    onEditRole(role) {
      const name = this.isProjectRole ? `ProjectRoleEdit` : `AdminRoleEdit`;
      const routeConfig = {
        name,
        params: {
          id: role?.uid,
          handle: this.$route.params.handle,
        }
      };
      
      if (!this.isProjectRole) {
        routeConfig.query = { level: this.rolesLevel === 'project' ? 'project' : 'organization' };
      }
      
      this.$router.push(routeConfig);
    },

    async getPermissionScope(handle){
      const roleService = makeRoleService(this.$api);
      const [writeRoleResponse, deleteRoleResponse ] = await Promise.all([
        roleService.getPermissionScope(handle, 'write_role'),
        roleService.getPermissionScope(handle, 'delete_role')
      ]);

      this.scopedAccess = {
        write_role: writeRoleResponse.data,
        delete_role: deleteRoleResponse.data
      }
    },

    async onDeleteRole(role, bulkRemove = false) {
      if(bulkRemove && this.hasDismissedDeleteRoleToast)
        return this.removeRoles(true);
      if(bulkRemove)
        return this.showDeleteConfirmDialog = true;

      if(this.hasDismissedDeleteRoleToast){
        this.deleteRole(true);
        return;
      }
      this.selectedRole = role;
      if (role?.assignees?.length > 0 && this.rolesLevel === 'org') {
        await this.getAvailableRoles();
        this.showRoleDeleteWithMembersDialog = true;
      } else {
        this.showDeleteConfirmDialog = true;
      }
    },

    async deleteRole(dontShowAgainToast) {
      try {
        this.showSkeletonLoader();
        if (this.isRoleHasMembers) {
          const name = this.isProjectRole ? `ProjectRolesMember` : `AdminRolesMember`;
          this.$router.push({
            name,
            params: {
              id: this.selectedRole.uid,
              handle: this.$route.params.handle,
            }
          });
        } else {
          await this.removeRoles(dontShowAgainToast, this.selectedRole.uid);
          this.showDeleteConfirmDialog = false;
          await this.getRoles(this.currentAccount.handle);

          if(dontShowAgainToast)
          this.setDismissedToasts('deleteRoleToast');
        }
      } catch (err) {
        console.error('Error deleting role:', err);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      this.currentPage = newPage;
      if (newItemsPerPage !== this.perPage) {
        this.perPage = newItemsPerPage;
        this.currentPage = 1; 
        this.perPage = newItemsPerPage;
      }
      const offset = (newPage - 1) * newItemsPerPage;  
      this.showSkeletonLoader();
      try {
        await this.getRoles(this.currentAccount.handle, offset, this.filter.name);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async getAvailableRoles() {
      const roleService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      try {
        const response = await roleService.getRoles(this.currentAccount.handle, projectKey);
        this.availableRoles = response.data?.items?.filter(role => role.uid !== this.selectedRole.uid) || [];
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, err?.response?.data);
      }
    },

    async onReassignRole(targetRole) {
      const roleService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      
      try {
        this.showSkeletonLoader();
        
        const members = this.selectedRole.assignees.map(assignee => ({
          userId: assignee.uid,
          roleId: this.selectedRole.uid
        }));

        await roleService.reAssignRole({
          handle: this.currentAccount.handle,
          roleId: targetRole.uid,
          payload: { members },
          projectKey
        });

        await this.getRoles(this.currentAccount.handle);
        
        showSuccessToast(this.$swal, this.$t('rolePage.roleReassignSuccess'));
        
        this.showRoleDeleteWithMembersDialog = false;
        this.showDeleteConfirmDialog = true;
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'role' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
  }
}
</script>
