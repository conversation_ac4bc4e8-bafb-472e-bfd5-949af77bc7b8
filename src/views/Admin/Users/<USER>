<template>
  <div
    class="pl-3"
  >
    <UserHeader
      :filter="filter"
      :roles="roles"
      :active-user-count="activeUserCount"
      :pending-user-count="pendingUsersCount"
      :requested-user-count="requestedUserCount"
      :expired-user-count="expiredUserCount"
      :write-member="_writeMember"
      :tags="tags"
      :project-scope="projectScope"
      :projects="projects"
      :users="orgScopeInvitedMembers"
      @update-filter="updateFilter"
      @showError="showError"
      @completed="inviteCompleted"
      @update-tags="fetchTags"
    />
    
    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center"
        >
          <SearchComponent 
            class="mr-3"
            @update:search="setSearchFilter" 
          />
          <ProjectUserFilter
            :available-projects="projects"
            :available-roles="roles"
            :available-tags="tags"
            @filters="handleFilters"
          />
        </v-col>
        <v-col
          cols="12"
          md="auto"
        >
          <v-row
            justify="end"
            class="align-center"
          >
            <SettingsMenu 
              table-type="users" 
            />
          </v-row>
        </v-col>
      </v-row>
      <UserTable
        :filtered-headers="filteredHeaders"
        :items="paginatedItems"
        :item-key="itemKey"
        :roles="roles"
        :tags="tags"
        :write-member="_writeMember"
        :delete-member="_deleteMember"
        :filter="filter"
        :is-tag-loading="isTagLoading"
        @update-role="confirmRoleUpdate"
        @update-tag="updateTag"
        @delete-item="onDelete"
        @edit-item="onEdit"
        @resend-invite="onResendInvite"
        @refreshTags="fetchTags"
      />

      <Pagination
        v-if="totalFilteredItems > 0"
        v-model="currentPage"
        :total-pages="totalPages"
        :items-per-page="perPage"
        :total-items="totalFilteredItems"
        @update="onUpdatePagination"
      />
    </v-card>
    <DiscardDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      @close="onDialogClose"
      @handleConfirmClick="onConfirm"
    >
      <template #content>
        <v-flex class="mt-4">
          <p class="text-start">
            {{ dialogDescription }}
          </p>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              width="100%"
              class="text-capitalize fw-semibold rounded-lg black--text mt-2"
              color="#F2F4F7"
              elevation="0"
              height="40"
              @click="dialogVisible = false"
            >
              {{ $t('discard_cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              width="100%"
              class="text-capitalize fw-semibold rounded-lg white--text mt-2"
              color="danger"
              height="40"
              @click="onConfirm"
            >
              {{ $t('discard_confirm') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </DiscardDialog>
    <UpdateDialog
      v-if="isOpenUpdateDialog"
      v-model="isOpenUpdateDialog"
      :selected-user="selectedUser"
      :filter="filter"
      :roles="roles"
      :projects="projects"
      :tags="tags"
      @closeDialog="handleCloseUpdateDialog"
      @clickSave="confirmUserUpdate"
    />
    <ReassignOwnerConfirmDialog 
      v-if="confirmOwnershipChange"
      v-model="confirmOwnershipChange"
      :is-new-owner-assignment="isNewOwnerAssignment"
      @updateRole="onConfirmReassign"
      @close="handleReassignOwnerDialogClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions as projectMapActions } from 'vuex';
import makeRoleService from '@/services/api/role';
import makeOrgService from '@/services/api/org';
import makeProjectService from '@/services/api/project';
import makeTagService from '@/services/api/tag';
import makeInviteService from '@/services/api/invite'
import makeUserService from '@/services/api/user';
import UserHeader from '@/components/User/UserHeader';
import ProjectUserFilter from '@/components/Project/ProjectUserFilter.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import UserTable from '@/components/User/UserTable.vue';  
import UpdateDialog from '@/components/User/UpdateDialog.vue';  
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import DiscardDialog from '@/components/base/DiscardDialog.vue';
import { users } from '@/constants/data.js';
import { handleNetworkStatusError } from '@/mixins/redirect';
import handleLoading from '@/mixins/loader.js'
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import ReassignOwnerConfirmDialog from '@/components/Admin/Role/ReassignOwnerConfirmDialog.vue';
import Pagination from '@/components/base/Pagination.vue';


export default {
  name: 'Users',
  components: {
    UserHeader,
    ProjectUserFilter,
    SearchComponent,
    UserTable,
    DiscardDialog,
    SettingsMenu,
    UpdateDialog,
    Pagination,
    ReassignOwnerConfirmDialog
  },
  mixins: [handleNetworkStatusError, handleLoading],
  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        this.activeMembers = [];
        this.roles = [];
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      next();
    }
  },
  data() {
    return {
      isOpenUpdateDialog: false,
      selectedUser: null,
      dialogVisible: false,
      dialogTitle: '',
      dialogDescription: '',
      currentItem: null,
      membersState: {
        isLoading: false,
        hasError: false,
        errorMessage: '',
      },
      currentDate: new Date(),
      activeMembers: [],
      requestedUsers: [],
      activeUserCount: 0,
      users: users,
      pendingUserCount: 0,
      requestedUserCount: 0,
      items: [],
      roles: [],
      projects: [],
      tags: [],
      filter: 'active',
      itemKey: 'uid',
      searchFilter: '',
      headers: [],
      filteredItems: [], // New data property to store filtered items
      pendingInvites: [],
      expiredInvites: [],
      orgService: null,
      inviteService: null,
      currentPage: 1,
      perPage: 10,
      userService: null,
      isTagLoading: false,
      confirmOwnershipChange: false,
      selectedRole: null,
      selectedUserId: null,
      userData: null,
    };
  },
  computed: {
    ...mapGetters(['isMenuCollapsed']),
    ...mapGetters({
      currentAccount: 'user/currentAccount',
      dynamicHeaders: 'headers/dynamicHeaders',
      currentUser: 'user/user'
    }),
    _readRole(){
      return this.authorityTo('read_role')
    },
    _writeMember(){
      return this.authorityTo('write_member')
    },
    _deleteMember(){
      return this.authorityTo('delete_member')
    },
    ...mapGetters({
      holderAccount: 'user/user',
    }),
    pendingUsers() {
      return this.pendingInvites.filter(item => new Date(item?.expiresAt) > this.currentDate);
    },
    expiredUsers() {
      return this.expiredInvites || [];
    },
    expiredUserCount() {
      return this.expiredUsers.length;
    },
    pendingUsersCount() {
      return this.pendingUsers.length;
    },
    filteredItem() {
      let items;
      if (this.filteredItems.length > 0) {
        items = this.filteredItems;
      } else {
        items = this.getFilteredMembers();
        if (this.searchFilter.length > 0) {
          items = this.applySearchFilter(items);
        }
      }
      return this.addProjects(items);
    },
    totalFilteredItems() {
      return this.filteredItem.length;
    },
    totalPages() {
      return Math.ceil(this.totalFilteredItems / this.perPage);
    },
    paginatedItems() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.filteredItem.slice(start, end);
    },
    orgScopeInvitedMembers(){
      const orgRoles = this.roles.map(role => role.uid);
      return this.activeMembers.filter(member => {
        return member.role && orgRoles.includes(member.role.uid);
      });
    },
    filteredHeaders() {
      const filtered = this.filteredMenuHeaders.filter((header) => {
        if (this.filter !=='pending' && header.value ==='createdAt'){
          return false;
        }
        if((this.projectScope && header.value === 'project') || (this.filter !== 'active' && header.value === 'project')){
          return false;
        }
        if (this.filter === 'expired' && header.value === 'lastActivity') {
          return false;
        }
        if (this.filter !== 'pending' && header.value === 'status') {
          return false;
        }
        return header.checked;
      });
      return filtered;
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.text != 'Actions');
      return filtered;
    },
    getActiveUserCount() {
      return this.activeMembers.length;
    },
    projectScope(){
      return !!this.$route.params.key;
    }
  },
  mounted() {
    let handle = this.$route.params.handle;
    this.init(handle);
  },
  created() {
    this.orgService = makeOrgService(this.$api);
    this.inviteService = makeInviteService(this.$api);
    this.userService = makeUserService(this.$api);
    if(!this.dynamicHeaders.users) {
      this.initializeHeaders({ type: 'users' });
    }
    this.headers = this.dynamicHeaders.users;
  },
  methods: {
    ...projectMapActions("headers", ['initializeHeaders']),
    handleFilters(filters) {
      this.applyFilters(filters);
    },

    applyFilters(filters) {
      this.filteredItems = this.getFilteredMembers().filter(user => {
        const projectMatch = filters.projects.length === 0 || 
          (user.projects && filters.projects.some(p => user.projects.includes(p.id)));
        const roleMatch = filters.roles.length === 0 || 
          (user.role && filters.roles.some(r => r.id === user.role));
        const tagMatch = filters.tags.length === 0 || 
          (user.tags && filters.tags.some(t => user.tags.includes(t.id)));
        return projectMatch && roleMatch && tagMatch;
      });
      // Reset to first page when applying filters
      this.currentPage = 1;
    },

    updateFilter(newFilter) {
      this.filter = newFilter;
      this.filteredItems = []; // Reset filtered items when filter changes
      // Reset to first page when filter changes
      this.currentPage = 1;
    },
    setSearchFilter(searchText) {
      this.searchFilter = searchText;
      this.filteredItems = []; // Reset filtered items when search changes
      // Reset to first page when search changes
      this.currentPage = 1;
    },
    getFilteredMembers() {
      switch (this.filter) {
        case 'active':
          return this.activeMembers;
        case 'pending':
          return this.pendingUsers;
        case 'requested':
          return this.requestedUsers;
        default:
          return this.expiredUsers;
      }
    },
    applySearchFilter(members) {
      return members.filter((item) => this.matchesFilter(item));
    },
    addProjects(items){
      return items.map((item) => {
        return {...item, projects: this.projects.filter(proj => proj.members?.map(m => m.uid).includes(item.uid))}
      });
    },
    matchesFilter(item) {
      const lowerCaseFilter = this.searchFilter.toLowerCase();
      const name = `${item.firstName} ${item.lastName}`;

      const nameMatch = name.toLowerCase().includes(lowerCaseFilter);

      return nameMatch;
    },

    async showError(errorMessage) {
      showErrorToast(this.$swal, 'genericError', { message: errorMessage });
    },
    async inviteCompleted() {
      try {
        this.showSkeletonLoader();
        showSuccessToast(this.$swal, 'inviteSuccess');
        await this.getPendingInvites(this.currentAccount.handle);
      } catch (error) {
        showErrorToast(this.$swal, 'inviteError', { error: error.message }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    onDelete(item) {
      this.currentItem = item;
      if (this.filter === 'active') {
        this.dialogTitle = this.$t('user_remove_title');
        this.dialogDescription = this.$t('user_remove_description', { projectName: 'TestFiesta' });
      } else if (this.filter === 'pending') {
        this.dialogTitle = this.$t('invite_discard_title');
        this.dialogDescription = this.$t('invite_discard_description');
      } else if (this.filter === 'expired') {
        this.dialogTitle = this.$t('request_delete_title');
      } else {
        this.dialogTitle = this.$t('request_decline_title');
      }
      this.dialogVisible = true;
    },
    onEdit(item) {
      this.selectedUser = item;
      this.isOpenUpdateDialog = true;
    },
    async onResendInvite(item) {
      try {
        this.showSkeletonLoader();
        const projectKey = this.$route.params.key;
        await this.inviteService.resendInvite({handle: this.$route.params.handle, emails: [item.email], projectKey});
        showSuccessToast(this.$swal, 'resendSuccess', { item: 'invite' });
      } catch (error) {
        showErrorToast(this.$swal, 'resendError', { item: 'invite' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    onConfirmReassign(password){
      if (this.userData) {
        this.updateUser(this.userData, password);
      } else {
        this.updateRole(this.selectedRole, this.selectedUserId, password);
      }
    },
    handleReassignOwnerDialogClose(){
      this.confirmOwnershipChange = false;  
      this.userData = null;
      this.selectedRole = null;
      this.selectedUserId = null;
      this.isNewOwnerAssignment = null;
    },
    async confirmUserUpdate(data){
      const isSelfReassignment = data?.currentRole?.toLowerCase() === 'owner' && this.selectedUser?.uid === this.currentUser?.uid;
      const isAssigningNewOwner = data?.assignedRole?.toLowerCase() === 'owner';

      if (isSelfReassignment || isAssigningNewOwner) {
        this.confirmOwnershipChange = true;
        this.userData = data;
        this.isNewOwnerAssignment = isAssigningNewOwner;
      } else {
        await this.updateUser(data);
      }
    },
    async confirmRoleUpdate(role, userId){
      const isOwnerRole = role.name?.toLowerCase() === 'owner';
      const isSelfReassignment = role.currentRoleName?.toLowerCase() === 'owner' && userId === this.currentUser.uid;

      if (isSelfReassignment) {
        this.isNewOwnerAssignment = false;
      } else if (isOwnerRole) {
        this.isNewOwnerAssignment = true;
      }
      
      if (isSelfReassignment || isOwnerRole) {
        this.confirmOwnershipChange = true;
        this.selectedRole = role;
        this.selectedUserId = userId;
      } else {
        await this.updateRole(role, userId);
      }
    },
    async updateRole(role, userId, password){
      const payload = {
        roleId: role.uid,
        members: [{
          userId
        }],
        ...( password ? {password}: {}) 
      }
      const roleMembersService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      await roleMembersService.reAssignRole({handle: this.currentAccount.handle, roleId: role.uid, payload, projectKey}).then(() => {
          const findIndex = this.activeMembers.findIndex(element => element.uid == userId)
          this.activeMembers.splice(findIndex,1, {...this.activeMembers[findIndex], role: {...role, projects: this.activeMembers[findIndex].role.projects} })
          showSuccessToast(this.$swal, this.$t('reassignToRole', {userCount: 1, newRole: role.name}))
      }).catch((err) => {
          showErrorToast(this.$swal, this.$t('unableToReassign'), {}, err?.response?.data)   
      })
      this.confirmOwnershipChange = false;
      this.selectedRole = null;
      this.selectedUserId = null;
    },
    async updateUser(data, password) {
      this.isOpenUpdateDialog = false;

      const payload = {
        ...(this.selectedUser?.uid ? {members: [{userId: this.selectedUser.uid}]} : {}),
        roleUid: data.selectedRole.uid ? data.selectedRole.uid : data.selectedRole,
        tagUids: data.tagsData.selectedTags.map(tag => tag?.uid),
        tagReplacements: [
          {
            existingTagUids: data.tagsData.replaceTag.map(tag => tag?.uid),
            newTagUids: data.tagsData.withTag.map(tag => tag?.uid)
          }
        ],
        userUids: [this.selectedUser?.uid],
        ...(password ? { password } : {}),
        email: this.selectedUser?.email,
        overriddenRoles: data.assignedProjects?.map((p) => {
          return {
            projectUid: p.projectUid,
            roleUid: p.roleUid
          }
        }) ?? []
      };

      if (this.filter === 'pending') {
        const inviteService = makeInviteService(this.$api);
        await inviteService.updatePendingInvites({handle: this.$route.params.handle, updates: [payload]}).then(() => {
          showSuccessToast(this.$swal, this.$t('inviteUpdated', {email: this.selectedUser.email}))
          this.getPendingInvites(this.currentAccount.handle); // Refresh pending invites
        }).catch((err) => {
          showErrorToast(this.$swal, this.$t('unableToUpdateInvite'), {}, err?.response?.data)   
        })
      } else {
        try {
          const roleMembersService = makeRoleService(this.$api);
          await roleMembersService.reAssignRole({handle: this.currentAccount.handle, roleId: payload.roleUid, payload}).then(() => {
              showSuccessToast(this.$swal, this.$t('userRolesUpdated'))
          }).catch((err) => {
              showErrorToast(this.$swal, this.$t('unableToReassign'), {}, err?.response?.data)   
          })
          this.init(this.$route.params.handle); // Refresh user list
        } catch (error) {
          showErrorToast(this.$swal, 'updateError', { item: 'user' }, error?.response?.data);
        } finally{
          this.confirmOwnershipChange = false;
          this.userData = null;
        }
        
      
      }
    },
    async updateTag(data) {
      try {
        this.isTagLoading = true;
        await this.userService.updateUsers( this.currentAccount.handle, data);
        showSuccessToast(this.$swal, this.$t('success.tagsUpdated'));
        await this.getMembers(30, 0, this.$route.params.handle)
      } catch (error) {
        showErrorToast(this.$swal, 'updateError', { item: 'user tags' }, error?.response?.data);
      } finally {
        this.isTagLoading = false;
      }
    },
    handleCloseUpdateDialog() {
      this.isOpenUpdateDialog = false;
      this.selectedUser = null;
    },
    onDialogClose() {
      this.dialogVisible = false;
    },
    async onConfirm() {
      this.showSkeletonLoader();
      if (this.filter === 'active') {
        try {
          const userId = this.currentItem.uid;
          await this.deleteUserOrg(this.currentAccount.handle, userId, { userId });
          this.dialogVisible = false;
          await this.init(this.currentAccount.handle);
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'member' });
        } catch (error) {
          showErrorToast(this.$swal, 'deleteError', { item: 'member' }, error?.response?.data);
        } finally {
          this.hideSkeletonLoader();
        }
      } else if (this.filter === 'pending' || this.filter === 'expired') {
        try {
          await this.deleteInvite({ handle: this.currentAccount.handle, email: this.currentItem.email });
          this.dialogVisible = false;
          await this.init(this.currentAccount.handle);
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'invite' });
        } catch (error) {
          showErrorToast(this.$swal, 'deleteError', { item: 'invite' }, error?.response?.data);
        } finally {
          this.hideSkeletonLoader();
        }
      }
    },
    async getMembers(per_page, next_page, handle) {
      const scopeService = this.projectScope ? makeProjectService(this.$api) : makeOrgService(this.$api);
      this.membersState.isLoading = true;
      const params = { per_page: per_page, next_page: next_page };
      const projectKey = this.$route.params.key;
      try {
        const response = await scopeService.getUsers({handle, params, projectKey });
        this.activeMembers = response.data.users;
        this.activeUserCount = this.getActiveUserCount;
        this.membersState.isLoading = false;
      } catch (error) {
        this.membersState.isLoading = false;
        this.membersState.hasError = true;
        this.membersState.errorMessage = error.message;
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'members' }, error?.response?.data);
      }
    },
    async deleteUserOrg(handle, userId, payload) {
      const orgService = makeOrgService(this.$api);
      try {
        await orgService.removeUserOrg(handle, userId, payload);
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'deleteError', { item: 'member' }, error?.response?.data);
      }
    },
    async init(handle) {
      try {
        this.showSkeletonLoader();
        await Promise.all([
          this.getMembers(30, 0, handle),
          this.fetchProjects(),
          this.fetchRoles(),
          this.fetchTags(),
          this.getPendingInvites(handle),
          this.getExpiredInvites(handle)
        ]);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async fetchProjects() {
      try {
        const projectService = makeProjectService(this.$api);
        const response = await projectService.getProjects(this.$route.params.handle);
        this.projects = response.data.items;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      }
    },
    async fetchRoles() {
      if(!this._readRole){
        this.unauthorizedToast;
        return ;
      }
      try {
        const roleService = makeRoleService(this.$api);
        const params = {
          includePermissions: true,
          ...(this.projectScope ? {includeOrgRoles: true} : {})
        }
        const projectKey = this.$route.params.key;
        const response = await roleService.getRoles(this.$route.params.handle, projectKey, params);
        this.roles = response.data.items;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, error?.response?.data);
      }
    },
    async fetchTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, 'users');
        this.tags = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, error?.response?.data);
      }
    },
    async getPendingInvites(handle) {
      try {
        const projectKey = this.$route.params.key;
        const response = await this.inviteService.listInvites({handle, status: 'pending', projectKey});
        this.pendingInvites = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'pending invites' }, error?.response?.data);
      }
    },

    async getExpiredInvites(handle) {
      try {
        const projectKey = this.$route.params.key;
        const response = await this.inviteService.listInvites({handle, status: 'expired', projectKey});
        this.expiredInvites = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'expired invites' }, error?.response?.data);
      }
    },

    async deleteInvite({ handle, email }) {
      try {
        this.showSkeletonLoader();
        const projectKey = this.$route.params.key;
        await this.inviteService.deleteInviteByEmail({handle, email, projectKey});
      } catch (error) {
        showErrorToast(this.$swal, 'deleteError', { item: 'invite' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
          onUpdatePagination(options) {
        const newPage = options.page;
        const newItemsPerPage = options.itemsPerPage;
        
        if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
          this.currentPage = newPage;
          this.perPage = newItemsPerPage;
        }
      },
  },
}
</script>