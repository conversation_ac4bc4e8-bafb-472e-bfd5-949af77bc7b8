<template>
  <v-container
    fluid
    class="pr-0 pb-0"
  >
    <section-header
      :title="$t('settingsPage.storage')"
      :has-action="false"
    />

    <v-card
      class="py-6 px-6 mt-3"
      rounded="lg"
      elevation="0"
      width="100%"
      height="100vh"
    >
      <v-row>  
        <v-col
          cols="12"
          sm="8"
          offset-sm="2"
          class="mt-5 text-left mx-auto"
        >
          <h6 class="text-h6 mb-5">
            {{ $t('storage.storageConfiguration') }}
          </h6>
          <v-row class="d-flex flex-row justify-space-between align-start">
            <v-col>
              <div class="d-flex justify-space-between mb-2">
                <p class="text-body fw-normal mb-2">
                  {{ $t('storage.enableCloudStorage') }}
                </p>
                <v-switch
                  v-if="!hasStorages"
                  v-model="isSwitch"
                  color="blue" 
                  inset 
                  hide-details
                  class="custom-switch mt-0"
                  @change="toggleStorage" 
                />
              </div>
              <p
                v-if="!hasDefaultStorage"
                class="text-subtitle-1 text-theme-secondary"
              >
                {{ $t('storage.storageInfo') }}
              </p>
              <div
                v-if="task.status === 'running' && !showStorageDialogModal"
                class="d-flex align-center bg-theme-base ps-5 rounded-lg" 
                style="gap: 20px;"
              >
                <Mascot3 class="mb-4" />
                <div class="d-flex flex-column py-4">
                  <p class="text-body">
                    The migration will run in the background.
                  </p>
                  <v-btn 
                    width="204.5px" 
                    class="btn-theme" 
                    height="40" 
                    color="primary"
                    :depressed="true" 
                    elevation="0" 
                    @click="evaluateProgress"
                  >
                    {{ $t('Check the Progress') }}
                  </v-btn>
                </div>
              </div>
            </v-col>
            <v-row class="d-flex gap-3">
              <v-btn 
                v-if="hasDefaultStorage"
                width="21px"
                class="btn-theme"
                height="40"
                :depressed="true"
                elevation="0"
                @click="openEditDialog"
              >
                Edit
              </v-btn>
            </v-row>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <CreateUpdateStorageDialog 
      v-model="showStorageDialogModal" 
      :data="activeStorage"
      :file="file"
      :task-id="taskId"
      @create-storage-field="handleStorageField('create', $event)"
      @update-storage-field="handleStorageField('update', $event)" 
      @type-change="fetchStorage"
      @close-dialog="closeDialog" 
    />
  </v-container>
</template>

<script>
  import { mapGetters, mapActions, mapState } from 'vuex';
  import SectionHeader from '@/components/Form/SectionHeader.vue';
  import Mascot3 from '@/assets/svg/mascot3.svg';
  import { encryptObject } from '@/utils/encryption';
  import CreateUpdateStorageDialog from '@/components/Settings/Storage/CreateUpdateStorageDialog.vue';
  import { showErrorToast } from '@/utils/toast';

  export default {
    name: 'Storage',

    components: {
      SectionHeader,
      CreateUpdateStorageDialog,
      Mascot3
    },

    data() {
      return {
        taskId: '',
        isSwitch: false,
        showStorageDialogModal: false,
      };
    },

    computed: {
      ...mapState('storage', ['storages', 'storage', 'file', 'task']),
      ...mapGetters({
        hasStorages: 'storage/hasStorages',
        getPublicKey: 'encryption/getPublicKey',
        hasDefaultStorage: 'storage/hasDefaultStorage',
        defaultStorage: 'storage/defaultStorage',
      }),
      ...mapState('user', ['currentAccount']),

      latestPublicKey() {
        return this.getPublicKey('media');
      },
      activeStorage() {
        return Object.keys(this.storage).length ? this.storage : this.defaultStorage;
      }
    },

    watch: {
      task: {
        handler(newVal) {
          if (newVal.status === 'completed' && !this.showStorageDialogModal) {
            this.$store.commit('storage/SET_STORAGE_TASK', {});
            this.$store.commit('storage/SET_STORAGE_file', {});
          }
        },
        immediate: true,
      },
    },

    mounted() {
      this.fetchStorages();
      this.fetchPublicKey({ service: 'media' });
    },

    methods: {
      ...mapActions('storage', ['get', 'create', 'getAttachmentSize', 'update', 'disable', 'setSelectedStorage']),
      ...mapActions('encryption', ['fetchPublicKey']),
      async toggleStorage() {
        this.showStorageDialogModal = true;
      },

      async fetchStorage(data) {
        await this.setSelectedStorage(data);
        this.showStorageDialogModal = true;
      },

      async openEditDialog() {
        this.fetchStorages();
        this.showStorageDialogModal = true;
      },

      async handleStorageField(action, data) { 
        let payload = { ...data, type: 'attachment', isDefault: false };
        try {
          if (data.credentials) {
            let encryptedData;
            if (data.provider === 'aws') {
              encryptedData = await encryptObject(
                {
                  access_key_id: data.credentials.access_key_id,
                  secret_access_key: data.credentials.secret_access_key,
                },
                this.latestPublicKey,
              );
            } else {
              encryptedData = await encryptObject(
                {
                  project_id: data.credentials.project_id,
                  private_key: data.credentials.private_key,
                  private_key_id: data.credentials.private_key_id,
                  client_email: data.credentials.client_email,
                  client_id: data.credentials.client_id,
                  token_uri: data.credentials.token_uri,
                },
                this.latestPublicKey,
              );
            }
            payload = {
              ...data,
              credentials: {...data.credentials, ...encryptedData.encryptedFields},
              symmetricKeyData: encryptedData.symmetricKeyData,
              type: 'attachment',
              isDefault: true,
            };
          }
          if (action === 'create') await this.createStorage(payload);
          else if (action === 'update') await this.updateStorage(data, payload);
          await this.fetchStorages();
          this.$store.commit('storage/SET_SELECTED_STORAGE', {});
        } catch (error) {
          showErrorToast(this.$swal, error.response.data.message);
        }
      },

      async createStorage(payload) {
        const response = await this.create({ swal: this.$swal, handle: this.currentAccount.handle, payload });
        if (!response.taskUid){
          this.showStorageDialogModal = false;
          return;
        }
        this.taskId = response.taskUid
        await this.getAttachmentSize({ swal: this.$swal, handle: this.currentAccount.handle});
      },

      async updateStorage(data, payload) {
        const {  ...filteredData } = data;
        const response = await this.update({ swal: this.$swal, handle: this.currentAccount.handle, data: filteredData, payload });
        if (!response.taskUid){
          this.showStorageDialogModal = false;
          return;
        }
        this.taskId = response.taskUid
        if (payload.credentials) await this.getAttachmentSize({ swal: this.$swal, handle: this.currentAccount.handle });
        else this.showStorageDialogModal = false;
      },

      async evaluateProgress() {
        this.showStorageDialogModal = true;
      },

      closeDialog() {
        this.isSwitch = false;
        this.showStorageDialogModal = false;
        this.$store.commit('storage/SET_SELECTED_STORAGE', {});
      },

      async fetchStorages() {
        await this.get(this.currentAccount.handle);
      },
    },
  };
</script>