<template>
  <div class="pl-3 mt-3">
    <PersonalizationHeader
      :title="$t('settingsPage.personalization')"
      :has-action="false"
      :filter-type="filterType"
      :update-tab="updateTab"
      :total-status-count="getTotalStatusCount"
      :total-priority-count="getTotalPriorityCount"
    />
    <v-card
      class="py-6 px-6 mt-3 text-left"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row class="justify-space-between align-center pt-4 pb-4 px-3 py-3">
        <h2 class="text-theme-base">
          {{ getSectionTitle() }}
        </h2>
        <AddStatus 
          :personalization-type="filterType == 'statusColors' && 'Status' || 'Priority'"
          :on-add="onCreateOrUpdate"
          :current-user="currentUser"
          :show-dialog.sync="showDialog"
          :modal-open="modalOpen"
          :action="action"
          :selected-item="selectedItem"
          :total-count="getTotalCountByFilterType"
          :selected-counts="isCompletedCount"
          @update:colors="updateColors" 
        />
      </v-row>

      <ColorsTable
        :headers="filteredHeaders"
        :on-edit="onCreateOrUpdate"
        :on-add-status="handleAddStatus"
        :on-color-change="handleColorChange"
        :items="colorItems"
        item-key="id"
        class="mt-3"
        type="status"
        :all-statuses="allStatuses"
        :all-priorities="allPriorities"
        :filter-type="filterType"
        @change-color="changeColor"
        @reset-color="resetColor"
      />
    </v-card>
  </div>
</template>

<script>
import { mapGetters, mapActions, createNamespacedHelpers } from 'vuex';
import ColorsTable from '@/components/Settings/DataColors/ColorsTable.vue';
import PersonalizationHeader from '@/components/Settings/Personalization/PersonalizationHeader.vue';
import AddStatus from '@/components/Settings/Personalization/AddStatus.vue';
import makeHandleService from '@/services/api/handle';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

import { entityTypes } from '@/constants/colors.js';

const { mapState, mapGetters: mapUserGetters, mapActions: mapUserActions } = createNamespacedHelpers('user');



export default {
  name: 'DataColors',

  components: {
    PersonalizationHeader,
    ColorsTable,
    AddStatus,
  },

  data () {
    return {
      headers: [],
      currentUser: undefined,
      filterType: 'statusColors',
      showDialog: false,
			action: 'Add',
			selectedItem: {},
    }
  },

  computed: {
    ...mapState(["currentAccount"]),
    ...mapUserGetters({
      getUserPreferences: "getUserPreferences",
    }),
    ...mapGetters({ 
      dynamicHeaders:'headers/dynamicHeaders'
    }),
    filteredHeaders() {
      return this.headers || [];
    },
    isCompletedCount(){
      return this.colorItems.filter(item => item.isCompleted).length
    },
    getEntityTypes() {
      return entityTypes;
    },
    colorItems() {
      // Get unique colors from all statuses/priorities
      let allItems = [];
      if (this.filterType === 'statusColors') {
        allItems = this.allStatuses;
      } else if (this.filterType === 'priorityColors') {
        allItems = this.allPriorities;
      }
      
      // Define the entity types that are actually shown in the table columns
      const tableEntityTypes = ['testCase', 'testRun', 'testPlan', 'milestone'];
      
      // Only include colors that have actual data for entity types shown in the table
      const validColors = allItems.filter(item => 
        item.name && 
        item.entityType && 
        tableEntityTypes.includes(item.entityType)
      );
      const uniqueColors = [...new Set(validColors.map(item => item.color))];
      
      return uniqueColors.map(color => ({
        id: color, // Use color as unique identifier
        color: color,
        name: this.getStatusNameForColor(color)
      }));
    },
    allStatuses() {
      return this.colors["statusColors"]?.filter(item => !item.archived) || [];
    },
    allPriorities() {
      return this.colors["priorityColors"]?.filter(item => !item.archived) || [];
    },
    preferencesState() {
      return this.getUserPreferences || {};
    },
    preferencesTimestamp() {
      return this.preferencesState?.timestamp || null;
    },
    getTotalStatusCount() {
      return this.colors["statusColors"]?.filter(element => !element.archived).length || 0;
		},
    getTotalPriorityCount() {
      return this.colors["priorityColors"]?.filter(element => !element.archived).length || 0;
    },
    getTotalCountByFilterType() {
      return this.colors[this.filterType]?.length || 0;
    },
    colors: {
      get() {
        return {
          statusColors: this.preferencesState?.statusColors || [],
          priorityColors: this.preferencesState?.priorityColors || [],
        }
      },
      set(value) {
        this.setUserPreferences(
          {
            ...value,
            timestamp: this.preferencesTimestamp
          }
        );
      }
    }
  },
  created() {
    if(!this.dynamicHeaders.personalizations) {
      this.initializeHeaders({ type: 'personalizations' });
    }
    this.headers = this.dynamicHeaders.personalizations;
  },
  methods: {
    ...mapActions({
      setLoading: 'setLoading',
    }),
    ...mapUserActions(['setUserPreferences']),
    ...mapActions("headers", ['initializeHeaders']),
    updateTab(type) {
      this.filterType = type;
    },
		onCreateOrUpdate(action, id, key) {
			this.showDialog = true;
			this.action = action;
			this.selectedItem = key && this.colors.default[this.filterType].find(color => color.id == id) || this.colors[this.filterType].find(color => color.id == id)
		},
		modalOpen(type) {
			this.showDialog = type;
      this.selectedItem = type ? this.selectedItem : [];
		},
		getCount(tab) {
      const customColorCount = this.colors[this.filterType]?.filter(color => color.entityType == tab && !color.archived)?.length || 0;
      return customColorCount;
		},
    changeColor(type, index, color) {
      this.colors[`${type}Colors`][index].value = color
    },
    updateColors(newColors) {
      this.colors = newColors;
    },
    getStatusNameForColor(color) {
      let allItems = [];
      if (this.filterType === 'statusColors') {
        allItems = this.allStatuses;
      } else if (this.filterType === 'priorityColors') {
        allItems = this.allPriorities;
      }
      
      const itemsWithColor = allItems.filter(item => item.color === color && item.name);
      if (itemsWithColor.length > 0) {
        // Return the first valid name found
        return itemsWithColor[0].name;
      }
      return '';
    },
    
    handleAddStatus(item, entityType) {
      // Find the entity type object from entityTypes
      const entityTypeObj = entityTypes.find(et => et.value === entityType);
      
      // Create a new status/priority for the specific entity type and color
      const newStatus = {
        name: '',
        aliases: [],
        entityType: entityTypeObj || entityType,
        color: item.color,
        id: '',
        isDefault: false,
        isCompleted: false,
        isSuccess: false,
        isFailure: false,
      };
      
      this.selectedItem = newStatus;
      this.action = 'Add';
      this.showDialog = true;
    },
    
    async handleColorChange(updatedItems) {
      try {
        // Create the payload for API call
        const payload = {
          preferences: {
            statusColors: this.filterType === 'statusColors' ? updatedItems : this.colors.statusColors,
            priorityColors: this.filterType === 'priorityColors' ? updatedItems : this.colors.priorityColors,
          },
        };
        
        // Make API call to update preferences
        const handleService = makeHandleService(this.$api);
        const response = await handleService.updatePreferences(this.currentAccount.handle, payload);
        
        if (response.status === 200) {
          // Update local state
          this.setUserPreferences(payload.preferences);
          
          // Show success message
          showSuccessToast(this.$swal, this.$t('dataColors.colorsUpdated'));
        }
      } catch (err) {
        const errorMessage = err.response?.data?.message || 'Failed to update colors';
        showErrorToast(this.$swal, errorMessage);
      }
    },
    resetColor(item, newColor) {
      // Update all statuses/priorities/charts with this color
      let allItems = [];
      if (this.filterType === 'statusColors') {
        allItems = this.allStatuses;
      } else if (this.filterType === 'priorityColors') {
        allItems = this.allPriorities;
      }
      
      const updatedItems = allItems.map(status => 
        status.color === item.color ? { ...status, color: newColor } : status
      );
      
      const updatedColors = {
        ...this.colors,
        [this.filterType]: updatedItems
      };
      
      this.updateColors(updatedColors);
    },
    getSectionTitle() {
      if (this.filterType === 'statusColors') {
        return this.$t('dataColors.statusColors');
      } else if (this.filterType === 'priorityColors') {
        return this.$t('dataColors.priorityColors');
      }
      return '';
    }
  },
}
</script>
