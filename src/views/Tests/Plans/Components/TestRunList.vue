<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      v-model="selectedRows"
      class="custom-table data-table-style table-fixed font-inter mt-6"
      show-select
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      :headers="headers"
      :items="itemsPerView"
      :item-key="itemKey"
      hide-default-footer
      :disable-pagination="true"
    >
      <template #[`header.data-table-select`]="{ props, on }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            indeterminate-icon="icon-indeterminate"
            :input-value="props.value"
            :indeterminate="props.indeterminate"
            @change="on.input"
          />
        </div>
      </template>

      <template #[`item.data-table-select`]="{ isSelected, select }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            :input-value="isSelected"
            @change="select"
            @click.stop
          />
        </div>
      </template>

      <template #[`headers.name`]="{ header }">
        <span class="header_text">{{ header.text }}</span>
      </template>
      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="item.name.length < 61"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div
              class="custom-attribute text-truncate font-weight-bold cursor-pointer"
              v-bind="attrs"
              v-on="on"
            >
              {{ item.name }}
            </div>
          </template>
          <span>{{ item.name }}</span>
        </v-tooltip>
      </template>
      <template #[`item.testcases`]="{ item }">
        <td class="d-flex align-center">
          <div class="text-start">
            <div class="text-subtitle-3">
              {{ item.customFields.caseCount }} {{ $t('plans.testRuns.testCases') }}
            </div>
          </div>
        </td>
      </template>
      <template #[`item.testruns`]="{ item }">
        <td class="d-flex align-center">
          <div class="text-start">
            <div class="text-subtitle-2 font-weight-bold">
              {{ item.testruns }} {{ $t('plans.testRuns.testRuns') }}
            </div>
          </div>
        </td>
      </template>
      <template #[`item.configurations`]="{ item }">
        <div class="d-flex flex-row align-center justify-start">
          <v-btn
            v-if="item?.configs?.length === 0"
            color="primary"
            text
            class="fw-semibold text-capitalize"
            @click="handleAddConfigurations(item)"
          >
            <v-icon
              color="primary"
            >
              mdi-plus
            </v-icon> {{ $t('add') }}
          </v-btn>
          <v-menu
            v-else
            bottom
            offset-y
          >
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                class="d-flex align-center"
                v-on="on"
              >
                <span>{{ item.configs?.map(config => config.option).map(option => option).join(', ') }}</span>
                <v-icon>mdi-chevron-down</v-icon>
              </div>
            </template>
            <v-list>
              <v-list-item
                v-for="(config, index) in item.customFields.configs"
                :key="index"
              >
                <div class="d-flex align-center">
                  <div
                    v-if="editingIndex === index"
                    class="d-flex align-center"
                  >
                    <v-text-field
                      ref="itemInput"
                      v-model="editedValue"
                      class="new-folder-input mt-0 pt-0 pa-0 editable "
                      type="text"
                      background-color="transparent"
                      hide-details
                      autofocus
                      height="20px"
                      solo
                      flat
                      dense
                      @blur="saveItem(item, index)"
                      @keyup.enter="saveItem(item, index)"
                      @click.stop
                    />
                  </div>
                  <span
                    v-else
                    class="fs-14 text-theme-label"
                  >{{ item.configs[index].option }}</span>
                  <v-btn
                    icon
                    plain
                    small
                    class="btn-plain-theme"
                    @click.stop="startEditItem(item, index)"
                  >
                    <PencilIcon />
                  </v-btn>
                  <v-btn
                    icon
                    plain
                    small
                    class="btn-plain-theme"
                    @click.stop="deleteItem(item, index)"
                  >
                    <DeleteIcon16px />
                  </v-btn>
                </div>
              </v-list-item>
              <v-btn
                depressed
                :ripple="false"
                plain
                class="f-color-white btn-theme text-capitalize rounded-lg btn-plain-theme"
                color="primary"
                height="40px"
                @click.stop="onAddConfiguration(item)"
              >
                <v-icon
                  color="primary"
                  class="mr-2"
                >
                  mdi-plus
                </v-icon>
                <span>{{ $t('plans.create.testRuns.addConfiguration') }} </span>
              </v-btn>
            </v-list>
          </v-menu>
        </div>
      </template>
      <template #[`item.priority`]="{item}">
        <span
          class="font-weight-bold text--lighten-1"
          :style="{ color: getPriorityColor(item.priority, priorities) }"
        >{{ getPriorityName(item.priority, priorities) }}</span>
      </template>
      <template #[`item.status`]="{item}">
        <span
          class="font-weight-bold text--lighten-1"
          :style="{ color: getStatusColor(item.status, statuses) }"
        >{{ getStatusName(item.status, statuses) }}</span>
      </template>
      <template #[`item.milestone`]="{ item }">
        <v-tooltip
          top
          left
          max-width="485px"
          :disabled="!Array.isArray(item.testMilestones) || item.testMilestones.length < 2"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <span
              class="custom-attribute font-weight-regular text-theme-table-text"
              v-bind="attrs"
              v-on="on"
            >
              <template>
                <div class="text-truncate">
                  <span v-if="Array.isArray(item.testMilestones) && item.testMilestones.length > 0">
                    {{ item.testMilestones.map(milestone => `${milestone.name}`).join(', ') }}
                  </span>
                  <span v-else-if="typeof item.testMilestones === 'string' && item.testMilestones.trim() !== ''">
                    {{ item.testMilestones }}
                  </span>
                  <v-icon v-else>mdi-minus</v-icon>
                </div>
              </template>
            </span>
          </template>
          <span>
            {{
              Array.isArray(item.testMilestones)
                ? item.testMilestones.map(milestone => `${milestone.name}`).join(', ')
                : ''
            }}
          </span>
        </v-tooltip>
      </template>
      <template #[`item.tags`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!((Array.isArray(item.tags) && item.tags.length >= 2) || (Array.isArray(item.customFields?.tags) && item.customFields.tags.length >= 2))"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <span
              class="custom-attribute font-weight-regular text-theme-table-text"
              v-bind="attrs"
              v-on="on"
            >
              <div class="text-truncate">
                {{
                  Array.isArray(item.tags) && item.tags.length > 0
                    ? item.tags.map(tag => tag.name).join(', ')
                    : Array.isArray(item.customFields?.tags) && item.customFields.tags.length > 0
                      ? item.customFields.tags.map(tag => tag.name).join(', ')
                      : typeof item.tags === 'string' && item.tags.trim() !== ''
                        ? item.tags
                        : typeof item.customFields?.tags === 'string' && item.customFields.tags.trim() !== ''
                          ? item.customFields.tags
                          : $t('no_tags')
                }}
              </div>
            </span>
          </template>
          <span>
            {{
              Array.isArray(item.tags) && item.tags.length > 0
                ? item.tags.map(tag => tag.name).join(', ')
                : Array.isArray(item.customFields?.tags)
                  ? item.customFields.tags.map(tag => tag.name).join(', ')
                  : ''
            }}
          </span>
        </v-tooltip>
      </template>
      <template #[`item.creationdate`]="{ item }">
        <span class="">{{ formatCreationDate(item.createdAt) }}</span>
      </template>
      <template #[`item.progress`]="{ item }">
        <ProgressBar
          :executions="item.executionsProgress"
          :percentage="item.percentage"
          :case-count="item.customFields.caseCount"
        />
      </template>
    </v-data-table>
    <template v-else>
      <RunTableSkeleton class="mt-6" />
    </template>
    <slot
      v-if="!skeletonLoaderState"
      name="action"
    />
  </div>
</template>

<script>
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ProgressBar from '@/components/base/ProgressBar'
import handleLoading from '@/mixins/loader.js';
import RunTableSkeleton from '@/components/Skeletons/TestRuns/RunTableSkeleton.vue';
import PencilIcon from '@/assets/svg/pencil-16.svg';
import DeleteIcon16px from '@/assets/svg/delete-16.svg';

export default {
  name: 'TestRunsList',
  components: {
    RunTableSkeleton,
    ProgressBar,
    PencilIcon,
    DeleteIcon16px,
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    configurations: {
      type: Array,
    },
    tab: {
      type: String,
      default: 'all',
    },
    data: {
      type: Array,
    },
    selectedRuns: {
      type: Array,
    },
    headers: {
      type: Array,
    },
    filteredItems: {
      type: Array,
    },
    rowClass: {
      type: Function,
    },
    globalConfiguration: {
      type: Object,
    },
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      itemKey: 'uid',
      selected: this.selectedRuns,
      isAllTestRunChecked: false,
      isSelectedTestRunChecked: false,
      isOpenAddConfigurations: false,
      statuses: [],
      priorities: [],
      editingIndex: null,
      editedValue: '',
    };
  },
  computed: {
    selectedRows:{
      get(){
        return this.selectedRuns
      },
      set(value){
        this.selected = value;
        this.$emit('selectTestRun', this.selected)
      }
    },
    itemsPerView() {
      if (this.tab === 'all') {
        return this.data
      } else {
        return this.selectedRuns
      }
    },
    
  },
  watch: {
    tab() {
      this.toggleTestRuns();
    },
  },
  created() {
    this.priorities = this.getPriorities("testRun");
    this.statuses = this.getStatuses("testRun");
  },
  methods: {
    getConfigurationSets(item){
      return item.configuration ? item.configuration.sets.flat(Infinity) : this.globalConfiguration.sets.flat(Infinity);
    },
    onUpdatePagination(options){
      this.$emit('update-pagination', options);
    },
    getConfigurationName(item){
      const [configUid, ItemUid] = item.split('::');
      const config = this.configurations.find(config => config.uid === Number(configUid));

      const optionName = config.options.find(option => option.uid === Number(ItemUid));
      const configName = config.name;
      return `${configName}::${optionName.name}`
    },
    selectedItems(items) {
      this.$emit('selectTestRun', items);
    },
    toggleTestRuns() {
      const isSelected = this.tab === 'all' ? this.isAllTestRunChecked : this.isSelectedTestRunChecked;
      this.data.forEach((run) => {
        const condition = this.tab === 'all' ? !run.selected : run.selected;
        if (condition) {
          this.$set(run, 'toBeSelected', isSelected);
        }
      });
    },
    toBeSelectedHandler() {
      const filteredRuns =
        this.tab === 'all' ? this.data.filter((run) => !run.selected) : this.data.filter((run) => run.selected);
      const allTrue = filteredRuns.every((run) => run.toBeSelected);

      if (this.tab === 'all') {
        this.isAllTestRunChecked = allTrue;
      } else {
        this.isSelectedTestRunChecked = allTrue;
      }
    },
    handleAddConfigurations(item) {
      this.$emit('addConfiguration', item);
    },
    formatCreationDate(createdAt) {
      return this.formatDate(createdAt)
    },
    onAddConfiguration(item) {
      this.$emit('addConfiguration', item);
    },
    deleteItem(item, index) {
      const unSelectedConfigs = item.customFields.configs.filter((_, i) => i !== index);
      const data = {
        newConfigs: unSelectedConfigs,
        runUid: item.uid,
      }
      this.$emit('deleteConfigItem', data);
    },
    startEditItem(item, index) {
      this.editingIndex = index;
      this.editedValue = item.configs[index].option
      this.$nextTick(() => {
        if (this.$refs?.itemInput) {
          this.$refs?.itemInput?.$el?.focus();
        }
      });
    },
    saveItem(oldItem, index) {
      if (this.editedValue) {
        const config = oldItem?.customFields?.configs[index];
        const [configUid, optionUid] = config.split("::");
        this.$emit('editConfigItem', {
          configUid: configUid,
          optionUid: optionUid,
          newItemName: this.editedValue
        });
      }
      this.editingIndex = null;
      this.editedValue = '';
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-add-configuration {
  padding: 0;
  background: transparent;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #667085;

  display: inline-flex;
  align-items: center;
  gap: 8px;

  &::before {
    display: none;
  }

  .v-icon {
    color: #667085;
  }
}

.btn-add-configuration:hover {
  color: #0c2ff3;

  .v-icon {
    color: #0c2ff3;
  }
}

.configurations-menu {
  .v-subheader {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #0c111d;
  }

  .btn-edit {
    color: #667085;
  }

  .btn-delete {
    color: #f4284e;
  }

  .btn-add-configuration {
    color: #0c2ff3;
  }
}
</style>