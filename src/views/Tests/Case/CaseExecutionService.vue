<template>
  <v-form
    id="create-case-form"
    ref="form"
    v-model="isCreateTestCaseFormValid"
    role="createTestCaseForm"
    class="d-flex"
  >
    <AiPopup
      v-if="assistField.fieldType && assistField.fieldKey"
      :key="assistField.fieldKey"
      :regenerate="regenerate"
      :style="{
        left: assistField.position.x + 'px',
        top: assistField.position.y + 'px',
        position: 'absolute'
      }"
      @closeAssist="closePopupAssist"
      @generateResponse="generateImprovement"
      @reject="rejectImprovement"
    />
    <Loader v-if="isCaseImproving">
      <template>
        {{ $t('generating') }}
      </template>
    </Loader>
    <div
      v-else
      class="white fill-height v-align-start font-inter rounded-lg app-height-global pb-3 mt-3 mr-0 flex-grow-1 pt-6"
      :class="{
        'ml-3': isEditMode,
      }"
    >
      <v-row
        align="center"
        justify="center"
        class="flex-column"
        :style="[showComparing ? { 'marginBottom': '72px' } : '']"
      >
        <v-col
          cols="12"
          sm="12"
          class="back-btn-container card px-8"
        >
          <div class="back-btn">
            <router-link
              v-if="isTestRunEdit || isTestRunCreate"
              :to="{
                name: isTestRunEdit ? 'TestRunEdit' : 'TestRunCreate',
                params: {
                  handle: $route.params.handle,
                  key: $route.params.key,
                  id: isTestRunEdit ? $route.query.runId : null,
                  folderUid: isTestRunEdit ? $route.query.folderUid : null,
                },
              }"
            >
              <v-icon>mdi-chevron-left</v-icon> {{ $t('backToTestRuns') }}
            </router-link>
            <router-link
              v-else-if="isExecution"
              :to="{
                name: 'TestRunCaseEdit',
                params: { handle: $route.params.handle, key: $route.params.key, id: $route.query.runId },
              }"
            >
              <v-icon>mdi-chevron-left</v-icon> {{ $t('backToTestExecutions') }}
            </router-link>
            <div
              v-else
              class="back-to-projects"
              @click="handleBackClick"
            >
              <v-icon color="blue">
                mdi-chevron-left
              </v-icon>
              <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
                {{ $t('backToTestCases') }}
              </p>
            </div>
          </div>
        </v-col>
        <v-col
          cols="12"
          sm="12"
          class="case-contents"
        >
          <div
            v-if="!isEditMode"
            class="item-area mb-5 justify-space-between"
          >
            <h2 class="font-weight-bold text-theme-dark">
              {{ $t('createNewTestCase') }}
            </h2>
            <v-btn
              depressed
              background-color="#F2F4F7"
              class="text-capitalize btn-theme fw-semibold"
              height="38px"
              width="100%"
              max-width="135px"
              @click="openAssist"
            >
              {{ $t('aiAssist.aiCreate') }}
              <CreationOutline class="ml-2" />
            </v-btn>
          </div>
          <div
            v-else-if="isExecution"
            class="item-area mb-5"
          >
            <h2>{{ $t('editExecution') }}</h2>
          </div>
          <div
            v-else
            class="item-area mb-5"
          >
            <h2>{{ $t('editTestCase') }}</h2>
          </div>

          <div
            v-if="!isEditMode"
            class="toggle-wrapper my-8"
          >
            <div class="toggle-container">
              <div
                class="toggle-option"
                :class="{ active: selectedFormat === 'testCase' }"
                @click="setFormat('testCase')"
              >
                {{ $t('testFormat') }}
              </div>
              <div
                class="toggle-option"
                :class="{ active: selectedFormat === 'testResult' }"
                @click="setFormat('testResult')"
              >
                {{ $t('resultFormat') }}
              </div>
            </div>
          </div>

          <template v-if="selectedFormat == 'testCase'">
            <div
              v-if="!isExecution"
              class="item-area mb-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('template') }}
              </v-label>
            </div>
            <div
              v-if="!isExecution"
              class="item-area"
            >
              <v-select
                v-model="templateId"
                class="mt-0 pt-1 rounded-lg field-theme custom-prepend"
                :items="filteredTemplates"
                background-color="#F9F9FB"
                item-text="name"
                item-value="uid"
                height="38px"
                :placeholder="$t('template')"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                @change="handleTemplateChange"
              />
            </div>
            <div class="d-flex align-center gap-1 mb-4">
              <div class="fs-20px fw-semibold">
                {{ $t('generalInfo') }}
              </div>
              <v-tooltip 
                bottom
                max-width="316"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    class="cursor-pointer d-flex align-center"
                    v-on="on"
                  >
                    <QuestionIcon />
                  </div>
                </template>
                <span>{{ $t('templatesPage.generalInfoDescription') }}</span>
              </v-tooltip>
            </div>
            <div class="item-area  mb-1">
              <v-label
                class="text-left fs-14px text-theme-label font-weight-medium"
              >
                {{ $t('name') }}<strong
                  class="red--text text--lighten-1"
                  :class="{
                    'd-none': isExecution
                  }"
                >*</strong>
              </v-label>
              <v-label
                v-if="isEditMode"
                class="text-left fs-14px text-theme-label font-weight-medium"
              >
                {{ $t('id') }}
              </v-label>
            </div>
            <div class="item-area">
              <v-text-field
                id="caseNameField"
                v-model="caseName"
                :placeholder="$t('name')"
                class="round-8  w-60 field-theme mb-2"
                background-color="#F9F9FB"
                dense
                height="38px"
                :rules="titleValidation"
                required
                :disabled="isExecution"
              />
              <v-text-field
                v-if="isEditMode"
                v-model="caseID"
                placeholder="HM-1"
                background-color="#F9F9FB"
                class="round-8 pl-3 w-40 field-theme mb-2"
                dense
                height="38px"
                :disabled="true"
              />
            </div>
            <div
              v-if="!isExecution"
              class="item-area  mb-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testFolder') }}
              </v-label>
            </div>
            <div
              v-if="!isExecution"
              class="item-area mb-2"
            >
              <FolderAutocomplete
                v-model="selectedFolderUID"
                :handle="$route.params.handle"
                :project-key="$route.params.key"
                :placeholder="$t('selectFolder')"
                :rules="[(v) => !!v || $t('error.requiredField')]"
                class=" mt-0 pt-1 mb-4"
                @folder-selected="handleFolderSelected"
              />
            </div>
            <div
              v-if="!isExecution"
              class="item-area mb-2"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('priority') }}
              </v-label>
            </div>
            <div
              v-if="!isExecution"
              class="item-area"
            >
              <v-select
                v-model="casePriority"
                :items="priorities"
                item-text="name"
                append-icon="mdi-chevron-down"
                item-value="id"
                class=" mt-0 pt-1 rounded-lg field-theme custom-prepend"
                :placeholder="$t('choosePriority')"
                background-color="#F9F9FB"
                height="38px"
                :menu-props="{ offsetY: true }"
                return-object
              />
            </div>
            <div class="item-area mb-2">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('tags') }}
              </v-label>
            </div>
            <div class="item-area mb-6">
              <TagSelector
                v-model="caseTags"
                :items="tags"
                class=""
              />
            </div>

            <div v-if="customFieldsFromTemplateGeneralInfo.length">
              <CustomFieldFromTemplate
                v-for="(field, iteration) in customFieldsFromTemplateGeneralInfo"
                :key="`general-${field.id || field.uid || iteration}`"
                :ref="`customField-${iteration}`"
                :field="field"
                :custom-index="iteration"
                @openAssist="popupAssist"
                @delete="removeCustomField(field)"
              />
            </div>
            <div v-else class="pa-6 text-center">
              <span class="fs-14px text-theme-secondary">{{ $t('templatesPage.noCustomFields') }}</span>
            </div>
            <ButtonWithMenuItemList
              :items="filteredCustomFieldsByFormat"
              :button-text="$t('templatesPage.add_custom_field')"
              button-color="primary"
              button-class="fs-14px mb-8 mt-6"
              :button-height="38"
              :show-add-new-icon="false"
              :search-placeholder="$t('placeHolder.searchByName')"
              :add-new-text="$t('addNewCustomField')"
              :cancel-text="$t('cancel')"
              :confirm-text="$t('add')"
              :no-items-text="$t('noCustomFieldsFound')"
              :item-disabled="(field) => isCustomFieldGeneralInfoSelected(field)"
              @confirm="addCustomFieldGeneralInfo"
              @add-new="addNewCustomFieldGeneralInfo"
            />

            <div class="d-flex align-center gap-1 my-4">
              <div class="fs-20px fw-semibold">
                {{ $t('integrations.edit_integration.details') }}
              </div>
              <v-tooltip 
                bottom
                max-width="316"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    class="cursor-pointer d-flex align-center"
                    v-on="on"
                  >
                    <QuestionIcon />
                  </div>
                </template>
                <span>{{ $t('templatesPage.detailsDescription') }}</span>
              </v-tooltip>
            </div>

            <!-- custom fields from template -->
            <div v-if="customFieldsFromTemplateDetails.length">
              <CustomFieldFromTemplate
                v-for="(field, iteration) in customFieldsFromTemplateDetails"
                :key="`details-${field.id || field.uid || iteration}`"
                :ref="`customField-${iteration}`"
                :field="field"
                :custom-index="iteration"
                @openAssist="popupAssist"
                @delete="removeCustomField(field)"
              />
            </div>
            <div v-else class="pa-6 text-center">
              <span class="fs-14px text-theme-secondary">{{ $t('templatesPage.noCustomFields') }}</span>
            </div>
          </template>
          <template v-else>
            <div
              v-if="!isExecution"
              class="item-area mb-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('template') }}
              </v-label>
            </div>
            <div
              v-if="!isExecution"
              class="item-area"
            >
              <v-select
                v-model="templateId"
                class="mt-0 pt-1 rounded-lg field-theme custom-prepend"
                :items="filteredTemplates"
                background-color="#F9F9FB"
                item-text="name"
                item-value="uid"
                height="38px"
                :placeholder="$t('template')"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                @change="handleTemplateChange"
              />
            </div>

            <v-label
              class="text-left fs-14px text-theme-label font-weight-medium"
            >
              {{ $t('name') }}<strong
                class="red--text text--lighten-1"
                :class="{
                  'd-none': isExecution
                }"
              >*</strong>
            </v-label>
            <v-text-field
              id="caseNameField"
              v-model="caseName"
              :placeholder="$t('name')"
              class="round-8 field-theme mb-2"
              background-color="#F9F9FB"
              dense
              height="38px"
              :rules="titleValidation"
              required
              :disabled="isExecution"
            />

            <TemplateRulesManager
              v-model="resultRules"
              :custom-fields-result="customFieldsResult"
            />
            <div v-if="filteredCustomFieldsFromTemplate.length">
              <CustomFieldFromTemplate
                v-for="(field, iteration) in filteredCustomFieldsFromTemplate"
                :key="`custom-template-result-${field.id || field.uid || iteration}`"
                :ref="`customField-${iteration}`"
                :field="field"
                :custom-index="iteration"
                @openAssist="popupAssist"
                @delete="removeCustomField(field)"
              />
            </div>
            <div v-else class="pa-6 text-center">
              <span class="fs-14px text-theme-secondary">{{ $t('templatesPage.noCustomFields') }}</span>
            </div>
            
            <ButtonWithMenuItemList
              :items="filteredCustomFieldsByFormat"
              :button-text="$t('templatesPage.add_custom_field')"
              button-color="primary"
              button-class="fs-14px mb-8 mt-6"
              :button-height="38"
              :show-add-new-icon="false"
              :search-placeholder="$t('placeHolder.searchByName')"
              :add-new-text="$t('addNewCustomField')"
              :cancel-text="$t('cancel')"
              :confirm-text="$t('add')"
              :no-items-text="$t('noCustomFieldsFound')"
              :item-disabled="(field) => isCustomFieldSelected(field)"
              @confirm="addCustomField"
              @add-new="addNewCustomField"
            />

          </template>
        </v-col>

        <!-- step -->
        <v-col
          v-if="steps?.length && selectedFormat === 'testCase'"
          id="all_steps"
          :key="steps?.length"
          cols="12"
          sm="12"
          class="pl-0 pr-0 block"
        >
          <step-editor
            v-for="(item, index) in steps"
            :key="`step-${item.id || item.uid || index}`"
            :ref="`step-${index}`"
            :case-step="item"
            :step-index="index"
            :selected="selectedSteps.includes(index)"
            :expected-result-by-step="expectedResultByStep"
            @openAssist="popupAssist"
            @delete-item="deleteStep"
            @duplicate-step="duplicateStep"
            @create-shared-step="handleCreateSharedStep"
            @description-change="changeDescription"
            @result-change="changeResult"
            @children-change="changeChildren"
            @toggle-selection="toggleStepSelection"
            @title-change-parent="changeTitleParent"
            @child-title-change="changeChildTitle"
          />
        </v-col>
        <v-col
          v-if="steps.length"
          id="all_steps"
          cols="12"
          sm="12"
          class="case-contents"
        >
          <div
            v-if="!expectedResultByStep"
            class="text-left item-area  mb-1"
          >
            <v-label class="fs-14px text-theme-label font-weight-medium">
              {{ $t('expectedResult') }} <strong class="red--text text--lighten-1">*</strong>
            </v-label>
          </div>
          <TextareaField
            v-if="!expectedResultByStep"
            ref="expectedResult"
            v-model="expectedResult"
            :enable-markdown="true"
            :enable-test-rail="true"
            auto-grow
            :placeholder="$t('expectedResult')"
            style="max-width: 380px;"
            background-color="#F9F9FB"
            class=" field-theme  mb-1 item-area"
          />
        </v-col>
        <v-col
          v-if="selectedFormat === 'testCase'"
          cols="12"
          sm="12"
        >
          <div class="flex justify-center gap-3">
            <ButtonWithMenuItemList
              :items="filteredCustomFieldsByFormat"
              :button-text="$t('templatesPage.add_custom_field')"
              button-color="#F2F4F7"
              button-class="fs-14px"
              :button-height="38"
              :button-max-width="205"
              :show-add-new-icon="false"
              :block-button="false"
              :search-placeholder="$t('placeHolder.searchByName')"
              :add-new-text="$t('addNewCustomField')"
              :cancel-text="$t('cancel')"
              :confirm-text="$t('add')"
              :no-items-text="$t('noCustomFieldsFound')"
              :item-disabled="(field) => isCustomFieldDetailsSelected(field)"
              @confirm="addCustomFieldDetails"
              @add-new="addNewCustomFieldDetails"
            />
            <v-menu
              offset-y
              top
              class="rounded-lg"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  class="text-capitalize f-color-white btn-theme"
                  color="primary"
                  depressed
                  height="38"
                  min-width="205px"
                  v-on="on"
                >
                  <v-icon
                    size="16"
                    class="mr-2"
                  >
                    mdi-plus
                  </v-icon>
                  {{ $t('addStep') }}
                </v-btn>
              </template>
              <v-card
                rounded="8"
                elevation="0"
              >
                <v-list>
                  <v-list-item
                    @click="addStep"
                  >
                    <v-list-item-title class="pl-3">
                      {{ $t('addStep') }}
                    </v-list-item-title>
                  </v-list-item>

                  <template>
                    <div class="text-center">
                      <v-menu
                        v-model="menu"
                        :close-on-content-click="false"
                        offset-y
                        max-width="205px"
                        top
                      >
                        <template #activator="{ on, attrs }">
                          <v-list-item
                            v-bind="attrs"
                            v-on="on"
                          >
                            <v-list-item-title class="pl-3 text-start">
                              {{ $t('addSharedStep') }}
                            </v-list-item-title>
                          </v-list-item>
                        </template>
                        <v-card>
                          <template>
                            <v-container>
                              <v-text-field
                                v-model="searchTerm"
                                class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                                :placeholder="$t('placeHolder.searchByName')"
                                height="40"
                                clear-icon="mdi-close-circle"
                                clearable
                                background-color="#F9F9FB"
                                :hide-details="true"
                              >
                                <template #prepend-inner>
                                  <SearchIcon />
                                </template>
                              </v-text-field>
                              <v-radio-group
                                v-if="isFilteredSelectedItemsHasData"
                                v-model="selectedItem"
                              >
                                <v-radio
                                  v-for="(item, index) in filteredSelectedItems"
                                  :key="index"
                                  :label="item.name"
                                  :value="item"
                                />
                              </v-radio-group>
                              <div
                                v-else
                                class="px-5 py-6 text-center"
                              >
                                {{ $t('noSharedStepsFound') }}, <br>{{ $t('click') }}
                                <span
                                  class="cursor-pointer text-theme-primary"
                                  @click="handleCreateSharedStep"
                                >{{
                                  $t('here')
                                }}</span>
                                {{ $t('toCreateOne') }}.
                              </div>
                              <v-spacer />
                              <div class="d-flex justify-center w-100 px-2">
                                <v-btn
                                  class="text-capitalize btn-theme mr-2"
                                  depressed
                                  height="38px"
                                  width="50%"
                                  @click="menu = false"
                                >
                                  {{ $t('cancel') }}
                                </v-btn>
                                <v-btn
                                  class="text-capitalize btn-theme ml-2"
                                  depressed
                                  color="primary"
                                  height="38px"
                                  width="50%"
                                  :class="{
                                    'disabled-action': !isFilteredSelectedItemsHasData,
                                  }"
                                  @click="addSharedStep"
                                >
                                  {{ $t('add') }}
                                </v-btn>
                              </div>
                            </v-container>
                          </template>
                        </v-card>
                      </v-menu>
                    </div>
                  </template>
                </v-list>
              </v-card>
            </v-menu>
          </div>
        </v-col>
      </v-row>
      <div>
          <div class="fill-height">
            <v-col
              cols="12"
              sm="12"
              class="flex justify-end pr-10 pb-6"
            >
              <v-menu
                v-if="selectedSteps.length > 0"
                offset-y
                top
                class="rounded-lg"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    depressed
                    background-color="#F2F4F7"
                    class="text-capitalize btn-theme"
                    v-bind="attrs"
                    height="38px"
                    width="100%"
                    max-width="128px"
                    v-on="on"
                  >
                    {{ $t('actions') }}
                    <v-icon size="16px">
                      mdi-chevron-up
                    </v-icon>
                  </v-btn>
                </template>
                <v-card
                  rounded="8"
                  elevation="0"
                >
                  <v-list>
                    <v-list-item
                      :key="'duplicate-steps'"
                      @click="duplicateSelectedSteps"
                    >
                      <div class="d-flex align-center">
                        <duplicateIcon />
                      </div>
                      <v-list-item-title class="pl-3">
                        {{ $t('duplicate') }}
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      :key="'create-shared-step'"
                      @click="handleCreateSharedStep"
                    >
                      <div class="d-flex align-center">
                        <createSharedStepIcon />
                      </div>
                      <v-list-item-title class="pl-3">
                        {{ $t('createSharedStep') }}
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      :key="'add-child-step'"
                      @click="addChildStepToSelectedSteps"
                    >
                      <div class="d-flex align-center">
                        <addChildStepIcon />
                      </div>
                      <v-list-item-title class="pl-3">
                        {{ $t('addChildStep') }}
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      :key="'remove-steps'"
                      @click="removeSelectedSteps"
                    >
                      <div class="d-flex align-center">
                        <removeIcon />
                      </div>
                      <v-list-item-title class="color-red pl-3">
                        {{ $t('remove') }}
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-card>
              </v-menu>

              <v-menu
                v-else
                offset-y
                top
                class="rounded-lg"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    depressed
                    background-color="#F2F4F7"
                    class="text-capitalize btn-theme"
                    v-bind="attrs"
                    height="38px"
                    width="100%"
                    max-width="128px"
                    v-on="on"
                  >
                    {{ $t('actions') }}
                    <v-icon
                      size="16px"
                      class="ml-2"
                    >
                      mdi-chevron-up
                    </v-icon>
                  </v-btn>
                </template>

                <v-card
                  rounded="8"
                  elevation="0"
                >
                  <v-list>
                    <v-list-item @click="caseImprovement">
                      {{ $t('aiAssist.improveWithAi') }}
                    </v-list-item>
                    <v-list-item
                      v-if="!isExecution"
                      :key="'save-template'"
                      @click="saveAsTemplate"
                    >
                      <v-list-item-title>
                        {{ $t('saveAsTemplate') }}
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-card>
              </v-menu>
              <v-btn
                depressed
                class="f-color-white ml-2 btn-theme text-capitalize"
                color="primary"
                width="100%"
                max-width="128px"
                :loading="createBtnLoading"
                :class="{ 'btn-loading-opacity': createBtnLoading }"
                @click="handleClickSave"
              >
                {{ $t('save') }}
              </v-btn>
              <div
                v-if="showComparing"
                class="ai-comparing"
              >
                <p class="mb-0">
                  {{ $t('compare') + ' :' }}
                </p>
                <v-btn-toggle
                  v-model="selectedVersion"
                  class="ai-comparing-actions"
                  @change="toggleCaseVersions"
                >
                  <v-btn
                    text
                    color="#667085"
                    class="justify-start bg-gray-theme"
                    height="100%"
                  >
                    {{ $t('initialVersion') }}
                  </v-btn>
                  <v-btn
                    text
                    color="#0C2FF3"
                    class="justify-start bg-theme-primary-light"
                    height="100%"
                  >
                    {{ $t('improvedVersion') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </v-col>
          </div>
        </div>

        <create-update-shared-step-dialog
          v-model="isOpenCreateUpdateSharedStepDialog"
          :data="selectedSharedStep"
          :is-create-step-loading="isCreateStepLoading"
          @close-dialog="handleCloseCreateUpdateSharedStepDialog"
          @create-shared-step="createSharedStep"
        />
        <CreateUpdateCustomFieldDialog
          v-model="showCreateUpdateCustomFieldDialog"
          @create-custom-field="createCustomField"
          @close-dialog="showCreateUpdateCustomFieldDialog = false"
        />
        <ProjectDiscardDialog
          v-model="showConfirmBackDialog"
          :title="$t('testcase_close_dialog')"
          @close="handleCloseClick"
          @handleConfirmClick="handleConfirmClick"
        />
    </div>
    <AiAssist
      v-if="aiAssist"
      class="flex-shrink-0"
      :templates="templates"
      @closeAssist="closeAssist"
      @response="aiResponse"
    />
  </v-form>
</template>
<script>
import ProjectDiscardDialog from '@/components/Project/ProjectDiscardDialog.vue';
import CustomFieldFromTemplate from '@/views/Tests/Case/Components/CustomFieldFromTemplate.vue';
import StepEditor from '@/views/Tests/Case/Components/StepEditor.vue';
import CreateUpdateSharedStepDialog from '@/components/Admin/SharedStep/CreateUpdateSharedStepDialog.vue';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import makeCasesService from '@/services/api/case';
import makeSharedStepService from '@/services/api/sharedStep';
import makeTemplateService from '@/services/api/template';
import SearchIcon from '@/assets/svg/search-icon.svg';
import removeIcon from '@/assets/svg/remove.svg';
import createSharedStepIcon from '@/assets/svg/create-shared-step.svg';
import CreationOutline from '@/assets/svg/creation-outline.svg';
import addChildStepIcon from '@/assets/svg/add-child-step.svg';
import duplicateIcon from '@/assets/svg/duplicate.svg';
import { requiredAndMax255FieldValidationRules } from '@/utils/validation';
import makeTagService from '@/services/api/tag';
import { createNamespacedHelpers, mapActions } from 'vuex';
import TagSelector from '@/components/base/TagSelector.vue';
import CreateUpdateCustomFieldDialog from '@/components/Admin/CustomField/CreateUpdateCustomFieldDialog.vue';
import makeCustomFieldService from '@/services/api/customField';
import { uuid } from 'vue-uuid';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import makeExecutionService from '@/services/api/execution';
import AiAssist from '@/components/base/AiAssist.vue'
import AiPopup from '@/components/base/AiPopup'
import makeExecutionsService from '@/services/api/execution'
import TextareaField from '@/components/base/TextareaField.vue';
import _ from 'lodash';
import makeAssistService from '@/services/api/assist';
import Loader from '@/components/base/Loader'
import FolderAutocomplete from '@/components/base/FolderAutocomplete.vue';
import QuestionIcon from '@/assets/svg/question-mark.svg';
import ButtonWithMenuItemList from '@/components/base/Forms/CustomButton/ButtonWithMenuItemList.vue';
import TemplateRulesManager from '@/components/Admin/CustomTemplates/TemplateRulesManager.vue';
import { entityTypeNames,entityTypes } from '@/constants/templates';
import { useTestCasesIndex } from '@/composables/modules/cases/index';

const { mapState } = createNamespacedHelpers('user');

let caseService;
let sharedStepService;
let executionsService;
let templateService;

export default {
  components: {
    ProjectDiscardDialog, TextareaField,
    CustomFieldFromTemplate,
    StepEditor,
    CreateUpdateSharedStepDialog,
    SearchIcon,
    removeIcon,
    createSharedStepIcon,
    addChildStepIcon,
    duplicateIcon,
    TagSelector,
    CreateUpdateCustomFieldDialog,
    CreationOutline,
    AiPopup,
    AiAssist,
    Loader,
    FolderAutocomplete,
    QuestionIcon,
    ButtonWithMenuItemList,
    TemplateRulesManager
  },
  mixins: [colorPreferencesMixin],
  setup() {
    const { clearCache } = useTestCasesIndex();
    
    return {
      clearTestCasesCache: clearCache
    };
  },
  data()
  {
    return {
      showConfirmBackDialog: false,
      isEditMode: false,
      showCreateUpdateCustomFieldDialog: false,
      caseName: '',
      caseID: '',
      templateId: '',
      casePriority: null,
      caseTags: [],
      originalTags: [],
      files: [],
      selectedFolderUID: '',
      editItem: {},
      isCreateStepLoading: false,
      isOpenCreateUpdateSharedStepDialog: false,
      selectedSharedStep: {},
      menu: false,
      steps: [],
      stepCount: 0,
      customFieldsFromTemplate: [],
      selectedItems: [],
      selectedItem: null,
      searchTerm: '',
      templatesGrouped: {
        testCase: [],
        testResult: [],
      },
      customFieldsByEntityType: {
        testCase: [],
        testResult: []
      },
      templates: [],
      tags: [],
      selectedSteps: [],
      stepUniqueId: 0,
      isCreateTestCaseFormValid: false,
      titleValidation: requiredAndMax255FieldValidationRules(this),
      createBtnLoading: false,
      searchCustomField: '',
      customFieldMenuRef: false,
      customFields: [],
      initialFiles: [],
      selectedCustomFields: [],
      customFieldService: null,
      isLoading: false,
      statuses: [],
      priorities: [],
      aiAssist: false,
      showComparing: false,
      selectedVersion: null,
      initialVersion: null,
      improvedVersion: null,
      expectedResultByStep: false,
      expectedResult: '',
      selectedFormat: 'testCase',
      selectedGroup: '',
      assistField: {
        fieldKey: null,
        fieldName: null,
        fieldType: null,
        oldValue: null,
        position: {
          x: 0,
          y: 0,
        }
      },
      regenerate: 0,
      isCaseImproving: false,
      entityTypeNames: entityTypeNames,
      entityTypes: entityTypes,
      resultRules: [],
      customFieldsResult: [],
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    filteredSelectedItems()
    {
      if (!this.searchTerm) {
        return this.selectedItems;
      }
      return this.selectedItems.filter((item) => item.name.toLowerCase().includes(this.searchTerm.toLowerCase()));
    },
    isFilteredSelectedItemsHasData()
    {
      return this.filteredSelectedItems.length > 0;
    },
    stepsLength()
    {
      return this.steps?.length;
    },
    filteredTemplates() {
      return this.templates
        ?.filter(t => t.entityType === this.selectedFormat)
      || [];
    },
    filteredCustomFields()
    {
      if (!this.searchCustomField) {
        return this.customFields;
      }
      const searchTerm = this.searchCustomField.toLowerCase();
      return this.customFields.filter((field) => field.name.toLowerCase().includes(searchTerm));
    },
    isExecution()
    {
      return this.$route.query.isExecution && this.$route.query.executionId;
    },
    isTestRunEdit()
    {
      return this.$route.query.redirectTo == 'TestRunEdit' && this.$route.query.runId;
    },
    isTestRunCreate()
    {
      return this.$route.query.redirectTo == 'TestRunCreate';
    },
    filteredCustomFieldsFromTemplate()
    {
      return this.customFieldsFromTemplate.filter((field) => field.entityType === this.selectedFormat);
    },
    customFieldsFromTemplateGeneralInfo()
    {
      return this.filteredCustomFieldsFromTemplate.filter((field) => field.templateType === 'generalInfo');
    },
    customFieldsFromTemplateDetails()
    {
      return this.filteredCustomFieldsFromTemplate.filter((field) => field.templateType === 'details');
    },
    filteredCustomFieldsByFormat() {
      return this.customFields?.filter((field) => 
        field.entityTypes && field.entityTypes.includes(this.selectedFormat)
      ) || [];
    },
  },

  destroyed()
  {
    window.removeEventListener("resize", this.handleResize);
  },
  async created()
  {
    window.addEventListener("resize", this.handleResize);
    caseService = makeCasesService(this.$api);
    templateService = makeTemplateService(this.$api);
    sharedStepService = makeSharedStepService(this.$api);

    this.priorities = this.getPriorities('testCase').filter(element => !element.archived);
    this.statuses = this.getStatuses('testCase').filter(element => !element.archived);

    const tagService = makeTagService(this.$api);
    executionsService = makeExecutionsService(this.$api);
    try {
      const response = await tagService.getTags(this.$route.params.handle, 'cases');
      if (response.status === 200) {
        this.tags = response.data.map((tag) =>
        {
          return { uid: tag.uid, name: tag.name };
        });
      } else {
        showErrorToast(this.$swal, 'failedToFetchTags', { item: this.$t('tags') });
      }
    } catch (error) {
      showErrorToast(this.$swal, 'errorFetchingTags', { item: this.$t('tags') }, error?.response?.data);
    }

    if (this.$route.params.uid) {
      this.isEditMode = true;
      try {
        if (!this.$route.query.isExecution) {
          const response = await caseService.getCase(
            this.$route.params.handle,
            this.$route.params.key,
            this.$route.params.uid
          );

          if (response.status === 200) {
            this.editItem = response.data;

            // Fetch tag relations for this case
            if (this.editItem && this.editItem.testCaseRef) {
              try {
                const tagRelationsRes = await caseService.getCaseRelations(
                  this.$route.params.handle,
                  this.$route.params.key,
                  'tag',
                  [this.editItem.testCaseRef]
                );
                if (tagRelationsRes.status === 200) {
                  const tagRelations = tagRelationsRes.data;
                  const caseTags = tagRelations[this.editItem.testCaseRef] || [];
                  this.editItem.tags = caseTags.map(tag => ({
                    uid: tag.uid,
                    name: tag.name
                  }));
                }
              } catch (tagErr) {
                console.warn('Failed to fetch tag relations for case:', tagErr);
                // Continue without tags rather than failing completely
              }
            }

            this.selectedFolderUID = this.editItem.parentUid;
            this.templateId = this.editItem.testTemplateUid;
            this.files = this.editItem.attachments;
            this.selectedFormat = this.editItem?.customFields?.entityType || 'testCase';
            this.resultRules = this.editItem?.customFields?.rules || [];

            // Initialize custom fields from template if they exist
            if (this.editItem.customFields && this.editItem.customFields.templateFields) {

              const savedFields = this.editItem.customFields?.templateFields || [];
              const currentTemplateFields = this.templates.find(template => template.uid === this.templateId).customFields?.templateFields || [];

              this.customFieldsFromTemplate = currentTemplateFields.map((templateField) =>
              {
                const savedField = savedFields.find(
                  (saved) => saved.name === templateField.name
                );
                return {
                  ...templateField,
                  id: templateField.uid || uuid.v4(),
                  value: savedField ? (savedField.value || '') : (templateField.value || ''),
                };
              });

              if(this.isEditMode) {
                

                const baseFields = currentTemplateFields.map(field => {
                  const saved = savedFields.find(sf => sf.name === field.name);
                  const result = {
                    ...field,
                    id: field.uid || uuid.v4(),
                    value: saved?.value ?? field.value ?? ""
                  };
                  
                  if (!result.entityType) {
                    result.entityType = this.selectedFormat;
                  }
                  
                  return result;
                });

                const extraFields = savedFields
                  .filter(sf => !currentTemplateFields.some(tf => tf.name === sf.name))
                  .map(sf => {
                    const result = {
                      ...sf,
                      id: sf.uid || uuid.v4(),
                      value: sf.value || ""
                    };

                    if (!result.entityType) {
                      result.entityType = this.selectedFormat;
                    }
                    
                    return result;
                  });


                const mergedFields = [
                  ...baseFields,
                  ...extraFields
                ];

                this.customFieldsFromTemplate = mergedFields;

              }

            }
            this.expectedResult = this.editItem.customFields?.expectedResult;
            this.expectedResultByStep = this.editItem.customFields?.expectedResultByStep;

            this.stepUniqueId = this.getHighestStepId(this.editItem.steps);
            this.inputEditValues();

          } else {
            showErrorToast(this.$swal, 'genericError', { message: response.msg });
          }
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('testCase') }, error?.response?.data);
      }
    } else {
      this.stepUniqueId = 0;
    }

    let customFields = {};

    if (this.$route.params.uid && this.$route.query.isExecution && this.$route.query.executionId) {
      const executionService = makeExecutionService(this.$api);

      await executionService
        .getExecution(this.$route.params.handle, this.$route.params.key, this.$route.query.executionId)
        .then((response) =>
        {
          if (response.status === 200) {
            this.editItem = response.data;
            this.files = response.data?.attachments;
            this.initialFiles = response.data?.attachments;
            this.expectedResult = response.data?.expectedResult;
            this.stepUniqueId = this.getHighestStepId(this.editItem.steps);
            this.inputEditValues();
          }
          customFields = response.data?.templateFields;
          this.selectedFormat = response.data?.customFields?.entityType;
          this.resultRules = response.data?.customFields?.rules || [];

          if (customFields) {
            this.customFieldsFromTemplate = customFields ?? [];
          }

        });
    }
  },
  async mounted()
  {
    this.selectedFolderUID = parseInt(this.$route.query.folderUid) || '';
    this.fetchSharedSteps();

    await this.init();
    this.setTemplates();

    this.customFieldService = makeCustomFieldService(this.$api);
    this.getCustomFields();

  },
  methods: {
    ...mapActions({
      uploadToServer: 'attachment/uploadToServer',
    }),
    setTemplates(){
      if (!this.isEditMode) {
        const defaultTemplate = this.filteredTemplates.find(template => template.isDefault === true);
        if (defaultTemplate) {
          this.templateId = defaultTemplate.uid;
          this.getCustomFieldsFromTemplate(defaultTemplate.uid);
        }
      } else {
        this.getCustomFieldsFromTemplate(this.templateId);
      }
    },
    setFormat(format)
    {
      this.selectedFormat = format;
      this.steps = [];
      this.setTemplates();
    },
    async init(){
      try {
        const entityTypeKeys = Object.keys(this.entityTypeNames);
        const templatePromises = entityTypeKeys.map(entityType => 
          this.initTemplates(entityType)
        );

        const allResults = await Promise.allSettled([
          ...templatePromises,
        ]);

        const templateResults = allResults.slice(0, entityTypeKeys.length);
        templateResults.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`Failed to fetch templates for ${entityTypeKeys[index]}:`, result.reason);
            showErrorToast(this.$swal, 'fetchError', {
              item: `${entityTypeKeys[index]} templates`
            }, result.reason?.response?.data);
          }
        });
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } 
    },
    async initTemplates(entityType) {
      const searchParams = new URLSearchParams();
      searchParams.set('current_page', 1);
      searchParams.set('per_page', 9999);
      searchParams.set('entityType', entityType)

      try {
        const response = await templateService.getTemplates(
          this.$route.params.handle,
          this.$route.params.key,
          searchParams.toString()
        );
        
        this.templatesGrouped[entityType] = response.data?.templates || [];
        this.templates.push(...this.templatesGrouped[entityType]);

      } catch (error) {
        console.log(error)
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      }
    },
    closePopupAssist()
    {
      this.assistField = {
        fieldType: null,
        fieldKey: null,
        oldValue: null,
        position: {
          x: 0,
          y: 0
        }
      }
    },
    popupAssist(fieldKey, fieldType, fieldName)
    {
      this.assistField.fieldKey = fieldKey;
      this.assistField.fieldType = fieldType;
      this.assistField.fieldName = fieldName;
      this.regenerate = 0;
      this.handleResize()
    },
    openAssist()
    {
      this.aiAssist = true;
      this.handleResize()
    },
    closeAssist()
    {
      this.aiAssist = false;
      this.handleResize()
    },
    handleResize()
    {
      if (this.assistField.fieldType && this.assistField.fieldKey) {
        this.$nextTick(() =>
        {
          const assistField = this.$refs[this.assistField.fieldKey][0].$refs[this.assistField.fieldType].$el.getBoundingClientRect()
          this.assistField.position = {
            x: assistField.left + window.scrollX + 380,
            y: assistField.top + window.scrollY - 30
          }
        })
      }
    },
    setCaseVersion(steps, customFields)
    {
      let newSteps = _.cloneDeep(this.steps);
      if (steps && steps.length)
        for (let [index, step] of steps.entries()) {
          newSteps[index].description = step.description
          newSteps[index].expectedResult = step.expectedResult

          if (step.children && newSteps[index].children) {
            const children = [];
            for (let [childIndex, childStep] of step.children.entries())
              children.push({
                ...newSteps[index].children[childIndex],
                description: childStep.description,
                expectedResult: childStep.expectedResult
              })
            newSteps[index].children = children


          }
        }
      this.steps = newSteps;

      const newCustomFields = _.cloneDeep(this.customFieldsFromTemplate);
      if (customFields && customFields.length)
        for (let [index, customField] of customFields.entries()) {
          newCustomFields[index].value = customField.fieldValue
        }

      this.customFieldsFromTemplate = newCustomFields;
    },
    handleBackClick()
    {
      this.showConfirmBackDialog = true;
    },
    handleCloseClick()
    {
      this.showConfirmBackDialog = false;
    },
    handleConfirmClick()
    {
      this.$router.push({
        name: 'Cases',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
        },
      });
    },
    toggleCaseVersions()
    {
      if (this.selectedVersion) {
        this.setCaseVersion(this.improvedVersion.steps, this.improvedVersion.customFields)
      } else {
        this.setCaseVersion(this.initialVersion.steps, this.initialVersion.customFields)
      }
    },
    aiResponse(asisstSuggestions, templateId)
    {
      this.closePopupAssist();
      this.templateId = templateId
      this.getCustomFieldsFromTemplate(templateId);
      if (asisstSuggestions.inputFields.length)
        asisstSuggestions.inputFields.forEach(element =>
        {
          this.customFieldsFromTemplate.forEach(templateField =>
          {
            if (element.fieldName?.toLowerCase() == templateField.name?.toLowerCase()) {
              const fieldIndex = this.customFieldsFromTemplate.findIndex(field => field.name.toLowerCase() == element.fieldName?.toLowerCase())
              this.customFieldsFromTemplate[fieldIndex] = {
                ...this.customFieldsFromTemplate[fieldIndex],
                value: element.fieldValue
              }
            }
          })
        })
      if (asisstSuggestions.steps.length) {
        const steps = asisstSuggestions.steps.map(item =>
        {
          this.stepUniqueId++
          return {
            id: this.stepUniqueId,
            description: item.description,
            expectedResult: item.expectedResult,
            shared: false
          }
        })
        this.steps = steps;
      }
      if(asisstSuggestions.expectedResult){
        this.expectedResult = asisstSuggestions.expectedResult;
      }
    },
    async generateImprovement(userPrompt)
    {
      const assistService = makeAssistService(this.$api);
      const handle = this.$route.params.handle
      if (this.assistField.fieldKey && this.assistField.fieldType) {
        const input = this.$refs[this.assistField.fieldKey][0].$refs[this.assistField.fieldType];
        if (!input.value)
          return showErrorToast(this.$swal, this.$t('aiAssist.fieldEmpty', { input: this.assistField.fieldName }));

        const payload = {
          inputFields: [
            {
              fieldName: this.assistField.fieldName,
              fieldValue: input.value,
            }
          ],
          userPrompt,
          type: 'fieldImprovement'
        }

        await assistService.getAssistResponse(handle, payload).then(response =>
        {
          const key = Number(this.assistField.fieldKey.split('-')[1]);
          if (this.assistField.fieldKey.includes('step')) {
            this.assistField.oldValue = this.steps[key][this.assistField.fieldType];
            const newStep = {
              ...this.steps[key],
              [this.assistField.fieldType]: response.data.inputFields[0].fieldValue
            }
            this.steps.splice(key, 1, newStep)
          }
          if (this.assistField.fieldKey.includes('customField')) {
            this.assistField.oldValue = this.customFieldsFromTemplate[key].value
            const newField = {
              ...this.customFieldsFromTemplate[key],
              value: response.data.inputFields[0].fieldValue
            }
            this.customFieldsFromTemplate.splice(key, 1, newField)
          }
          this.regenerate = 1
        });
      }
    },
    async caseImprovement()
    {
      this.isCaseImproving = true;

      const handle = this.$route.params.handle;
      const steps = this.steps.map(item =>
      {
        return {
          description: item.description,
          expectedResult: item.expectedResult,
          ...(item.children?.length ? {
            children: item.children.map(child =>
            {
              return {
                description: child.description,
                expectedResult: child.expectedResult,
              }
            })
          } : undefined)
        }
      })
      const inputFields = this.customFieldsFromTemplate.map(item =>
      {
        return {
          fieldName: item.name,
          fieldValue: item.value
        }
      });

      const assistService = makeAssistService(this.$api);
      const payload = {
        type: 'caseImprovement',
        ...(this.caseName ? { caseTitle: this.caseName } : undefined),
        ...(this.casePriority?.name ? { casePriority: this.casePriority.name } : undefined),
        steps,
        inputFields
      }
      await assistService.getAssistResponse(handle, payload).then(response =>
      {
        this.improvedVersion = {
          steps: response.data.steps,
          inputFields: response.data.inputFields
        }
        this.initialVersion = {
          inputFields,
          steps
        }
        this.showComparing = true;
        this.selectedVersion = 1;
        this.setCaseVersion(this.improvedVersion.steps, this.improvedVersion.inputFields)
      }).catch((err) =>
      {
        showErrorToast(this.$swal, this.$t('aiAssist.caseImprovementFailed', { input: this.assistField.fieldName }), {}, err?.response?.data);
      })

      this.isCaseImproving = false;

    },
    rejectImprovement()
    {
      const key = Number(this.assistField.fieldKey.split('-')[1]);
      if (this.assistField.fieldKey.includes('step')) {
        const newStep = {
          ...this.steps[key],
          [this.assistField.fieldType]: this.assistField.oldValue
        }
        this.steps.splice(key, 1, newStep)
      }
      if (this.assistField.fieldKey.includes('customField')) {
        const newField = {
          ...this.customFieldsFromTemplate[key],
          value: this.assistField.oldValue
        }
        this.customFieldsFromTemplate.splice(key, 1, newField)
      }
      this.closePopupAssist();
    },
    async createCustomField(customField)
    {
      this.showCreateUpdateCustomFieldDialog = false;

      try {
        const response = await this.customFieldService.createCustomField(
          this.currentAccount.handle,
          this.$route.params.key,
          {
            ...customField,
            source: 'Manual',
            templateType: this.selectedGroup
          }
        );
        showSuccessToast(this.$swal, 'createSuccess', { item: this.$t('customField') });

        const newCustomField = {
          ...customField,
          uid: response.data.uid,
          value: customField.value || '',
        };
        this.customFieldsFromTemplate.push(newCustomField);

        this.getCustomFields();
      } catch (err) {
        showErrorToast(this.$swal, 'createError', { item: this.$t('customField') }, err?.response?.data);
      }
    },
    updateFiles(files)
    {
      this.files = files;
    },
    handleTagsChange(selectedTagUids)
    {
      this.caseTags = selectedTagUids.map((tagUid) =>
      {
        const tag = this.tags.find((t) => t.value === tagUid);
        return { uid: tag.value, name: tag.text };
      });
    },
    toggleSelection(item)
    {
      const index = this.caseTags.indexOf(item.text);
      if (index === -1) {
        this.caseTags.push(item.text);
      } else {
        this.caseTags.splice(index, 1);
      }
    },

    findLeafNodes(folders)
    {
      let nodes = [];
      for (let idx = 0; idx < folders.length; idx++) {
        let parent = {
          name: folders[idx].name,
          uid: folders[idx].uid,
        };
        if (!folders[idx].children || folders[idx].children.length < 1) {
          nodes.push(parent);
        } else {
          nodes.push(parent, ...this.findLeafNodes(folders[idx].children));
        }
      }
      return nodes;
    },
    inputEditValues()
    {
      this.caseName = this.editItem.name;
      this.casePriority = this.editItem.priority;
      this.caseTags = this.editItem?.tags.map((tag) =>
      {
        return { uid: tag.uid, name: tag.name };
      });
      this.originalTags = this.caseTags;
      this.caseID = `${this.$route.params.key}-${this.editItem.testCaseRef ?? this.editItem.uid}`;
      this.steps = this.editItem.steps?.sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );
      this.templateId = this.editItem.testTemplateUid;
    },
    handleCreateSharedStep()
    {
      this.selectedSharedStep = {
        uid: '',
        name: '',
        steps: [],
        expectedResultByStep: this.expectedResultByStep,
      };
      this.isOpenCreateUpdateSharedStepDialog = true;
    },
    handleCloseCreateUpdateSharedStepDialog()
    {
      this.isOpenCreateUpdateSharedStepDialog = false;
    },

    async createSharedStep(sharedStep)
    {
      try {
        this.isCreateStepLoading = true;
        // Ensure the expectedResultByStep property is included in the payload
        const sharedStepPayload = {
          ...sharedStep,
          expectedResultByStep: sharedStep.expectedResultByStep
        };
        const response = await sharedStepService.createSharedStep(
          this.currentAccount.handle,
          this.$route.params.key,
          sharedStepPayload
        );
        if (response.status === 200) {
          showSuccessToast(this.$swal, 'createSuccess', { item: this.$t('sharedStep') });
          this.isOpenCreateUpdateSharedStepDialog = false;
          this.fetchSharedSteps();
        } else {
          showErrorToast(this.$swal, 'createError', { item: this.$t('sharedStep') });
        }
      } catch (err) {
        showErrorToast(this.$swal, 'createError', { item: this.$t('sharedStep') }, err?.response?.data);
      } finally {
        this.isCreateStepLoading = false;
      }
    },
    async fetchSharedSteps()
    {
      try {
        // Use a large limit to get all available shared steps for the dropdown
        // Only get active (non-archived) shared steps for case creation
        const response = await sharedStepService.getSharedSteps(
          this.currentAccount.handle, 
          this.$route.params.key,
          {
            limit: 1000,
            offset: 0,
            archived: false,
            active: true,
            filters: {}
          }
        );
        if (response.status === 200) {
          this.selectedItems = response.data.items.filter((step) => !step.archivedAt);
        }
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('sharedStepPage.title') }, err?.response?.data);
      }
    },
    addStep()
    {
      this.stepUniqueId++;
      let step = {
        id: this.stepUniqueId,
        description: '',
        expectedResult: '',
        children: [],
        shared: false,
        title: this.$t('step', { index: this.steps.length + 1 })
      };
      this.steps.push(step);
    },
    addSharedStep()
    {
      if (this.selectedItem !== null) {
        // Check if the shared step has expectedResultByStep property
        const hasExpectedResultByStep = this.selectedItem.expectedResultByStep !== undefined
          ? this.selectedItem.expectedResultByStep
          : this.expectedResultByStep;

        let selectedSharedSteps = this.selectedItem.steps.map((step) => ({
          ...step,
          uid: this.selectedItem.uid,
        }));

        selectedSharedSteps.forEach((step) =>
        {
          this.stepUniqueId++;
          this.steps.push({
            ...step,
            id: this.stepUniqueId,
            sharedStepUid: step.uid,
            // Only include expectedResult if expectedResultByStep is true
            ...(hasExpectedResultByStep ? {} : { expectedResult: undefined }),
          });
        });
        this.menu = false;
        this.searchTerm = '';
        this.selectedItem = null;
      }
    },
    async deleteAttachment(id)
    {
      if (!this.isExecution) {
        const params = {
          handle: this.$route.params.handle,
          projectKey: this.$route.params.key
        }
        await caseService.deleteAttachments({ params, id }).then(() =>
        {
          showSuccessToast(this.$swal, this.$t('success.deleteAttachment'))
        }).catch((err) =>
        {
          showErrorToast(this.$swal, this.$t('error.failedToDeleteAttachment'), {}, err?.response?.data)
        })
      } else {
        try {
          const response = await executionsService.deleteAttachments({
            id: id, params: {
              handle: this.$route.params.handle,
              projectKey: this.$route.params.key
            }
          });
          if (response.status === 200) {
            showSuccessToast(this.$swal, this.$t('toast.deleteSuccess', { item: 'Attachment' }));
          }
        } catch (err) {
          showErrorToast(this.$swal, this.$t('toast.deleteError', { item: 'Attachment' }), {}, err?.response?.data)
        }
      }
    },
    deleteStep(index)
    {
      this.steps.splice(index, 1);
      this.closePopupAssist()
    },
    duplicateStep(index)
    {
      const stepToDuplicate = this.steps[index];
      const duplicatedStep = JSON.parse(JSON.stringify(stepToDuplicate));
      duplicatedStep.id = this.stepsLength + 1;
      // insert duplicated step after the current step
      this.steps.splice(index + 1, 0, duplicatedStep);
    },
    changeResult(result, stepId)
    {
      let step = this.getStepFromId(stepId);
      this.steps[step].expectedResult = result;
    },

    changeDescription(description, stepId)
    {
      let step = this.getStepFromId(stepId);
      this.steps[step].description = description;
    },

    changeChildren(children, stepIndex)
    {
      this.$set(this.steps[stepIndex], 'children', children);
    },
    getStepFromId(stepId)
    {
      let steps = this.steps;
      for (let step in steps) {
        if (steps[step].id == stepId) {
          return step;
        }
      }
    },
    addChildStepToSelectedSteps()
    {
      this.selectedSteps.forEach((parentIndex) =>
      {
        const parentStep = this.steps[parentIndex];

        if (!parentStep.children) {
          parentStep.children = [];
        }

        this.stepUniqueId++;
        const newChildStep = {
          id: this.stepUniqueId,
          description: '',
          expectedResult: '',
          children: [],
          shared: false,
          title: ''
        };

        parentStep.children.push(newChildStep);
      });

      this.selectedSteps = [];
    },
    toggleStepSelection(index, selected)
    {
      if (selected) {
        this.selectedSteps.push(index);
      } else {
        this.selectedSteps = this.selectedSteps.filter((i) => i !== index);
      }
    },

    removeSelectedSteps()
    {
      const sortedIndices = [...this.selectedSteps].sort((a, b) => b - a);

      sortedIndices.forEach((index) =>
      {
        this.steps.splice(index, 1);
      });

      this.selectedSteps = [];
    },
    renumberSteps()
    {
      this.steps.forEach((step, index) =>
      {
        step.title = `Step ${index + 1}`;
        if (step.children) {
          step.children.forEach((childStep, childIndex) =>
          {
            childStep.title = `Step ${index + 1}.${childIndex + 1}`;
          });
        }
      });
    },

    duplicateSelectedSteps()
    {
      this.selectedSteps.sort((a, b) => a - b);

      this.selectedSteps.forEach((index) =>
      {
        const stepToDuplicate = this.steps[index];
        const duplicatedStep = JSON.parse(JSON.stringify(stepToDuplicate));
        duplicatedStep.id = this.stepsLength + 1;
        this.steps.push(duplicatedStep);
      });
      this.selectedSteps = [];
    },
    validateForm()
    {
      return this.$refs.form.validate();
    },
    resetForm()
    {
      return this.$refs.form.reset();
    },
    resetValidationForm()
    {
      return this.$refs.form.resetValidation();
    },
    handleClickSave()
    {
      if (this.validateForm()) {
        if (this.isEditMode) {
          this.saveTestCase();
        } else {
          this.createTestCase();
        }
      }
    },
    async createTestCase()
    {
      if (this.currentAccount.handle) {
        const handle = this.currentAccount.handle;

        const steps = this.steps.map((item, index) =>
        {
          const stepData = {
            ...item,
            title: item.title || this.$t('step', { index: index + 1 }),
            children: item.children?.map((child, childIndex) =>
            {
              const childData = {
                ...child,
                title: child.title || this.$t('step', { index: childIndex + 1 })
              };
              // Conditionally remove expectedResult from child steps
              if (!child.expectedResult) {
                delete childData.expectedResult;
              }
              return childData;
            }),
          };
          // Conditionally remove expectedResult from parent steps
          if (!item.expectedResult) {
            delete stepData.expectedResult;
          }
          return stepData;
        });

        let data = {
          name: this.caseName,
          source: 'testfiesta',
          projectKey: this.$route.params.key,
          parentId: this.selectedFolderUID,
          templateId: this.templateId,
          priority: this.casePriority?.id,
          customFields: {
            templateFields: this.customFieldsFromTemplate,
            expectedResultByStep: this.expectedResultByStep,
            ...(this.expectedResult && { expectedResult: this.expectedResult }),
            entityType: this.selectedFormat,
          },
          tagIds: this.caseTags.map((tag) => tag.uid),
          steps: steps
        };

        if(this.selectedFormat == 'testResult') {
          data.customFields.rules = this.resultRules;
        } 

        try {
          const mediaType = 'attachment';
          this.createBtnLoading = true;
          const response = await caseService.createTestCase(handle, this.$route.params.key, data);

          if (response.status == 200) {
            showSuccessToast(this.$swal, 'createSuccess', { item: this.$t('testCase') });
            // Clear cache so the new test case appears immediately in the list
            this.clearTestCasesCache();
            
            if (this.files.length) {
              try {
                await Promise.all(
                  this.files.map(async (file) =>
                  {
                    await this.uploadToServer({
                      handle,
                      mediaType,
                      file,
                      apiService: caseService,
                      params: {
                        handle,
                        projectKey: this.$route.params.key,
                        caseId: response.data.uid
                      }
                    });
                  })
                );
              } catch (err) {
                if (err?.status == 507)
                  showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
                else
                  showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data)
              }
            }
            if (this.isTestRunEdit || this.isTestRunCreate) {
              this.$router.push({
                name: this.isTestRunEdit ? 'TestRunEdit' : 'TestRunCreate',
                params: {
                  handle: this.$route.params.handle,
                  key: this.$route.params.key,
                  id: this.isTestRunEdit ? this.$route.query.runId : null,
                  folderUid: this.isTestRunEdit ? this.selectedFolderUID ?? this.$route.query.folderUid : null,
                },
              });
              return;
            }
            if (this.selectedFolderUID) {
              this.$router.push({
                name: 'Cases'
              });
            } else {
              this.$router.push({ name: 'Cases' });
            }
          } else {
            showErrorToast(this.$swal, 'createError', { item: this.$t('testCase') });
          }
        } catch (err) {
          showErrorToast(this.$swal, 'createError', { item: this.$t('testCase') }, err?.response?.data);
        } finally {
          this.createBtnLoading = false;
        }
      }
    },
    async saveTestCase()
    {
      const handle = this.currentAccount.handle;
      const projectKey = this.$route.params.key;

      const steps = this.steps.map((item, index) =>
      {
        const stepData = {
          uid: item.uid,
          position: item.position || index + 1,
          description: item.description,
          title: item.title || this.$t('step', { index: index + 1 }),
          children: item.children?.map((child, childIndex) =>
          {
            const childData = {
              uid: child.uid,
              position: child.position || childIndex + 1,
              description: child.description,
              title: child.title || this.$t('step', { index: childIndex + 1 })
            };
            // Conditionally remove expectedResult from child steps
            if (this.expectedResultByStep) {
              childData.expectedResult = child.expectedResult;
            }
            return childData;
          }),
        };
        // Conditionally remove expectedResult from parent steps
        if (this.expectedResultByStep) {
          stepData.expectedResult = item.expectedResult;
        }

        return stepData;
      });

      const { tagIds, tagReplacements } = this.tagFomation(
        this.originalTags.map((tag) => tag.uid),
        this.caseTags.map((tag) => tag.uid)
      );
      if (this.$route.query?.isExecution && this.$route.query?.executionId) {
        const executionId = this.$route.query.executionId;
        const payload = {
          name: this.caseName,
          tags: this.caseTags,
          steps: steps,
          tagUids: tagIds,
          tagReplacements: tagReplacements,
          templateFields: this.customFieldsFromTemplate,
          customFields: {
            expectedResultByStep: this.expectedResultByStep,
            ...(this.expectedResult && { expectedResult: this.expectedResult }),
          },
        };

        this.expectedResultByStep && this.expectedResult && (payload.expectedResult = this.expectedResult);
        const executionService = makeExecutionService(this.$api);

        try {
          const response = await executionService.updateExecution(handle, projectKey, executionId, payload)
          if (response.status === 200) {
            showSuccessToast(this.$swal, 'updateSuccess', { item: this.$t('testCase') });
            if (this.files.length > 0) {
              const mediaType = 'attachment';
              const params = {
                handle,
                projectKey,
                executionUid: executionId
              }

              const filesToUpload = this.files.filter(
                file => !this.initialFiles.some(initialFile => initialFile.uid === file.uid)
              );
              try {
                await Promise.all(
                  filesToUpload.map(async (file) =>
                  {
                    await this.uploadToServer({ handle, mediaType, file, apiService: executionService, params });
                  })
                )
              } catch (err) {
                if (err?.status == 507)
                  showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
                else
                  showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data)
                return;
              }
            }
          }
        } catch (err) {
          showErrorToast(this.$swal, 'updateError', { item: this.$t('testCase') }, err?.response?.data);
        }
        this.$router.push({ name: this.$route.query.redirectTo });
        return;
      }

      const payload = {
        name: this.caseName,
        projectKey: this.$route.params.key,
        templateId: this.templateId,
        parentId: this.selectedFolderUID,
        source: 'testfiesta',
        priority: this.casePriority?.id,
        customFields: {
          templateFields: this.customFieldsFromTemplate,
          expectedResultByStep: this.expectedResultByStep,
          ...(this.expectedResult && { expectedResult: this.expectedResult }),
          entityType: this.selectedFormat,
        },
        tagIds: tagIds,
        tagReplacements: tagReplacements,
        steps: steps,
      };

      if(this.selectedFormat == 'testResult') {
        payload.customFields.rules = this.resultRules;
      } 

      try {
        this.createBtnLoading = true;
        const response = await caseService.updateTestCase(
          handle,
          this.$route.params.key,
          this.editItem.testCaseRef,
          payload
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, 'updateSuccess', { item: this.$t('testCase') });
          // Clear cache so the updated test case reflects immediately in the list
          this.clearTestCasesCache();
          
          if (this.files?.length)
            try {
              await Promise.all(
                this.files.map(async (file) =>
                {
                  if (!file.uid)
                    await this.uploadToServer({
                      handle,
                      mediaType: 'attachment',
                      file,
                      apiService: caseService,
                      params: {
                        handle,
                        projectKey: this.$route.params.key,
                        caseId: response.data.testCaseRef
                      }
                    });
                })
              );
            } catch (err) {
              if (err?.status == 507)
                showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
              else
                showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data)
            }
          if (this.isTestRunEdit || this.isTestRunCreate) {
            this.$router.push({
              name: this.isTestRunEdit ? 'TestRunEdit' : 'TestRunCreate',
              params: {
                handle: this.$route.params.handle,
                key: this.$route.params.key,
                id: this.$route.query.runId,
                folderUid: this.selectedFolderUID ?? this.$route.query.folderUid,
              },
            });
          } else if (this.selectedFolderUID) {
            this.$router.push({
              name: 'Cases'
            });
          } else {
            this.$router.push({ name: 'Cases' });
          }
        }
      } catch (err) {
        showErrorToast(this.$swal, 'updateError', { item: this.$t('testCase') }, err?.response?.data);
      } finally {
        this.createBtnLoading = false;
      }
    },

    tagFomation(originalTags, newTags)
    {
      const tagIdsToRemove = originalTags.filter((tagId) => !newTags.includes(tagId));
      const tagIdsToAdd = newTags.filter((tagId) => !originalTags.includes(tagId));

      const tagReplacements = [];

      if (tagIdsToRemove?.length > 0) {
        tagReplacements.push({
          existingTagUids: tagIdsToRemove,
          newTagUids: [],
        });
      }

      if (tagIdsToAdd?.length > 0) {
        tagReplacements.push({
          existingTagUids: [],
          newTagUids: tagIdsToAdd,
        });
      }

      return {
        tagUids: tagIdsToAdd,
        tagReplacements,
      };
    },
    getCustomFieldsFromTemplate(templateId)
    {
      const selectedTemplate = this.templates.find((template) => template.uid === templateId);
      if (this.isEditMode && selectedTemplate) {
        if (this.editItem.testTemplateUid === templateId) {

          // Use existing template fields if template hasn't changed
          this.customFieldsFromTemplate = this.editItem.customFields.templateFields.map((field) => ({
            ...field,
            id: field.uid || uuid.v4(),
            value: field.value || '',
          }));
        } else {
          // Map new template fields if template has changed
          this.customFieldsFromTemplate = selectedTemplate.customFields.templateFields.map((field) => ({
            ...field,
            id: field.uid || uuid.v4(),
            value: field.value || '',
          }));
        }
      } else {
        // If we're not in edit mode
        if (!selectedTemplate) {
          return;
        } else {
          this.customFieldsFromTemplate = selectedTemplate.customFields.templateFields.map((field) => ({
            ...field,
            id: field.uid || uuid.v4(),
            value: field.value || '',
            entityType: this.selectedFormat,
            templateType: selectedTemplate.templateType || 'generalInfo',
          }));
          this.resultRules = selectedTemplate.rules || [];
        }
      }
    },
    handleTemplateChange(value)
    {
      this.getCustomFieldsFromTemplate(value);
    },
    getHighestStepId(steps)
    {
      let highestId = 0;
      steps.forEach((step) =>
      {
        if (step.id > highestId) {
          highestId = step.id;
        }
        if (step.children) {
          const childHighestId = this.getHighestStepId(step.children);
          if (childHighestId > highestId) {
            highestId = childHighestId;
          }
        }
      });
      return highestId;
    },
    selectCustomField(field)
    {
      // Handle the custom field selection here
      this.customFieldsFromTemplate.push(field);
    },
    closeCustomFieldMenu()
    {
      this.customFieldMenuRef = false;
      this.selectedCustomFields = []; // Reset array instead of single value
    },
    addCustomField(selectedField)
    {
      if (selectedField && Array.isArray(selectedField)) {
        selectedField?.forEach((field) => {
          if (!this.isCustomFieldSelected(field)) {
            this.customFieldsFromTemplate.push({
              dataType: field.type,
              id: field.uid || uuid.v4(),
              value: field.value || '',
              options: field.options || [],
              name: field.name || '',
              entityType: this.selectedFormat,
            });
          }
        });
      } else if (this.selectedCustomFields.length) {
        this.customFieldsFromTemplate.push(...this.selectedCustomFields); // Push all selected fields
        this.closeCustomFieldMenu();
      }
    },
    addCustomFieldGeneralInfo(selectedField){
      if(selectedField && Array.isArray(selectedField)) {
        selectedField?.forEach((field) => {
          if (!this.isCustomFieldGeneralInfoSelected(field)) {
            this.customFieldsFromTemplate.push({
              dataType: field.type,
              id: field.uid || uuid.v4(),
              value: field.value || '',
              templateType: "generalInfo",
              options: field.options || [],
              name: field.name || '',
              entityType: this.selectedFormat,
            });
          }
        });
        
        this.closeCustomFieldMenu();
      }
    },
    addNewCustomFieldGeneralInfo()
    {
      this.showCreateUpdateCustomFieldDialog = true;
      this.selectedGroup = 'generalInfo';
    },

    addCustomFieldDetails(selectedField){
      if(selectedField && Array.isArray(selectedField)) {
        selectedField?.forEach((field) => {
          if (!this.isCustomFieldDetailsSelected(field)) {
            this.customFieldsFromTemplate.push({
              dataType: field.type,
              id: field.id || field.uid || uuid.v4(),
              value: field.value || '',
              templateType: "details",
              options: field.options || [],
              name: field.name || '',
              entityType: this.selectedFormat,
            });
          }
        });
        this.closeCustomFieldMenu();
      }
    },

    addNewCustomFieldDetails()
    {
      this.showCreateUpdateCustomFieldDialog = true;
      this.selectedGroup = 'details';
    },

    addNewCustomField()
    {
      this.showCreateUpdateCustomFieldDialog = true;
    },

    async getCustomFields()
    {
      this.isLoading = true;
      try {
        const queryParams = new URLSearchParams();
        this.entityTypes.forEach(entityType => {
          queryParams.append('entityTypes', entityType);
        });
        const response = await this.customFieldService.getCustomFields(this.$route.params.handle, this.$route.params.key, queryParams.toString());
        const result = response?.data || [];

        this.customFields = result.map((field) => ({
          ...field,
          id: String(field.uid),
        }));

      } catch (err) {
        this.redirectOnError(err.response?.status);
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('customFields') }, err?.response?.data);
      } finally {
        this.isLoading = false;
      }
    },
    async saveAsTemplate()
    {
      if (!this.caseName) {
        showErrorToast(this.$swal, this.$t('templateNameRequired'));
        return;
      }

      try {
        const templatePayload = {
          name: this.caseName,
          uid: uuid.v4(),
          templateFields: this.filteredCustomFieldsFromTemplate.map((field) => ({
            name: field.name,
            dataType: field.type || field.dataType,
            options: field.options || [],
            defaultValue: field.value || '',
            id: uuid.v4(),
          })),
          entityType: this.selectedFormat,
          rules: this.resultRules,
        };

        const response = await templateService.createTemplate(
          this.$route.params.handle,
          this.$route.params.key,
          templatePayload
        );

        if (response.status === 200) {
          showSuccessToast(this.$swal, 'createSuccess', { item: this.$t('template') });
          // Refresh templates list
          await this.init();
        }
      } catch (error) {
        showErrorToast(this.$swal, 'createError', { item: this.$t('template') }, error?.response?.data);
      }
    },
    isCustomFieldSelected(field)
    {
      return this.customFieldsFromTemplate.some((f) => {
        const normalize = (str) => String(str).toLowerCase().trim();
        return normalize(f.id) === normalize(field.id) || normalize(f.name) === normalize(field.name);
      });
    },
    isCustomFieldGeneralInfoSelected(field)
    {
      return this.customFieldsFromTemplate.some((f) => String(f.id) === String(field.id) && f.templateType === 'generalInfo');
    },
    isCustomFieldDetailsSelected(field)
    {
      return this.customFieldsFromTemplate.some((f) => String(f.id) === String(field.id) && f.templateType === 'details');
    },
    removeCustomField(fieldToRemove)
    {
      this.customFieldsFromTemplate = this.customFieldsFromTemplate.filter((field) => field.id !== fieldToRemove.id);
      this.closePopupAssist()
    },
    changeTitleParent(newName, stepIndex)
    {
      if (this.steps[stepIndex]) {
        this.$set(this.steps[stepIndex], 'title', newName);
      }
    },
    changeChildTitle(newTitle, childIndex, parentStepIndex)
    {
      if (this.steps[parentStepIndex] && this.steps[parentStepIndex].children && this.steps[parentStepIndex].children[childIndex]) {
        this.$set(this.steps[parentStepIndex].children[childIndex], 'title', newTitle);
      }
    },
    handleFolderSelected(folder)
    {
      // Handle folder selection if needed for additional logic
      console.log('Selected folder:', folder);
    },
  },
};
</script>

<style scoped>
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

#create-case-form {
  gap: 8px;
}

.justify-center {
  justify-content: center;
}

.property {
  min-width: 35px !important;
  background-color: white !important;
}

.f-color-white {
  color: white !important;
}

.f-color-red {
  color: #f2284e !important;
}

.justify-end {
  justify-content: end;
}

.align-start {
  text-align: start;
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}



.step-container {
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
}

.position-relative {
  position: relative;
}

.trash-style {
  position: absolute !important;
  right: -35px;
  top: 13px;
  color: #f2284e !important;
}

.item-area {
  width: 100%;
  display: flex;
  align-items: center;
}

.item-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.v-align-start {
  align-items: flex-start !important;
}

.back-btn-container {
  display: flex;
  align-items: flex-start;
}

.back-btn a {
  text-decoration: none;
  color: #0c2ff3 !important;
  font-weight: 600;
}

.back-btn a i {
  color: #0c2ff3 !important;
}

.case-contents {
  display: block;
  max-width: 438px;
  width: 438px;
}

.bg-color-grey {
  background-color: #f9f9fb;
}

.round-8 {
  border-radius: 8px;
}

.w-60 {
  width: 60%;
}

.w-40 {
  width: 40%;
}

.v-text-field__slot textarea {
  padding: 12px;
}

.font-inter {
  font-family: Inter;
}

.v-list-item:hover {
  background-color: #f9fafb;
  cursor: pointer;
}

.v-menu__content {
  text-align: left !important;
}

.color-red {
  color: #f2284e;
}

.ai-comparing {
  width: 100%;
  bottom: 0px;
  position: fixed;
  height: 72px;
  background-color: #FFF;
  display: flex;
  justify-content: center;
  align-items: center;
  display: flex;
  gap: 12px;
  margin-top: 250px;
  right: 0px;
}

.ai-comparing .ai-comparing-actions {
  gap: 6px;
  height: 30px;
}

.ai-comparing .ai-comparing-actions button {
  text-transform: none !important;
  border: 6px;

}
</style>
