<template>
  <div class="card flex justify-between h-40 bg-gray align-center px-3 py-1 my-1 rounded">
    <div class="d-flex gap-2 justify-start">
      <div class="run-id">
        {{ externalId }}
      </div>
      <v-tooltip
        bottom
        left
        max-width="485px"
        :disabled="!isTruncated"
        content-class="tooltip-theme"
      >
        <template #activator="{ on, attrs }">
          <a
            :ref="'testrunName_case_'+runId"
            text
            class="run-title"
            v-bind="attrs"
            @click="$router.push({ name: 'TestRunCaseEdit', params: { id: runId } })"
            v-on="on"
            @mouseover="checkTruncate(runId, 'testrunName_case')"
          >
            {{ runTitle }}
          </a>
        </template>
        <span>{{ runTitle }}</span>
      </v-tooltip>
    </div>
    <div
      v-if="runStatus"
      class="run-status fs-14px font-weight-medium"
      :style="{ color: getStatusColor(statusId, statuses) }"
    >
      {{ runStatus }}
    </div>
  </div>
</template>
<script>
import colorPreferencesMixin from '@/mixins/colorPreferences';

export default {
  name: 'TestRunItem',
  mixins: [colorPreferencesMixin],
  props: {
    runId: [String, Number],
    runTitle: String,
    runStatus: [String, Number],
    externalId: String,
    statusId: [String, Number],
  },
  data() {
    return {
      isTruncated: false,
      statuses: [],
    };
  },
  created(){
    this.statuses = this.getStatuses('testCase');
  },
  methods: {
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        console.log(el?.scrollWidth , el?.clientWidth, `${columnName}_${uid}`);
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>
<style scoped>
.h-40 {
  height: 40px;
}
.bg-gray {
  background-color: #f9fafb;
}
.align-center {
  align-items: center;
}
.run-id {
  /* width: 70px; */
  text-align: left;
  color: #667085;
  font-size: 14px;
  font-weight: 400;
}

.run-title {
  color: #0c111d !important;
  font-size: 14px;
  font-weight: 400;
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
}

@media (max-width: 1600px) {
  .run-title {
    max-width: 200px; 
  }
}
</style>
