<template>
  <MilestoneTestView 
    v-model="selectedMilestone"
    hide-action-btn
    :write-entity="_writeEntity"
    @back="$router.replace({ name: 'Milestones' })"
  />
</template>
<script>
import MilestoneTestView from '@/views/Tests/Milestones/MilestoneTestActivitiesView'
import makeMilestonesService from '@/services/api/milestone'
import handleLoading from '@/mixins/loader.js'
import { showErrorToast } from '@/utils/toast';

let makeMilestoneService;

export default{
  components:{
    MilestoneTestView,
  },
  mixins:[handleLoading],
  data(){
    return{
      selectedMilestone: {},
    }
  },
  computed:{
    _writeEntity(){
      return this.authorityTo('write_entity')
    }
  },
  created(){
    makeMilestoneService = makeMilestonesService(this.$api);
  },
  async mounted(){
    await this.init([this.findMilestone()]);
  },
  methods:{
    async findMilestone() {
      try {
        const handle = this.$route.params.handle;
        const response = await makeMilestoneService.findMilestone(handle, this.$route.params.key, this.$route.params.id);
        this.selectedMilestone = response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestone' }, err?.response?.data);
        console.error('Failed to fetch milestone:', err);
      }
    },
  },
}
</script>
