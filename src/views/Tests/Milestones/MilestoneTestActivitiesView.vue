<template>
  <div
    :class="{
      'pl-3': $route.name != 'MilestoneCreate',
    }"
  >
    <v-card
      class="py-4 px-6 my-3"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex flex-column">
        <div class="d-flex flex-row align-center justify-space-between">
          <div class="d-flex flex-column">
            <div class="d-flex align-center">
              <button
                v-if="!skeletonLoaderState"
                class="d-flex flex-row align-center pointer mr-3"
                @click="$emit('back')"
              >
                <v-icon color="black">
                  mdi-arrow-left
                </v-icon>
              </button>
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-3"
                height="24"
                width="24"
                type="button"
              />
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="value?.name?.length < 30"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <h2
                    class="custom__tooltip__title"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ value?.name }}
                  </h2>
                </template>
                <span>{{ value?.name }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-3"
                height="24"
                width="150"
                type="button"
              />
              <div
                v-if="!skeletonLoaderState"
                class="d-flex flex-row align-center justify-space-between ml-6"
              >
                <ProgressBar
                  :executions="generateExecutionsProgress(value?.customFields?.frequency)"
                  :percentage="value?.customFields?.progress"
                  :case-count="getObjectCount(value?.customFields?.frequency)"
                />
              </div>
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="24"
                width="100"
                type="button"
              />
            </div>
            <div v-if="value.description">
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="value.description?.length < 61"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <p
                    class="edit-plan mb-0"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ value.description }}
                  </p>
                </template>
                <span>{{ value.description }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else-if="skeletonLoaderState"
                class="mt-2"
                height="24"
                width="350"
                type="text"
              />
            </div>

            <!-- References Section in Header -->
            <div
              v-if="!skeletonLoaderState && value.references && value.references.length > 0"
              class="mt-3"
            >
              <h4 class="custom_field_heading mb-2">
                {{ $t('references') }}
              </h4>
              <div class="d-flex flex-wrap gap-2">
                <v-tooltip
                  v-for="(reference, index) in value.references"
                  :key="index"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                      style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                      v-on="on"
                      @click="window.open(reference.externalLink, '_blank')"
                    >
                      <div
                        class="d-flex align-center"
                        style="min-width: 0; flex: 1;"
                      >
                        <span
                          class="fs-12px text-theme-label mr-1 text-truncate"
                          style="min-width: 0; flex: 1; font-weight: 500;"
                        >{{ reference.name }}</span>
                      </div>
                      <a
                        :href="reference.externalLink"
                        target="_blank"
                        class="reference-link"
                        style="text-decoration: none; color: inherit;"
                        @click.stop
                      >
                        <v-icon
                          size="12"
                          class="text-theme-secondary"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </a>
                    </div>
                  </template>
                  <span>{{ reference.name }}</span>
                </v-tooltip>
              </div>
            </div>
          </div>
          <div>
            <template v-if="!skeletonLoaderState">
              <v-btn
                v-if="!isCreate"
                depressed
                color="primary"
                height="40px"
                class="text-capitalize btn-theme rounded-lg"
                @click="handleCloseClick"
              >
                {{ archiveButtonLabel }}
              </v-btn>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg primary"
              height="40"
              width="125"
              type="button"
            />
          </div>
        </div>
        <v-sheet
          class="white"
          color="#F2F4F7"
          rounded="lg"
          :outlined="true"
        >
          <div class="d-flex align-center justify-end">
            <template v-if="!skeletonLoaderState">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    icon
                    depressed
                    :ripple="false"
                    plain
                    v-bind="attrs"
                    v-on="on"
                    @click="toggleView('list')"
                  >
                    <ViewListSelectedIcon v-if="!listView" />
                    <ViewListIcon v-else />
                  </v-btn>
                </template>
                <span>{{ $t('List View') }}</span>
              </v-tooltip>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg"
              height="35"
              width="35"
              type="button"
            />
            <template v-if="!skeletonLoaderState">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    icon
                    depressed
                    :ripple="false"
                    plain
                    v-bind="attrs"
                    v-on="on"
                    @click="toggleView('dashboard')"
                  >
                    <ViewDashboardIcon v-if="!listView" />
                    <ViewDashboardSelectedIcon v-else />
                  </v-btn>
                </template>
                <span>{{ $t('Dashboard View') }}</span>
              </v-tooltip>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg"
              height="35"
              width="35"
              type="button"
            />
          </div>
        </v-sheet>
        <template v-if="isListView">
          <div
            v-if="!skeletonLoaderState"
            class="mt-5 d-flex"
          >
            <v-chip
              :class="{ 'blue--text': tableTestType === 'plans' }"
              width="200px"
              :color="tableTestType === 'plans' ? 'blue-light' : 'gray-light'"
              label
              @click="changeTestType('plans')"
            >
              <div
                class="px-2"
                :class="{ 'fw-semibold': tableTestType === 'plans', 'font-weight-medium': tableTestType !== 'plans' }"
              >
                {{ $t('testPlans') }} <span class="ml-1">{{ getActivePlanItemsCount }}</span>
              </div>
            </v-chip>
            <div class="ml-2">
              <v-chip
                :class="{ 'blue--text': tableTestType === 'runs' }"
                width="200px"
                :color="tableTestType === 'runs' ? 'blue-light' : 'gray-light'"
                label
                @click="changeTestType('runs')"
              >
                <div
                  class="px-2"
                  :class="{ 'fw-semibold': tableTestType === 'runs', 'font-weight-medium': tableTestType !== 'runs' }"
                >
                  {{ $t('testRuns') }} <span class="ml-1">{{ getActiveRunItemsCount }}</span>
                </div>
              </v-chip>
            </div>
          </div>
          <div
            v-else
            class="mt-5 d-flex"
          >
            <v-skeleton-loader
              class="rounded-sm d-flex gap-2 chip-primary"
              height="32"
              width="200"
              type="button@2"
            />
          </div>
        </template>
      </div>
    </v-card>

    <v-container
      v-if="isListView"
      class="d-flex flex-column align-self-stretch"
      fluid
    >
      <div class="row">
        <div
          v-if="!isTableTypePlans"
          class="pa-0"
          :style="{ width: isProjectMenuCollapsed ? '5%' : '15%' }"
        >
          <TestPlanMiniSection
            :is-collapsed.sync="isProjectMenuCollapsed"
            :selected-plan-id="selectedPlanId"
            @onPlanActiveId="onClickPlanActive"  
          />
        </div>
        <div
          class="pa-0"
          :style="{ width: !isTableTypePlans ? isProjectMenuCollapsed ? '95%' : '85%' : '100%' }"
        >
          <div class="container pa-6 white align-start card rounded-lg container--fluid app-height-global">
            <v-row class="align-center">
              <v-col
                cols="6"
                sm="6"
                class="py-0"
              >
                <div class="d-flex flex-row justify-start align-center">
                  <v-responsive
                    v-if="!skeletonLoaderState"
                    class="ma-0"
                    max-width="344"
                  >
                    <v-text-field
                      v-model="searchFilter"
                      class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                      :placeholder="$t('searchByName')"
                      height="40"
                      clear-icon="mdi-close-circle"
                      clearable
                      background-color="#F9F9FB"
                      hide-details
                    >
                      <template #prepend-inner>
                        <SearchIcon />
                      </template>
                    </v-text-field>
                  </v-responsive>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg mr-3"
                    width="344"
                    height="40"
                    type="button"
                  />

                  <PlanFilter
                    v-if="singularTestType == 'plan'"
                    :configurations="configurations"
                    :view-type="isTableTypePlans"
                    :milestones="activeMilestones"
                    :tags="tags"
                    @applyFilters="applyPlanFilters"
                  />
                  <RunFilter
                    v-else
                    :configurations="configurations"
                    :milestones="activeMilestones"
                    :tags="tags"
                    @applyFilters="applyRunFilters"
                  />
                </div>
              </v-col>
              <v-col
                cols="6"
                sm="6"
                class="d-flex justify-end"
              >
                <SettingsMenu
                  :key="singularTestType"
                  :view-type="isTableTypePlans"
                  :table-type="singularTestType"
                />
              </v-col>
            </v-row>
            <template>
              <MilestoneTestPlanTable
                v-if="tableTestType === 'plans'"
                v-model="value.testPlans"
                :value="value.testPlans"
                :plans-data="filteredTestPlans"
                :table-filter="tableFilter"
                :filtered-headers="filteredPlanHeaders"
                :filtered-items="filteredTestPlans"
                :total-items="planTotalRows"
                :current-page="planCurrentPage"
                :items-per-page="planPerPage"
                :relations-loading="relationsLoading"
                :relation-loading-states="relationLoadingStates"
                @row-click="onTestPlanRowClick"
                @input="handleTestPlans"
                @update-pagination="onUpdatePlanPagination"
              />

              <MilestoneTestRunTable
                v-if="tableTestType === 'runs'"
                :value="value.testRuns"
                :runs-data="filteredTestRuns"
                :table-filter="tableFilter"
                :filtered-headers="filteredRunHeaders"
                :filtered-items="filteredTestRuns"
                :total-items="runTotalRows"
                :current-page="runCurrentPage"
                :items-per-page="runPerPage"
                :relations-loading="relationsLoading"
                :relation-loading-states="relationLoadingStates"
                @row-click="onTestRunRowClick"
                @input="handleTestRuns"
                @update-pagination="onUpdateRunPagination"
              />
            </template>
            <v-row
              v-if="!hideActionBtn && !skeletonLoaderState"
              justify="end"
              class="mt-4"
            >
              <v-col cols="12">
                <v-flex class="mt-6 d-sm-flex justify-end">
                  <v-btn
                    :disabled="!writeEntity"
                    background-color="#F2F4F7"
                    height="40px"
                    depressed
                    :class="{ 'text-capitalize fw-semibold rounded-lg black--text mr-4 mt-2': true, 'disabled-action': isProjectArchived }"
                    :width="$vuetify.breakpoint.smAndDown ? '100%' : '150xp'"
                    @click="openCreateTest()"
                  >
                    <v-icon
                      class="mr-1"
                      size="16"
                    >
                      mdi-plus
                    </v-icon> {{ isTableTypePlans ? $t('Create new test plan') : $t('Create new test run') }}
                  </v-btn>
                  <slot name="actionButton" />
                </v-flex>
              </v-col>
            </v-row>
          </div>
        </div>
      </div>

      <MilestoneArchiveDialog
        v-model="showConfirmOpenDialog"
        :title="$t('milestone.close_dialog.title')"
        :content="$t('milestone.close_dialog.content_part1')"
        :content_part2="$t('milestone.close_dialog.content_part2')"
        :btn_label="activeMilestoneFilter == 'open' ? $t('milestone.close_dialog.btn_label') : $t('milestone.reopen_dialog.btn_label')"
        :milestone_name="value.name"
        color="primary"
        @close="handleCloseClick"
        @handleConfirmClick="handleMilestoneConfirmation()"
      />
    </v-container>
    <Dashboard
      v-if="isDashboardView"
      :style="{
        'margin-top': '10px',
      }"
      :is-ready="true"
      :show-archived="false"
    />
  </div>
</template>

<script>
import MilestoneTestRunTable from '@/components/Milestone/MilestoneTestRunTable.vue';
import MilestoneTestPlanTable from '@/components/Milestone/MilestoneTestPlanTable.vue';
import MilestoneArchiveDialog from '@/components/Milestone/MilestoneArchiveDialog.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import RunFilter from '@/components/TestRuns/RunFilter.vue';
import PlanFilter from '@/components/TestPlans/PlanFilter'
import {  mapGetters, mapActions } from 'vuex';
import SearchIcon from '@/assets/svg/search-icon.svg';
import handleLoading from '@/mixins/loader.js'
import makeRunService from '@/services/api/run'
import makePlanService from '@/services/api/plan'
import makeConfigurationService from '@/services/api/configuration'
import makeTagService from '@/services/api/tag';
import makeMilestonesService from '@/services/api/milestone'
import projectStatus from '@/mixins/projectStatus';
import ProgressBar from '@/components/base/ProgressBar.vue';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import TestPlanMiniSection from '@/components/base/TestPlanMiniSection.vue';
import MilestoneService from '@/services/api/milestone';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { useRelations } from '@/composables/utils/relations';
import ViewListSelectedIcon from '@/assets/svg/view-list-selected.svg';
import ViewDashboardIcon from '@/assets/svg/left-menu/dashboard.svg';
import Dashboard from "@/views/Dashboard";

let makeMilestoneService;

export default {
  name: 'MilestoneTestActivitiesView',
  components: {
    MilestoneTestRunTable,
    MilestoneTestPlanTable,
    SearchIcon,
    SettingsMenu,
    RunFilter,
    PlanFilter,
    ProgressBar,
    TestPlanMiniSection,
    MilestoneArchiveDialog,
    ViewDashboardIcon,
    ViewListSelectedIcon,
    Dashboard
  },
  mixins: [handleLoading, projectStatus, colorPreferencesMixin],
  props: {
    value: {
      type: Object,
    },
    hideActionBtn: {
      type: Boolean,
      default: false
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  emits: ['input'],
  setup() {
    const { relationsLoading, relationLoadingStates, fetchPlanRelations, fetchRunRelations } = useRelations();
    
    return {
      relationsLoading,
      relationLoadingStates,
      fetchPlanRelations,
      fetchRunRelations
    };
  },
  data()
  {
    return {
      isCreateDisabled: false,
      tableTestType: 'plans',
      tableFilter: 'all',
      searchFilter: '',
      appliedRunFilters: null,
      appliedPlanFilters: null,
      listView: false,
      runHeaders: [],
      planHeaders: [],
      milestones: [],
      configurations: [],
      runItems: [],
      planItems: [],
      tags: [],
      showConfirmBackDialog: true,
      isProjectMenuCollapsed: false,
      selectedPlanId: 'all',
      showConfirmOpenDialog: false,
      // Pagination state for plans
      planTotalRows: 0,
      planCurrentPage: 1,
      planPerPage: 10,
      // Pagination state for runs
      runTotalRows: 0,
      runCurrentPage: 1,
      runPerPage: 10,
      searchTimeout: null, // For debouncing search
    };
  },
  computed: {
    ...mapGetters(['activeMilestoneFilter']),
    ...mapGetters({
      currentAccount: 'user/currentAccount',
      dynamicHeaders: 'headers/dynamicHeaders'
    }),
    isListView(){
      return this.$route.query.view === 'list'
    },
    isDashboardView(){
      return this.$route.query.view === 'dashboard';
    },
    getActivePlanItems()
    {
      return this.planItems?.filter((plan) => !plan?.archivedAt);
    },
    getActiveRunItems()
    {
      return this.runItems?.filter((run) => !run?.archivedAt);
    },
    archiveButtonLabel() {
      return this.activeMilestoneFilter == 'open' ? this.$t('archive') : this.$t('unarchive');
    },
    getActivePlanItemsCount()
    {
      return this.planTotalRows; // Use server count instead of client-side length
    },
    getActiveRunItemsCount()
    {
      return this.runTotalRows; // Use server count instead of client-side length
    },
    getMilestoneProgress()
    {
      return this.value.progress;
    },
    singularTestType()
    {
      return this.tableTestType.substring(0, this.tableTestType.length - 1)
    },
    selectedTestType()
    {
      const testType = {
        plans: this.$t('testPlans'),
        runs: this.$t('testRuns')
      }
      return testType[this.tableTestType];
    },
    selectedRunLength()
    {
      return this.value.testRuns.length;
    },
    selectedPlanLength()
    {
      return this.value.testPlans.length;
    },
    filteredRunHeaders()
    {
      return this.runHeaders?.filter((header) => header.checked && header.value !== 'actions');
    },
    filteredPlanHeaders()
    {
      return this.planHeaders?.filter((header) => header.checked);
    },
    isTableTypePlans()
    {
      return this.tableTestType === 'plans';
    },
    filteredTestRuns()
    {
      // For server-side pagination, pass raw data to table component
      // Client-side filtering should only happen for search and additional filters
      // not for milestone filtering (which is done on server)
      let items = this.getActiveRunItems; // Use raw server data
      
      // Apply search filter only (milestone filtering is server-side)
      if (this.searchFilter) {
        items = items.filter(item => this.matchesFilter(item));
      }
      
      // Apply additional filters if any
      if (this.appliedRunFilters) {
        items = items.filter(item => this.matchRunApplyFilter(item));
      }
      
      // Handle plan filtering for runs (this is client-side)
      let filtered = items?.filter((item) =>
      {
        if (this.selectedPlanId == 'unlinked' && item.testPlans && item.testPlans.length > 0) {
          return false;
        }
        if (Number.isInteger(this.selectedPlanId)) {
          const hasMatchingPlan = item.testPlans?.some(plan => plan.uid === this.selectedPlanId);
          if (!hasMatchingPlan) {
             return false;
          }
        }
        return true;
      });
      
      return filtered;
    },

    filteredTestPlans()
    {
      // For server-side pagination, pass raw data to table component
      // Client-side filtering should only happen for search and additional filters
      let items = this.getActivePlanItems; // Use raw server data
      
      // Apply search filter only (milestone filtering is server-side)
      if (this.searchFilter) {
        items = items.filter(item => this.matchesFilter(item));
      }
      
      // Apply additional filters if any
      if (this.appliedPlanFilters) {
        items = items.filter(item => this.matchPlanApplyFilter(item));
      }
      
      return items;
    },
    testCasesCount(){
      return this.runItems.reduce((total, item) => total + (item.testcases || 0), 0)
    },
    activeMilestones()
    {
      return this.milestones?.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
  },
  watch: {
    // Watch for changes in the milestone UID only, not the entire value object
    'value.uid': {
      handler(newUid, oldUid) {
        // Only fetch data when milestone UID changes or when creating
        if ((newUid && newUid !== oldUid) || this.isCreate) {
          this.fetchData();
        }
      },
      immediate: true // Call handler immediately when component is created
    },
    // Watch search filter changes to trigger data refresh
    searchFilter: {
      handler() {
        // Reset to first page when search changes
        if (this.tableTestType === 'runs') {
          this.runCurrentPage = 1;
        } else {
          this.planCurrentPage = 1;
        }
        // Debounce search to avoid too many API calls
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
          this.fetchData();
        }, 300);
      }
    }
  },
  async created()
  {
    if (!this.dynamicHeaders.run) {
      this.initializeHeaders({ type: 'run' });
    }
    if (!this.dynamicHeaders.plan) {
      this.initializeHeaders({ type: 'plan' });
    }

    const validViews = ['list', 'dashboard'];
    const view = validViews.includes(this.$route.query.view) ? this.$route.query.view : 'list';
    await this.toggleView(view)

    makeMilestoneService = MilestoneService(this.$api);

    this.runHeaders = this.dynamicHeaders.run;
    this.planHeaders = this.dynamicHeaders.plan;

    this.getMilestones();
    this.getConfigurations();
    this.getTags();
  },
  methods: {
    ...mapActions({
      initializeHeaders: 'headers/initializeHeaders'
    }),
    
    // New method to fetch both runs and plans data
    async fetchData() {
      if (!this.isCreate && (!this.value || !this.value.uid)) {
        console.warn('Milestone data not available yet');
        return;
      }

      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      try {
        this.showSkeletonLoader();
        
        // Load core data first
        await Promise.all([
          this.getAllTestRuns({ handle, projectKey, perPage: this.runPerPage, currentPage: this.runCurrentPage }),
          this.getPlans({ handle, projectKey, perPage: this.planPerPage, currentPage: this.planCurrentPage })
        ]);
        
        // Hide skeleton loader as soon as core data is loaded
        this.hideSkeletonLoader();
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestone data' }, error?.response?.data);
        this.hideSkeletonLoader();
      }
    },

    handleCloseClick()
    {
      this.showConfirmOpenDialog = !this.showConfirmOpenDialog;
    },
    async handleMilestoneConfirmation()
    {
      try {
        await makeMilestoneService.updateMilestone(
          this.$route.params.handle,
          this.$route.params.key,
          this.value.uid,
          {
            ...this.value.customFields,
            archived: this.activeMilestoneFilter == 'open',
            name: this.value.name,
          }
        ); 
        showSuccessToast(this.$swal, 'closeSuccess', { item: 'Milestone' });
        this.$router.replace({
          name: 'Milestones',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key,
          },
          query: {
            activeClosed: 'true'
          }
        });
      } catch (error) {
        showErrorToast(this.$swal, 'closeError', { item: 'Milestone' }, error?.response?.data);
      }
    },
    async getAllTestRuns({ handle, projectKey, perPage, currentPage })
    {
      // Add safety check for milestone data
      if (!this.isCreate && (!this.value || !this.value.uid)) {
        console.warn('Cannot fetch runs: milestone data not available');
        return;
      }

      const runService = makeRunService(this.$api);
      
      const queryParams = {
        limit: perPage || 10, 
        offset: ((currentPage || 1) - 1) * (perPage || 10)
      };
      
      // Only add milestoneUid if not creating
      if (!this.isCreate && this.value && this.value.uid) {
        queryParams.milestoneUids = this.value.uid;
      }
      
      const response = await runService.getRuns(handle, projectKey, queryParams);
      this.runItems = response.data.items;
      this.runTotalRows = response.data?.count || response.data?.total || 0;
      
      // Fetch relations for the runs in background
      if (this.runItems.length > 0) {
        this.fetchRunRelations(runService, handle, projectKey, this.runItems).catch(error => {
          console.warn('Failed to load run relations:', error);
        });
      }
    },
    async getPlans({ handle, projectKey, perPage, currentPage })
    {
      // Add safety check for milestone data
      if (!this.isCreate && (!this.value || !this.value.uid)) {
        console.warn('Cannot fetch plans: milestone data not available');
        return;
      }

      const planService = makePlanService(this.$api);
      
      const queryParams = {
        limit: perPage || 10,
        offset: ((currentPage || 1) - 1) * (perPage || 10)
      };
      // Only add milestoneUid if not creating
      if (!this.isCreate && this.value && this.value.uid) {
        queryParams.milestoneUids = this.value.uid;
      }
      
      const response = await planService.getPlans(handle, projectKey, queryParams);
      this.planItems = response.data.items;
      this.planTotalRows = response.data?.count || response.data?.total || 0;
      
      // Fetch relations for the plans in background
      if (this.planItems.length > 0) {
        this.fetchPlanRelations(planService, handle, projectKey, this.planItems).catch(error => {
          console.warn('Failed to load plan relations:', error);
        });
      }
    },
    applyRunFilters(filters)
    {
      this.appliedRunFilters = filters ?? null;
      // Reset to first page when filters are applied
      this.runCurrentPage = 1;
      // Refresh data with new filters - this ensures server-side pagination works correctly
      if (this.tableTestType === 'runs') {
        this.getAllTestRuns({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.runPerPage, 
          currentPage: this.runCurrentPage 
        });
      }
    },
    applyPlanFilters(filters)
    {
      this.appliedPlanFilters = filters ?? null;
      // Reset to first page when filters are applied
      this.planCurrentPage = 1;
      // Refresh data with new filters - this ensures server-side pagination works correctly
      if (this.tableTestType === 'plans') {
        this.getPlans({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.planPerPage, 
          currentPage: this.planCurrentPage 
        });
      }
    },
    async toggleView(view = 'list'){
      if (this.$route.query.view === view) {
        return;
      }

      // Determine the route name based on whether we're creating or viewing
      const routeName = this.isCreate ? 'MilestoneCreate' : 'MilestoneView';
      
      const routeConfig = {
        name: routeName,
        params: {
          ...this.$route.params,
        },
        query: {
          view: view
        },
      };

      // Only add the id parameter if not creating and milestone exists
      if (!this.isCreate && this.value && this.value.uid) {
        routeConfig.params.id = this.value.uid;
      }

      await this.$router.push(routeConfig);
    },
    matchRunApplyFilter(item)
    {
      if (this.appliedRunFilters?.panel_priority?.length > 0 &&
        !this.appliedRunFilters.panel_priority.includes(item.priority)) {
        return false;
      }
      if (this.appliedRunFilters?.panel_status?.length > 0 &&
        !this.appliedRunFilters.panel_status.includes(item.status)) {
        return false;
      }
      if (this.appliedRunFilters?.panel_milestone?.length > 0 &&
        !this.appliedRunFilters.panel_milestone.includes(item.customFields?.milestone) &&
        !(this.appliedRunFilters.panel_milestone.includes('None') && !item.customFields?.milestone)) {
        return false;
      }
      if (this.appliedRunFilters?.panel_tag?.length > 0) {
        let tagExists = false;
        const itemTags = item.customFields?.tags || [];
        if (this.appliedRunFilters.panel_tag.includes('none') && itemTags.length === 0) {
            tagExists = true;
        } else {
            for (const tag of this.appliedRunFilters.panel_tag) {
                if (itemTags.includes(tag)) {
                    tagExists = true;
                    break;
                }
            }
        }
        if (!tagExists) return false;
      }
      if (this.appliedRunFilters?.dateRange?.start && this.appliedRunFilters?.dateRange.start > item.createdAt ||
        this.appliedRunFilters?.dateRange?.end && this.appliedRunFilters?.dateRange.end < item.createdAt) {
        return false;
      }
      return true;
    },
    matchPlanApplyFilter(item)
    {
      if (this.appliedPlanFilters?.panel_priority?.length > 0 &&
        !this.appliedPlanFilters.panel_priority.includes(item.priority)) {
        return false;
      }
      if (this.appliedPlanFilters?.panel_status?.length > 0 &&
        !this.appliedPlanFilters.panel_status.includes(item.status)) {
        return false;
      }
      if (this.appliedPlanFilters?.panel_tag?.length > 0) {
        let tagExists = false;
        const itemTags = item.customFields?.tags || [];
        if (this.appliedPlanFilters.panel_tag.includes('none') && itemTags.length === 0) {
            tagExists = true;
        } else {
            for (const tag of this.appliedPlanFilters.panel_tag) {
              if (itemTags.includes(tag)) {
                tagExists = true;
                break;
              }
            }
        }
        if (!tagExists) return false;
      }
      if (this.appliedPlanFilters?.dateRange?.start && this.appliedPlanFilters?.dateRange.start > item.createdAt ||
        this.appliedPlanFilters?.dateRange?.end && this.appliedPlanFilters?.dateRange.end < item.createdAt) {
        return false;
      }
      return true;
    },
    onClickPlanActive(planId)
    {
      this.selectedPlanId = planId
    },
    handleTestPlans(value)
    {
      // Only emit if the selection actually changed
      if (JSON.stringify(this.value.testPlans) !== JSON.stringify(value)) {
        this.value.testPlans = value;
        this.$emit('input', this.value)
      }
    },
    handleTestRuns(value)
    {
      // Only emit if the selection actually changed
      if (JSON.stringify(this.value.testRuns) !== JSON.stringify(value)) {
        this.value.testRuns = value;
        this.$emit('input', this.value)
      }
    },
    async getTags()
    {
      const handle = this.$route.params.handle;
      const tagService = makeTagService(this.$api);
      try {
        const response = await tagService.getTags(handle, 'runs');
        this.tags = response.data
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
        return [];
      }
    },
    async getMilestones()
    {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const milestoneService = makeMilestonesService(this.$api);
      try {
        const response = await milestoneService.getMilestones(handle, projectKey);
        this.milestones = response.data?.items;
        return response.data?.items;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
        return [];
      }
    },
    async getConfigurations()
    {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
        this.configurations = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    openCreateTest()
    {
      if (this.isTableTypePlans) {
        this.$router.push({
          name: 'TestPlanCreate',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key
          },
          query: {
            redirectTo: 'MilestoneCreate',
          }
        });

      } else {
        this.$router.push({
          name: 'TestRunCreate',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key
          },
          query: {
            redirectTo: 'MilestoneCreate',
          }
        });

      }
    },
    handleConfirmClick()
    {
      this.showConfirmBackDialog = false;
      this.$router.replace({
        name: 'MilestoneCreate',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key
        },
        query: {
          activeEdit: 'true'
        }
      });
    },
    changeFilter(filter)
    {
      this.tableFilter = filter;
    },
    changeTestType(type)
    {
      this.tableTestType = type;
    },
    matchesFilter(item)
    {
      const lowerCaseFilter = this.searchFilter.toLowerCase();

      const nameMatch = item.name.toLowerCase().includes(lowerCaseFilter);

      return nameMatch;
    },
    onUpdatePlanPagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.planCurrentPage || newItemsPerPage !== this.planPerPage) {
        this.planCurrentPage = newPage;
        this.planPerPage = newItemsPerPage;
        // Refresh plan data with new pagination
        this.getPlans({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.planPerPage, 
          currentPage: this.planCurrentPage 
        });
      }
    },
    onUpdateRunPagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.runCurrentPage || newItemsPerPage !== this.runPerPage) {
        this.runCurrentPage = newPage;
        this.runPerPage = newItemsPerPage;
        // Refresh run data with new pagination
        this.getAllTestRuns({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.runPerPage, 
          currentPage: this.runCurrentPage 
        });
      }
    },
    onTestPlanRowClick(plan){
      this.$router.push({
        name: 'TestPlanRerun',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          planId: plan.uid,
        },
      });
    },
    onTestRunRowClick(run){
      this.$router.push({
        name: 'TestRunCaseEdit',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          id: run.uid,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.search_input {
  width: 100%;

  @media screen and (min-width: 600px) {
    width: 300px;
  }
}

.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}

.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}

.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>
