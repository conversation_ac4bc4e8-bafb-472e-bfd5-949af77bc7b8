<template>
  <div
    id="case-index-container"
    fluid
    class="pl-3 pt-3"
  >
    <StatusesDialog
      v-model="statusDialog"
      @completed="completeTestRun"
    />
    <v-card
      class="py-6 px-6"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex flex-row justify-space-between">
        <div class="d-flex flex-column">
          <div
            class="d-flex flex-row align-center pointer"
          >
            <v-icon
              v-if="!skeletonLoaderState"
              color="black"
              @click="handleBackClick"
            >
              mdi-arrow-left
            </v-icon>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="24"
              width="24"
              type="button"
            />
            <h2
              v-if="!skeletonLoaderState"
              class="d-flex-inline justify-center align-center ml-2 ma-0 font-weight-bold"
            >
              {{ name }} 
            </h2>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="24"
              width="150"
              type="button"
            />
            <template v-if="!skeletonLoaderState">
              <div
                v-if="executionsCount"
                class="d-flex flex-row align-center justify-space-between ml-6"
              >
                <ProgressBar
                  :executions="generateExecutionsProgress(executionsProgress)"
                  :percentage="executionsPercentage"
                  :case-count="getObjectCount(executionsProgress)"
                />
              </div>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg"
              height="24"
              width="100"
              type="button"
            />
          </div>
          <p
            v-if="!skeletonLoaderState"
            class="text-start display-10 my-2"
          >
            {{ description }}
          </p>
          
          <!-- References Section in Header -->
          <div
            v-if="!skeletonLoaderState && runData?.references && runData.references.length > 0"
            class="mt-3"
          >
            <h4 class="custom_field_heading mb-2">
              {{ $t('references') }}
            </h4>
            <div class="d-flex flex-wrap gap-2">
              <v-tooltip
                v-for="(reference, index) in runData.references"
                :key="index"
                bottom
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                    style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                    v-on="on"
                    @click="window.open(reference.externalLink, '_blank')"
                  >
                    <div
                      class="d-flex align-center"
                      style="min-width: 0; flex: 1;"
                    >
                      <span
                        class="fs-12px text-theme-label mr-1 text-truncate"
                        style="min-width: 0; flex: 1; font-weight: 500;"
                      >{{ reference.name }}</span>
                    </div>
                    <a
                      :href="reference.externalLink"
                      target="_blank"
                      class="reference-link"
                      style="text-decoration: none; color: inherit;"
                      @click.stop
                    >
                      <v-icon
                        size="12"
                        class="text-theme-secondary"
                      >
                        mdi-arrow-top-right
                      </v-icon>
                    </a>
                  </div>
                </template>
                <span>{{ reference.name }}</span>
              </v-tooltip>
            </div>
          </div>
          
          <v-skeleton-loader
            v-else-if="skeletonLoaderState"
            class="rounded-lg mt-2 mr-3"
            height="24"
            width="150"
            type="button"
          />
        </div>
        <div class="d-flex flex-column">
          <div class="buttons d-flex flex-row">
            <template v-if="!skeletonLoaderState">
              <v-btn
                v-if="_writeEntity"
                color="#F2F4F7"
                class="text-capitalize btn-theme rounded-lg mr-3"
                height="40px"
                :width="$vuetify.breakpoint.smAndDown ? '100%' : '92px'"
                :class="{ 'disabled-action': isProjectArchived }"
                depressed
                @click="editTestRun"
              >
                {{ $t('edit') }}
              </v-btn>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="40"
              width="125"
              type="button"
            />
            <template v-if="!skeletonLoaderState">
              <v-btn
                v-if="_writeEntity"
                color="#F2F4F7"
                class="text-capitalize btn-theme rounded-lg mr-3"
                height="40px"
                :width="$vuetify.breakpoint.smAndDown ? '100%' : '92px'"
                :loading="isRerunLoading"
                :class="{ 'disabled-action': isRerunLoading || isProjectArchived }"
                depressed
                @click="duplicateTestRun"
              >
                {{ $t('testruns.rerun') }}
              </v-btn>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="40"
              width="125"
              type="button"
            />
            <template v-if="!skeletonLoaderState">
              <v-btn
                v-if="_writeEntity"
                depressed
                color="primary"  
                height="40px"
                class="text-capitalize btn-theme rounded-lg"
                :width="$vuetify.breakpoint.smAndDown ? '100%' : '118px'"
                :loading="isCompleteRunLoading"
                :class="{ 'disabled-action': isCompleteRunLoading || isProjectArchived }"
                @click="statusDialog = true"
              >
                {{ $t('testruns.complete') }}
              </v-btn>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg primary"
              height="40"
              width="125"
              type="button"
            />
          </div>
          <v-sheet
            class="white"
            color="#F2F4F7"
            rounded="lg"
            :outlined="true"
          >
            <div class="d-flex align-center justify-end">
              <template v-if="!skeletonLoaderState">
                <v-tooltip bottom>
                  <template #activator="{ on, attrs }">
                    <v-btn
                      icon
                      depressed
                      :ripple="false"
                      plain
                      v-bind="attrs"
                      v-on="on"
                      @click="toggleView('list')"
                    >
                      <ViewListSelectedIcon v-if="!listView" />
                      <ViewListIcon v-else />
                    </v-btn>
                  </template>
                  <span>{{ $t('List View') }}</span>
                </v-tooltip>
              </template>
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="35"
                width="35"
                type="button"
              />
              <template v-if="!skeletonLoaderState">
                <v-tooltip bottom>
                  <template #activator="{ on, attrs }">
                    <v-btn
                      icon
                      depressed
                      :ripple="false"
                      plain
                      v-bind="attrs"
                      v-on="on"
                      @click="toggleView('dashboard')"
                    >
                      <ViewDashboardIcon v-if="!listView" />
                      <ViewDashboardSelectedIcon v-else />
                    </v-btn>
                  </template>
                  <span>{{ $t('Dashboard View') }}</span>
                </v-tooltip>
              </template>
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="35"
                width="35"
                type="button"
              />
            </div>
          </v-sheet>
        </div>
      </div>
    </v-card>
    
    <template v-if="hasNoExecutions">
      <div class="mt-3 mb-0 white rounded-lg mx-0 app-height-global d-flex justify-center align-center">
        <ActiveEmptyState
          :image-src="require('@/assets/png/empty-step.png')"
          image-max-width="323px"
          :title="$t('noTestCasesFound')"
          :description="$t('noTestCasesFoundDescription')"
        />
      </div>
    </template>

    <template v-else>
      <ExecutionManagement
        v-if="isListView"
        :show-create="false"
        :execution="selectedExecution"
        :executions="testRunExecutions"
        :assignees="assignees"
        :test-results="testResults"
        :execution-defects="executionDefects"
        :show-folders="showFolders"
        :folders="folders"
        :write-activity="_writeActivity"
        :delete-activity="_deleteActivity"
        :write-entity="_writeEntity"
        :total-items="totalRows"
        :current-page="currentPage"
        :items-per-page="perPage"
        :executions-loading="executionsLoading"
        @bulkRemove="onBulkRemove"
        @getExecution="getExecution"
        @folder-select="getFolder"
        @updateExecution="updateTestExecution"
        @updateExecutions="updateTestExecutions"
        @updateSelectedExecution="updateSelectedExecution"
        @updateResult="updateTestResult"
        @deleteResult="deleteTestResult"
        @deleteExecution="deleteExecution"
        @addResult="addTestResult"
        @moveItem="moveSelectedItem"
        @uploadAttachments="uploadAttachments"
        @reloadExecution="reloadExecution"
        @reloadExecutionResults="reloadExecutionResults"
        @updateStepStatus="updateStepStatus"
        @updateAllStepStatus="updateAllStepStatus"
        @openDefectDialog="handleOpenDefectDialog"
        @refetchExecution="handleRefetchExecution"
        @update-pagination="onUpdatePagination"
      />
      <Dashboard
        v-if="isDashboardView"
        :style="{
          'margin-top': '10px',
        }"
        :is-ready="true"
        :show-archived="false"
      />
    </template>
    <AddNewDefectDialog
      v-model="showAddDefectDialog"
      :title="$t('defect.addNewDefectDialog.title')"
      :content="$t('defect.addNewDefectDialog.content')"
      :color="'primary'"
      :write-defect="_writeDefect"
      @close="showAddDefectDialog = false"
      @handle-actions-type="handleDefectActionType"
    />
    <CreateDefect
      v-if="showCreateDefectDialog"
      :create-defect-dialog="showCreateDefectDialog"
      :execution="failedExecution"
      :result-uid="failedResultUid"
      :action-selected="selectedDefectAction"
      @closeDialog="showCreateDefectDialog = false"
      @defectCreated="handleDefectCreated"
      @defectLinked="handleDefectLinked"
    />
  </div>
</template>

<script>

import ExecutionManagement from '@/components/Execution/ExecutionManagement'
import { mapActions, mapGetters } from 'vuex';
import makeRunService from '@/services/api/run';
import makeExecutionService from '@/services/api/execution'
import makeResultService from '@/services/api/result'
import makeDefectService from '@/services/api/defect'
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { useFolders } from '@/composables/modules/cases/folders';
import handleLoading from '@/mixins/loader.js'
import projectStatus from '@/mixins/projectStatus';
import ProgressBar from '@/components/base/ProgressBar.vue';
import StatusesDialog from '@/components/TestRuns/StatusesDialog'
import CreateDefect from '@/components/Defect/CreateDefect'
import AddNewDefectDialog from '@/components/Defect/AddNewDefectDialog'
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ViewListSelectedIcon from '@/assets/svg/view-list-selected.svg';
import ViewDashboardIcon from '@/assets/svg/left-menu/dashboard.svg';
import Dashboard from "@/views/Dashboard";
import workspaceService from '@/services/api/workspace';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';

export default {
  components:{
    ExecutionManagement,
    ProgressBar,
    StatusesDialog,
    CreateDefect,
    ViewListSelectedIcon,
    ViewDashboardIcon,
    AddNewDefectDialog,
    Dashboard,
    ActiveEmptyState
  },
  mixins: [handleLoading, projectStatus, colorPreferencesMixin],
  props: {},
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      actionMenu: false,
      selectedCases: [],
      testRunExecutions: [],
      executionsPercentage: 0,
      executionsProgress: {},
      folderId: null,
      selectedExecution: null,
      selectedExecutionTag: null,
      assignees: [],
      testResults: [],
      folders: [],
      listView: false,
      isRerunLoading: false,
      isCompleteRunLoading: false,
      showFolders: true,
      statusDialog: false,
      showDefectDialog: false,
      failedExecution: null,
      failedResultUid: null,
      showAddDefectDialog: false,
      showCreateDefectDialog: false,
      selectedDefectAction: null,
      statuses: [],
      name: null,
      description: null,
      // Pagination state
      totalRows: 0,
      currentPage: 1,
      perPage: 10,
      // Selected folder for filtering executions
      selectedFolderId: null,
      // Loading state for executions table
      executionsLoading: false,
      // Execution defects
      executionDefects: [],
      // Run data for displaying details
      runData: null,
    };
  },
  computed:{
    ...mapGetters({
      user: 'user/user'
    }),
    isListView(){
      return this.$route.query.view === 'list' || this.$route.name === 'TestRunCaseEdit' || this.$route.name === 'TestRunCaseEditExecutions'
    },
    isDashboardView(){
      return this.$route.query.view === 'dashboard';
    },
    executionsCount(){
      return this.testRunExecutions?.length
    },
    _readActivity(){
      return this.authorityTo('read_activity')
    },
    _writeActivity(){
      return this.authorityTo('write_activity')
    },
    _deleteActivity(){
      return this.authorityTo('delete_activity')
    },
    _writeEntity(){
      return this.authorityTo('write_entity')
    },
    _writeDefect() {
      return this.authorityTo('write_defect');
    },
    executionUidParams() {
      return this.$route.params.executionUid;
    },
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage);
    },
    hasNoExecutions() {
      return (!this.skeletonLoaderState && 
              (!this.folders || this.folders.length === 0) && 
              (!this.testRunExecutions || this.testRunExecutions.length === 0));
    },
  },

  watch:{
    selectedExecution(newVal){
      if (!newVal) return;
      this.selectedExecutionTag = newVal.tags?.map(tag => tag.uid) || [];
    }
  },
  async mounted(){
    if(this.executionUidParams){
      // If we have an execution parameter but haven't loaded folders yet
      if (this.folders.length === 0) {
        await this.getFolders(this.$route.params.handle, this.$route.params.key, this.$route.params.id);
      }
      
      // Set the correct folder from route parameters if available, otherwise use first folder
      if (!this.selectedFolderId) {
        if (this.$route.params.folderUid) {
          this.selectedFolderId = this.$route.params.folderUid;
        } else if (this.folders.length > 0) {
          this.selectedFolderId = this.folders[0].uid;
        }
      }
      
      if (!this.testRunExecutions.length) {
        await this.getTestRunExecutions(this.$route.params.handle, this.$route.params.key, this.$route.params.id);
      }
      await this.getExecution(this.executionUidParams);
    }
  },
  async created(){
    this.statuses = this.getStatuses("testCase").filter(element => !element.archived);
    const handle = this.$route.params.handle;
    const projectKey = this.$route.params.key;
    const id = this.$route.params.id;
    
    // First fetch folders and run details
    await this.init([
      this.getTestRunById(handle,projectKey,id),
      this.getFolders(handle, projectKey, id),
    ])
    
    // After folders are fetched, select the correct folder and fetch executions
    if (this.folders.length > 0) {
      // Use folder from route parameters if available, otherwise use first folder
      if (this.$route.params.folderUid) {
        this.selectedFolderId = this.$route.params.folderUid;
      } else {
        this.selectedFolderId = this.folders[0].uid;
      }
      await this.getTestRunExecutions(handle, projectKey, id);
    }
    
    const validViews = ['list', 'dashboard'];
    const view = validViews.includes(this.$route.query.view) ? this.$route.query.view : 'list';
    
    // Don't redirect if we're already on an execution route
    if (this.$route.name !== 'TestRunCaseEditExecutions') {
      await this.toggleView(view)
    }
  },
  methods: {
    ...mapActions({
        uploadToServer: 'attachment/uploadToServer',
      }
    ),
    async uploadAttachments(newFiles){
      const handle = this.$route.params.handle;
      const mediaType = 'attachment';
      const executionService = makeExecutionService(this.$api);
      const params = {
        handle,
        projectKey: this.$route.params.key,
        executionUid: this.selectedExecution.uid
      }

      const baseUrl = `${import.meta.env.VITE_APP_SERVER_BASEURL}/${handle}`
      if(newFiles?.length) {
        await Promise.all(newFiles.map(async (file) => {
          await this.uploadToServer( {handle, mediaType, file, apiService: executionService, params}).then(objectData => {
            const img = {
              previewUrl: `${baseUrl}/executions/attachments/${objectData.uid}/object`,
              type: objectData.type
            }
            const attachments = [...this.selectedExecution.attachments, img];
            this.selectedExecution = {
              ...this.selectedExecution,
              attachments
            }
          })
        })).then(() => {
          showSuccessToast(this.$swal, this.$t('success.uploadedAttachments'))
        }).catch((err) => {
          if (err?.status == 507)
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
          else 
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data)
        });
        this.reloadExecution(this.selectedExecution.uid);
      } else {
        showSuccessToast(this.$swal, this.$t('noAttachments'))
      }
    },
 
    handleOpenDefectDialog(execution) {
     this.failedExecution = execution;
     this.failedResultUid = null; // No specific result when manually opening defect dialog
     this.showAddDefectDialog = true;
    },
    handleBackClick() {
      this.$router.push({
        name: 'Runs',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key
        },
      });
    
    },
    async getFolders(handle, projectKey, id) {
      const { getFolders } = useFolders();
      try {
        const folderData = await getFolders(handle, projectKey, { runId: id, api: this.$api });
        this.folders = folderData;
      } catch (err) {
        showErrorToast(this.$swal, this.$t("test_folder.refresh_failed"), {}, err?.response?.data);
      }
    },
    updateProgress(){
      const completedStatuses = this.getEntityCompletedStatuses('testCase');
      let completedCount = 0;
      const executionsProgress = this.testRunExecutions.reduce((acc, curr) => {
        if(completedStatuses.includes(curr.status))
          completedCount++;
        acc[curr.status] ? acc[curr.status]++ : acc[curr.status] = 1; 
        return acc
      }, {})

      let progress = 0;
      if(this.testRunExecutions.length)
        progress = Math.round((completedCount / this.testRunExecutions.length) * 100);

      this.executionsProgress = executionsProgress;
      this.executionsPercentage = progress;
    },
    completeTestRun(status) {
      if(!this._writeEntity){
        this.unauthorizedToast;
        return ;
      }
      const runService = makeRunService(this.$api);

      const payload = {
        status,  
      }
      runService.updateTestRun(this.$route.params.handle, this.$route.params.key, this.$route.params.id, payload)
        .then(() => {
          this.$router.push({
            name: 'Runs',
            params: {
              handle: this.$route.params.handle,
              key: this.$route.params.key
            },
          });
        })
        .catch(error => {
          console.error("Failed to Update Test Run:", error);
        }).finally(() => {
          this.isCompleteRunLoading = false;
        });
    },
    async duplicateTestRun(){
      if(!this._writeEntity){
        this.unauthorizedToast;
        return ;
      }
      const runService = makeRunService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const runId = this.$route.params.id;
      const payload = { testRuns: [{uid: runId,}]};
      this.isRerunLoading = true;
      await runService.duplicateTestRun(handle, projectKey, payload ).then(() => {
        showSuccessToast(this.$swal, this.$t('testruns.duplicate_testrun.success'))
        this.$router.push({
            name: 'Runs',
            params: {
              handle: this.$route.params.handle,
              key: this.$route.params.key
            },
          });
      }).catch((err) => {
        showErrorToast(this.$swal, this.$t('testruns.duplicate_testrun.error'), {}, err?.response?.data)
      }).finally(()=> {
        this.isRerunLoading = false;
      })
    },
    async toggleView(view = 'list'){
      if (this.$route.query.view === view) {
        return;
      }

      // Preserve the current route name and parameters
      const targetRouteName = this.$route.name === 'TestRunCaseEditExecutions' ? 'TestRunCaseEditExecutions' : 'TestRunCase';
      
      await this.$router.push({
        name: targetRouteName,
        params: {
          ...this.$route.params,
        },
        query: {
          view: view
        },
      })   
    },
    editTestRun(){
      if(!this._writeEntity){
        this.unauthorizedToast;
        return ;
      }
      this.$router.push({
        name: 'TestRunEdit',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          id: this.$route.params.id,
          folderUid: this.$route.params.folderUid
        },
      });
    },
    async getTestRunExecutions(handle, projectKey, runId){
      try {
        
        if(!this._readActivity){
          this.unauthorizedToast;
          return ;
        }
        
        // Show loading state for executions table only
        this.executionsLoading = true;
        
        const runService = makeRunService(this.$api);
        const params = {
          limit: this.perPage,
          offset: (this.currentPage - 1) * this.perPage
        };
        
        // Add parentUid if a folder is selected
        if (this.selectedFolderId) {
          params.parentUid = this.selectedFolderId;
        }
        
        await runService.getTestRunExecutions(
          handle,
          projectKey,
          runId,
          params
        )
          .then(response =>
          {
          this.testRunExecutions = response.data.items || [];
          this.totalRows = response.data.count || response.data.total || 0;
        })
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchExecutions'), {}, error?.response?.data)
      } finally {
        // Hide loading state for executions table
        this.executionsLoading = false;
      }
    },
    async getTestRunById(handle, projectKey, runId){
      try {
        if(!this._readActivity){
          this.unauthorizedToast;
          return ;
        }
        await this.getWorkspaceUsers()
        const runService = makeRunService(this.$api);
        await runService.getTestRunById(handle, projectKey, runId).then(response => {
          const runData = response.data;
          this.name = runData.name;
          this.description = runData.description;
          this.executionsPercentage = runData.progress || runData.customFields?.progress || 0;
          this.executionsProgress = runData.frequency || runData.customFields?.frequency || {};
          this.runData = runData; // Store the complete run data for displaying details
        })
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchRun'), {}, error?.response?.data)
      }
    },
    async getWorkspaceUsers() {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        const response = await makeWorkspaceService.getWorkspaceUsers(handle);
        this.assignees = response.data?.users || [];

        const currentUser = {
            email: this.user.email,
            firstName: this.user.firstName,
            lastName: this.user.lastName,
            handle: this.user.handle,
            uid: this.user.uid
        }
        const currentUserExists = this.assignees.some(assignee => assignee.uid === currentUser.uid);
        if (!currentUserExists) {
            this.assignees.push(currentUser);
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('users') }, error?.response?.data);
      }
    },
    async getFolder(folderId){
      this.folderId = folderId;
      // Update selected folder and reload executions for this folder
      if (this.selectedFolderId !== folderId) {
        this.selectedFolderId = folderId;
        // Reset pagination to first page when switching folders
        this.currentPage = 1;
        await this.refreshExecutions();
      }
    },
    async getExecution(executionUid){
      if(!this._readActivity){
        this.unauthorizedToast;
        return ;
      }
      await this.getExecutionResult(executionUid);
      await this.reloadExecution(executionUid);

    },
    async performStepStatusUpdate(steps) {
      const handle = this.$route.params.handle
      const executionService = makeExecutionService(this.$api)
      const projectKey = this.$route.params.key;
      const executionId = this.selectedExecution.uid
      const payload = { steps }
      await executionService.updateExecution(handle, projectKey, executionId, payload);
      const response = await executionService.getExecution(handle, projectKey, executionId);
      if (response.status === 200) {
        this.selectedExecution = {
          ...this.selectedExecution,
          steps: response.data.steps
        }
        return response
      }
      throw new Error(`Unexpected status ${response.status}`)
    },

    async updateStepStatus(stepStatus) {
      const { steps } = this.selectedExecution
      let step, position

      if (stepStatus.parentStepUid) {
        const parent = steps.find(s => s.uid === stepStatus.parentStepUid)
        step = parent?.children?.find(c => c.uid === stepStatus.stepItemId)
        if (step && step.position === undefined) {
          position = parent.children.findIndex(c => c.uid === stepStatus.stepItemId)
        }
      } else {
        step = steps.find(s => s.uid === stepStatus.stepItemId)
        if (step && step.position === undefined) {
          position = steps.findIndex(s => s.uid === stepStatus.stepItemId)
        }
      }

      if (!step) return

      step.status = stepStatus.statusUid
      const stepsPayload = [{
        uid: step.uid,
        position: step.position ?? position,
        status: stepStatus.statusUid
      }]

      try {
        await this.performStepStatusUpdate(stepsPayload)
        showSuccessToast(this.$swal, this.$t('toast.stepStatusUpdated'))
      } catch (error) {
        showErrorToast(
          this.$swal,
          this.$t('toast.failedToUpdateStepStatus'),
          {},
          error?.response?.data
        )
      }
    },


    flattenSteps(steps = []) {
      return steps.reduce((acc, step) => {
        acc.push(step)
        if (Array.isArray(step.children) && step.children.length) {
          acc.push(...this.flattenSteps(step.children))
        }
        return acc
      }, [])
    },

    async updateAllStepStatus(data) {
      if (!Array.isArray(data.steps) || data.steps.length === 0) return

      const all = this.flattenSteps(data.steps)
      const stepsPayload = all.map(s => ({
        uid:      s.uid,
        position: s.position,
        status:   data.completedId
      }))

      try {
        await this.performStepStatusUpdate(stepsPayload)
        showSuccessToast(this.$swal, this.$t('toast.stepStatusUpdated'))
      } catch (error) {
        showErrorToast(
          this.$swal,
          this.$t('toast.failedToUpdateStepStatus'),
          {},
          error?.response?.data
        )
      }
    },
    async reloadExecution(executionUid){
      try {
        const executionService = makeExecutionService(this.$api);
        const response = await executionService.getExecution(this.$route.params.handle, this.$route.params.key, executionUid);
        const testRunExecution = this.testRunExecutions.find(element => element.uid == executionUid);
        this.selectedExecution = {
          ...testRunExecution,
          steps: response.data?.steps || [],
          attachments: response.data?.attachments,
          tags: response.data?.tags || [],
          references: response.data?.references || [],
        }
        this.selectedExecutionTag = response.data?.tags?.map(tag => tag.uid);
        
        // Also reload defects when reloading execution
        await this.getExecutionDefects(this.$route.params.key, executionUid);
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchExecution'), {}, error?.response?.data)
      }
    },
    updateSelectedExecution(selectedCases) {
      this.selectedCases = selectedCases;
    },
    async reloadExecutionResults(executionUid){
      await this.getExecutionResult(executionUid);
      await this.getExecutionDefects(this.$route.params.key, executionUid);
    },
    async getExecutionResult(executionUid){
      if(!this._readActivity){
        this.unauthorizedToast;
        return ;
      }
      const handle = this.$route.params.handle;
      const resultService = makeResultService(this.$api);
      const projectKey = this.$route.params.key; 

      this.testResults = await resultService.getTestResults(handle, projectKey, executionUid).then(async response => {
        return response.data   
      }) 
    },
    onBulkRemove(payload){
      console.log("Delete from Test Run", payload)
    },
    tagFomation(originalTags, newTags) {
      const tagIdsToRemove = originalTags.filter((tagId) => !newTags.includes(tagId));
      const tagIdsToAdd = newTags.filter((tagId) => !originalTags.includes(tagId));

      const tagReplacements = [];

      if (tagIdsToRemove.length > 0) {
        tagReplacements.push({
          existingTagUids: tagIdsToRemove,
          newTagUids: [],
        });
      }

      if (tagIdsToAdd.length > 0) {
        tagReplacements.push({
          existingTagUids: [],
          newTagUids: tagIdsToAdd,
        });
      }

      return {
        tagUids: tagIdsToAdd,
        tagReplacements,
      };
    },
    async getExecutionResults(projectKey, executionUid){
      const handle = this.$route.params.handle;
      const resultService = makeResultService(this.$api);
      this.testResults = await resultService.getTestResults(handle, projectKey, executionUid).then(async response => {
        return response.data   
      }) 
    },
    async getExecutionDefects(projectKey, executionUid) {
      if (!projectKey || !executionUid) return;
      
      try {
        const defectService = makeDefectService(this.$api);
        const handle = this.$route.params.handle;
        const response = await defectService.getExecutionDefects(handle, projectKey, executionUid);
        this.executionDefects = response.data || [];
      } catch (error) {
        console.warn('Failed to fetch execution defects:', error);
        this.executionDefects = [];
      }
    },
    async updateTestExecution(updatedData){

      if(!this._writeActivity) {
        this.unauthorizedToast;
        return;
      }
      
      const executionService = makeExecutionService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const selectedExecution = this.selectedExecution?.uid || updatedData?.selectedExecutionId;
      let payload = {};

  
      if (updatedData.property == 'tags') {
        const originalTags = this.selectedExecutionTag || [];
        const newTags = updatedData?.value?.map(tag => tag?.uid);
        const { tagUids, tagReplacements } = this.tagFomation(originalTags, newTags);
        payload = {
          tagUids,
          tagReplacements
        }
      }
      else if(typeof(updatedData.value) == 'object') {
        Object.assign(payload, {
          [updatedData.property]: updatedData?.value?.uid
        })
      } else {
        Object.assign(payload, {
          [updatedData.property]: updatedData?.value
        })
      }

      const previousAssignedTo = this.selectedExecution?.assignedTo;
      
      try {
        const response = await executionService.updateExecution(handle, projectKey, selectedExecution, payload);
        const execution = response.data;
        if (execution.uid) {
          const findIndex = this.testRunExecutions.findIndex(element => element.uid == execution.uid);
          if (this.selectedExecution?.uid) {
          this.selectedExecution = { ...this.selectedExecution, ...execution };
          this.testRunExecutions.splice(findIndex, 1, { ...this.selectedExecution, ...execution });
          if (updatedData.property === 'status') {
             this.$set(this.selectedExecution, 'status', execution.status);
          } else if (updatedData.property === 'priority') {
             this.$set(this.selectedExecution, 'priority', execution.priority);
          } else if (updatedData.property === 'assignedTo') {
             this.$set(this.selectedExecution, 'assignedTo', execution.assignedTo);
          } else if (updatedData.property === 'dueAt') {
             this.$set(this.selectedExecution, 'dueAt', execution.dueAt);
          } else if (updatedData.property === 'tags') {
             this.$set(this.selectedExecution, 'tags', this.selectedExecution.tags);
          } else {
             this.$set(this.selectedExecution, updatedData.property, execution[updatedData.property]);
        }
       
        // update in testRunExecutions array
        if (findIndex !== -1) {
          const updatedExecution = {...this.testRunExecutions[findIndex]};
          updatedExecution[updatedData.property] = execution[updatedData.property];
          this.$set(this.testRunExecutions, findIndex, updatedExecution);
         }
        }
        // Show add defect dialog if status is Failed
        if (updatedData.property === 'status') {
            const statusDetails = this.getStatusDetails(updatedData.value, this.statuses);
            if (statusDetails?.isFailure) {
              this.failedExecution = execution;
              this.showAddDefectDialog = true;
            }
            else {
              this.showAddDefectDialog = false;
              this.showCreateDefectDialog = false;
              this.failedExecution = null;
            }
          }

        }

        if (updatedData.property === 'assignedTo' && previousAssignedTo !== execution.assignedTo) {
          showSuccessToast(this.$swal, this.$t('success.testCaseAssigned'));
        } else if(updatedData.property == 'dueAt'){
          showSuccessToast(this.$swal, this.$t('success.executionDueAt'));
        } else if(updatedData.property == 'tags'){
          if(updatedData.isAddTags){
            showSuccessToast(this.$swal, this.$t('success.newTagApplied'));
          } else {
            showSuccessToast(this.$swal, this.$t('success.tagRemoved'));
          }
        } 
        else {
          showSuccessToast(this.$swal, this.$t('success.executionUpdated'));
        }
        this.updateProgress();
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.executionUpdateFailed'), {}, error?.response?.data)
      }
    },
    async updateTestExecutions(data){
      if(!this._writeActivity){
        this.unauthorizedToast;
        return ;
      }
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const executionService = makeExecutionService(this.$api);
      const payload = {
        executionUids: this.selectedCases.map(item => item.uid),
        ...data
      }
    
      await executionService.updateExecutions(handle, projectKey, payload).then((response) => {
        if(response.data.length)
          response.data.forEach(element => {
            const eIndex = this.testRunExecutions.findIndex(obj => obj.uid == element.uid);
            this.testRunExecutions.splice(eIndex, 1, {...this.testRunExecutions[eIndex], ...element})
          })  
        showSuccessToast(this.$swal, this.$t('success.executionUpdated'))
      }).catch((err) => {
        showErrorToast(this.$swal, this.$t('error.executionUpdateFailed'), {}, err?.response?.data)
      })
      
    },
    async deleteExecution(id){
      if(!this._deleteActivity){
        this.unauthorizedToast;
        return ;
      }
      const handle = this.$route.params.handle
      const projectKey = this.$route.params.key
      const executionService = makeExecutionService(this.$api);
      
      await executionService.deleteExecutions(handle, projectKey, id).then(() => {
        this.testRunExecutions = this.testRunExecutions.filter(element => element.uid !== id)
        showSuccessToast(this.$swal, this.$t('success.executionDeleted'))
      }).catch((err) => {
        showErrorToast(this.$swal, this.$t('error.executionDeletedFailed'), {}, err?.response?.data)
      })
      
    },
    async moveSelectedItem(direction){
      let itemIndex = this.testRunExecutions.findIndex(element => element.uid == this.selectedExecution.uid)
      if(direction == 'next' && itemIndex < this.testRunExecutions.length-1){
        const testRunExecution = this.testRunExecutions[itemIndex + 1]
        await this.reloadExecution(testRunExecution.uid);
      } else if(direction == 'previous' && itemIndex > 0) {
        const testRunExecution = this.testRunExecutions[itemIndex - 1]
        await this.reloadExecution(testRunExecution.uid);
      }

      let newPath = this.$route.path;
      let updatedPath;
      if (newPath.includes('/executions/')) {
        updatedPath = newPath.replace(/\/executions\/[^/]+/, `/executions/${this.selectedExecution.uid}`);
      } else {
        updatedPath = `${newPath}/executions/${this.selectedExecution.uid}`;
      }
      if (updatedPath !== newPath) {
        this.$router.replace({ path: updatedPath }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            console.warn('Router navigation error:', err);
          }
        });
      }
      await this.getExecutionResult(this.selectedExecution.uid);
    },
    async addTestResult(data,isExecutionFailed){
      if(!this._writeActivity){
        this.unauthorizedToast;
        return ;
      }
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const selectedExecution = this.selectedExecution.uid;
      const payload = {
        status: data.status,
        comment: data.comment,
        tagUids: data.tagUids
      };

      await resultService.addTestResult(handle, projectKey, selectedExecution, payload).then(async (response) => {
        const result = response.data;
        const mediaType = 'attachment'
        const params = {
          handle,
          projectKey,
          resultUid: result.uid
        }
        if(data.files.length)
         await Promise.all(data.files.map(async (file) => {
          await this.uploadToServer( {handle, mediaType, file, apiService: resultService, params});
        })).then(() => {
          showSuccessToast(this.$swal, this.$t('success.testResultAdded'))
           if(isExecutionFailed){
            this.failedExecution = this.selectedExecution;
            this.failedResultUid = result.uid;
            this.showAddDefectDialog =true;
          }
        }).catch((err) => {
          if (err?.status == 507)
            showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, 'limitReached', handle)
          else
            showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, 'limitReached')
        })
        else
          showSuccessToast(this.$swal, this.$t('success.testResultAdded'))
        
        const executionIndex = this.testRunExecutions.findIndex(element => element.uid == selectedExecution);
        this.selectedExecution = {...this.selectedExecution, ...{updatedAt : result.updatedAt, status: payload.status}}
        this.testRunExecutions.splice(executionIndex, 1, {...this.selectedExecution, ...{updatedAt : result.updatedAt, status: payload.status}})
         if(isExecutionFailed){
            this.failedExecution = this.selectedExecution;
            this.failedResultUid = result.uid;
            this.showAddDefectDialog =true;
          }
      }).catch((err) => {
        showErrorToast(this.$swal, this.$t('error.failedToAddTestResult'), {}, err?.response?.data)
      })
      
      await this.getExecutionResult(selectedExecution)
    },
    async updateTestResult(resultUid,data){
      if(!this._writeActivity){
        this.unauthorizedToast;
        return ;
      }
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const payload = {
        status: data?.status,
        comment: data?.comment
      }
      
      if(resultUid){
        await resultService.updateTestResult(handle, projectKey, resultUid, payload).then(async () => {
          const resultIndex = this.testResults.findIndex(element => element.resultUid == resultUid)
          this.testResults[resultIndex].status = payload.status[0].toUpperCase()+payload.status.slice(1)
          this.testResults[resultIndex].comment = payload.comment;
          const mediaType = 'attachment'
          const params = {
            handle,
            projectKey,
            resultUid
          }
          if(data.files.length)
            await Promise.all(data.files.map(async (file) => {
              await this.uploadToServer({handle, mediaType, file, apiService: resultService, params});
            })).then(() => {
              showSuccessToast(this.$swal, this.$t('success.testResultUpdated'))
            }).catch((err) => {
              if (err?.status == 507)
                showErrorToast(this.$swal, this.$t('success.testResultUpdated'), {}, 'limitReached', handle)
              else
                showErrorToast(this.$swal, this.$t('success.testResultUpdated'), {}, err?.response?.data)
            })
          else
            showSuccessToast(this.$swal, this.$t('success.testResultUpdated'))
        }).catch((err) => {
          showErrorToast(this.$swal, this.$t('success.testResultUpdated'), {}, err?.response?.data)
        })
      }
    },
    async deleteTestResult(resultUid){
      if(!this._deleteActivity){
        this.unauthorizedToast;
        return ;
      }
      const resultService = makeResultService(this.$api);
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      
      await resultService.deleteTestResult(handle, projectKey, resultUid).then(() => {
        showSuccessToast(this.$swal, this.$t('success.testResultDeleted'));
        const resultIndex = this.testResults.findIndex(element => element.resultUid == resultUid)
        this.testResults.splice(resultIndex,1)
      }).catch( (err) => {
        showErrorToast(this.$swal, this.$t('error.failedToDeleteTestResult'), {}, err?.response?.data)
      } )
    },
    handleDefectActionType(actionType) {    
      this.showAddDefectDialog = false;
      this.selectedDefectAction = actionType;
      // items: ['Create new defect', 'Link existing defect'],
      if (actionType === 'Create new defect') {
        this.showCreateDefectDialog = true;
      } else if (actionType === 'Link existing defect') {
        this.showCreateDefectDialog = true;
      }
    },
    async reloadExecutions(){
      if(this.executionUidParams){
        await this.getTestRunExecutions(this.$route.params.handle, this.$route.params.key, this.$route.params.id);
        await this.getTestRunById(this.$route.params.handle, this.$route.params.key, this.$route.params.id);
        await this.getExecution(this.executionUidParams);
      } else {
        // If no specific execution is selected, just reload executions for current folder
        await this.refreshExecutions();
      }
    },
    handleDefectCreated() {
      this.showCreateDefectDialog = false;
      this.showAddDefectDialog = false;
      this.failedExecution = null;
      this.failedResultUid = null; // Clear failedResultUid on successful defect creation
      this.reloadExecutions()
    },

    handleDefectLinked() {
      this.showCreateDefectDialog = false;
      this.showAddDefectDialog = false;
      this.failedExecution = null;
      this.failedResultUid = null; // Clear failedResultUid on successful defect linking
      this.reloadExecutions();
    },
    async handleRefetchExecution(updatedExecution) {
      try {
        // Update the selectedExecution with the updated data
        if (this.selectedExecution?.uid === updatedExecution.uid) {
          this.selectedExecution = { ...this.selectedExecution, ...updatedExecution };
        }
        
        // Update the execution in the testRunExecutions array
        const findIndex = this.testRunExecutions.findIndex(element => element.uid === updatedExecution.uid);
        if (findIndex !== -1) {
          this.$set(this.testRunExecutions, findIndex, { ...this.testRunExecutions[findIndex], ...updatedExecution });
        }
        
        // Show success message for assignedTo updates specifically
        if (updatedExecution.assignedTo !== undefined) {
          showSuccessToast(this.$swal, this.$t('success.testCaseAssigned'));
        }
      } catch (error) {
        console.error('Error in handleRefetchExecution:', error);
        showErrorToast(this.$swal, this.$t('error.executionUpdateFailed'), {}, error?.response?.data);
      }
    },
    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      // Only update if page or itemsPerPage actually changed
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
        this.refreshExecutions();
      }
    },
    async refreshExecutions() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const id = this.$route.params.id;
      await this.getTestRunExecutions(handle, projectKey, id);
    }
  },
};
</script>

<style lang="scss" scoped>
.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>
<style lang="scss" scoped>
.custom-switch {
  ::v-deep .v-input--switch__thumb {
    width: 24px;
    height: 24px;
    top: 0;
    right: 2px;
  }

  ::v-deep .primary--text{
    background-color: #ffffff !important; /* Custom track color when switch is on */
  }

  ::v-deep .primary--text.v-input--switch__track {
    background-color: #0000ff !important; /* Custom thumb color */
    opacity: 1;
  }
}
.toggle-folder{
  gap: 10px;
  align-items: center;
}
</style>
<style scoped>
  .custom-progressbar{
    width: 200px;
  }
  .__attachment_progress{
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    flex-direction: column !important;
    z-index: 999;
  }
</style>
