<template>
  <v-container
    class="d-flex flex-column justify-start"
    fluid
  >
    <v-row>
      <v-col class="pr-0 pl-3">
        <RunEditor
          v-if="selectedItem"
          :milestones="activeMilestones"
          :item="selectedItem"
          @update-filter="updateItem"
          @update-tags="updateTags"
          @update-milestones="updateMilestones"
          @update-due-date="updateDueDate"
          @tags-list="getTags"
        />
        <RunAddCaseView
          ref="runAddCaseView"
          v-model="runCases"
          :case-type="'EditCase'"
          :back-title="$t('testruns.create_testrun.back_to_testrun')"
          :show-pagination="false"
          @input="updateCases"
        >
          <template #action>
            <v-btn
              v-if="!skeletonLoaderState"
              color="gray-100"
              depressed
              class="ml-2 btn-theme text-capitalize"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
              height="38px"
              @click="$router.go(-1)"
            >
              {{ $t('cancel') }}
            </v-btn>
            <v-btn
              v-if="!skeletonLoaderState"
              color="primary"
              depressed
              class="f-color-white ml-2 btn-theme text-capitalize"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
              height="38px"
              :loading="loading"
              @click="saveTestRuns"
            >
              {{ $t('save') }}
            </v-btn>
          </template>
        </RunAddCaseView>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import RunEditor from '@/components/TestRuns/RunEditor.vue';
import RunAddCaseView from '@/views/Tests/Runs/Create/RunAddCaseView';
import { mapGetters } from 'vuex';
import makeMilestoneService from '@/services/api/milestone'
import makeRunService from '@/services/api/run';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import handleLoading from '@/mixins/loader.js'

let runsService;

export default {
  name: 'TestRunEdit',
  components: {
    RunEditor,
    RunAddCaseView,
  },
  mixins: [handleLoading],
  props: ['customItem'],
  data() {
    return {
      selectedItem: null,
      runCases: null,
      milestones: [],
      loading: false,
      selectedTags: [],
      selectedMilestones: [],
      selectedDueDate: null,
      tags: [],
      initialCases: [],
    };
  },
  computed: {
    ...mapGetters({
      currentOrg: 'user/currentAccount',
    }),
    activeMilestones() {
      return this.milestones.filter(milestone => !milestone.archivedAt && !milestone.deletedAt)
      .map(milestone => ({ name: milestone.name, uid: milestone.uid }));
    },
    addedCases() {
      const initialUids = new Set(this.initialCases?.map(c => c.uid))
      return this.runCases?.filter(c => !initialUids.has(c.uid))
    },
    removedCases() {
      if (!this.runCases?.length || !this.initialCases?.length) return []
      const runUidSet = new Set(this.runCases.map(c => c.uid))
      return this.initialCases.filter(c => !runUidSet.has(c.uid))
    }
  },
  watch: {
    // Watch for route changes to reset state when navigating between different test runs
    '$route'(to, from) {
      // Only reset if we're navigating to a different test run edit page
      if (to.name === 'TestRunEdit' && from.name === 'TestRunEdit' && to.params.id !== from.params.id) {
        this.resetComponentState();
        this.getProjectRun();
        this.getMilestones();
      }
    }
  },
  async created() {
    runsService = makeRunService(this.$api);
  },
  async mounted(){ 
    await this.getProjectRun();
    this.getMilestones();
  },
  methods: {
    updateItem(item){
      this.selectedItem = item
    },
    updateTags(tags){
      this.selectedTags = tags
    },
    updateMilestones(milestones){
      this.selectedMilestones = milestones
    },
    updateDueDate(dueDate){
      this.selectedDueDate = dueDate
    },
    getTags(tags){
      this.tags = tags
    },
    async getProjectRun() {
      
      try {

        const { handle, key: projectKey, id: runId } = this.$route.params;

        const [response, cases] = await Promise.all([
            runsService.getTestRunById(handle, projectKey, runId),
            runsService.getTestRunCases(handle, projectKey, runId)
        ]);

        this.selectedItem = {...response.data,
          status: response?.data?.status ?? response?.data?.customFields?.status,
          priority: response?.data?.priority ?? response?.data?.customFields?.priority,
          references: response?.data?.references || [],
        };
        this.selectedTags = response.data.customFields.tagUids;
        this.runCases = cases.data.items;
        this.initialCases = [...cases.data.items];

      } catch (error) {
        showErrorToast(this.$swal, this.$t('testruns.edit_testrun.not_found'), {}, error?.response?.data)
        this.$router.push({name: 'Runs'})
      }

    },
    async saveTestRuns() {
      if(this.selectedItem){

        const addedMilestones = this.selectedMilestones?.length > 0 ? this.milestones
          .filter(milestone => this.selectedMilestones.includes(milestone.uid))
          .map(milestone => milestone.uid) : [];

        const removedMilestones = this.selectedMilestones?.length > 0 ? this.milestones
          .filter(milestone => !this.selectedMilestones.includes(milestone.uid))
          .map(milestone => milestone.uid) : [];

        const addedTags = this.selectedTags?.length > 0 ? this.tags
          .filter(tag => this.selectedTags?.includes(tag.uid))
          .map(tag => tag.uid) : [];

        const removedTags = this.selectedTags?.length > 0 ? this.tags
          .filter(tag => !this.selectedTags?.includes(tag.uid))
          .map(tag => tag.uid) : [];


        const payload = {
          name: this.selectedItem?.name,
          status: this.selectedItem?.status ?? this.selectedItem?.customFields?.status,
          priority: this.selectedItem?.priority ?? this.selectedItem?.customFields?.priority,
        }

  

        addedMilestones.length && (payload.addMilestoneUids = addedMilestones);
        this.selectedDueDate && (payload.dueAt = this.selectedDueDate);
        removedMilestones.length && (payload.removeMilestoneUids = removedMilestones);
        addedTags.length && (payload.addTagUids = addedTags);
        removedTags.length && (payload.removeTagUids = removedTags);
        this.selectedItem?.description && (payload.description = this.selectedItem?.description);

        let handle = this.$route.params.handle;

          try {
            this.loading = true;
            const response = await runsService.updateTestRun(handle, this.$route.params.key, this.$route.params.id, payload);
            await runsService.updateTestRunCases(handle, this.$route.params.key, this.$route.params.id, {
              addCaseUids: this.addedCases.map(c => c.uid),
              removeExecUids: this.removedCases.map(c => c.uid)
            });

            if(response.status === 200) {
              showSuccessToast(this.$swal, this.$t('success.testRunUpdated'));

              if (this.$route.query.redirectTo && this.$route.query.redirectTo === 'TestPlanDetail') {
                this.$router.replace({
                  name: this.$route.query.redirectTo,
                  params: { ...this.$route.params, planId: this.$route.query.planId }
                });
              } else if(this.$route.query.isPlanRerunEdit && this.$route.query.planId) {
                this.$router.replace({
                  name: 'TestPlanRerun',
                  params: { ...this.$route.params, planId: this.$route.query.planId }
                });
              } else {
                this.$router.push({
                  name: 'Runs', params: {
                    handle: handle,
                    key: this.$route.params.key
                  }
                });
              }
            }
          } catch (error) {
            showErrorToast(this.$swal, 'updateError', { item: 'test run' }, error?.response?.data);
            console.error('Failed to Update Test Run:', error);
          } finally {
            this.loading = false;
          }
      }
    },
    updateCases(cases) {
      this.runCases = cases;
    },
    resetComponentState() {
      // Reset the component state when navigating between different test runs
      this.selectedItem = null;
      this.runCases = null;
      this.loading = false;
      this.selectedTags = [];
      this.selectedMilestones = [];
      this.selectedDueDate = null;
      this.initialCases = [];
      
      // Reset the child RunAddCaseView component if it exists
      if (this.$refs.runAddCaseView && this.$refs.runAddCaseView.reset) {
        this.$refs.runAddCaseView.reset();
      }
    },
    async getMilestones() {
      try {
        const milestoneSrvice = makeMilestoneService(this.$api);
        const response = await milestoneSrvice.getMilestones(this.$route.params.handle, this.$route.params.key);
        this.milestones = response.data.items;
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, error?.response?.data);
        this.milestones = []; // Reset to empty array in case of error
      } 
    },
    // Refreshes all data
    async refreshData() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const runId = this.$route.params.id;
      this.getProjectRun(
        handle,
        projectKey,
        runId
      );
    },
  }
};
</script>