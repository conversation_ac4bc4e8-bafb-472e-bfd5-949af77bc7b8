<template>
  <v-container
    fluid
    class="pa-0"
  >
    <RunAddCaseView
      v-if="showCases"
      v-model="selectedCases"
      class="mt-3"
      :back-title="$t('testruns.create_testrun.back_to_create_testrun')"
      @back="onBackShowCases"
      @close="handleBackClick"
    >
      <template #action>
        <v-btn
          color="primary"
          depressed
          class="f-color-white ml-2 btn-theme text-capitalize"
          :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
          height="38px"
          :loading="onCreateLoading"
          :class="{ 'btn-loading-opacity': onCreateLoading }"
          @click="createTestRuns"
        >
          {{ $t('save') }}
        </v-btn>
      </template>
    </RunAddCaseView>
    <v-card
      v-show="!showCases"
      class="py-6 px-6 app-height-global mt-3"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row>
        <v-col flex>
          <div
            class="back-to-projects"
            @click="handleBackClick"
          >
            <v-icon color="blue">
              mdi-chevron-left
            </v-icon>
            <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
              {{ $t('testruns.create_testrun.back_to_testrun') }}
            </p>
          </div>
        </v-col>
      </v-row>
      <template>
        <v-row justify="center">
          <v-col
            md="4"
            lg="3"
            class="mb-3"
          >
            <h2 class="text-start">
              {{ $t('testruns.create_testrun.title') }}
            </h2>
          </v-col>
        </v-row>
        <v-form
          ref="runForm"
          role="createRunForm"
          class="text-left"
        >
          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.create_testrun.testrun_name') }}<strong class="red--text text--lighten-1">*</strong>
              </v-label>
              <v-text-field
                v-model="selectedRun.name"
                type="text"
                dense
                :placeholder="$t('testruns.create_testrun.testRunName')"
                height="38px"
                :rules="runNameRule"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
              />
            </v-col>
          </v-row>
          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
              class="pt-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.create_testrun.description') }}
              </v-label>
              <v-text-field
                v-model="selectedRun.description"
                type="text"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                dense
                height="38px"
                :placeholder="$t('description')"
              />
            </v-col>
          </v-row>
          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
              class="pt-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.create_testrun.milestone') }}
              </v-label>
              <v-select
                v-model="selectedRun.milestoneUids"
                type="text"
                dense
                background-color="#F9F9FB"
                multiple
                :placeholder="$t('milestone.choose')"
                :items="activeMilestones"
                class="rounded-lg field-theme combo-box custom-prepend mh-38px"
                append-icon="mdi-chevron-down"
                item-text="name"
                item-value="uid"
                :menu-props="{ offsetY: true }"
              >
                <template #selection="{ item }">
                  <v-tooltip
                    bottom
                    max-width="485px"
                    :disabled="item.name.length < 61"
                    content-class="tooltip-theme"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        class="d-flex align-center custom-chip-theme mr-1 mb-1"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <div class="text-theme-label label text-truncate mr-1">
                          {{ item.name }}
                        </div>
                        <v-icon
                          size="16px"
                          @click="onRemoveSelectedMilestone(item.uid)"
                        >
                          mdi-close
                        </v-icon>
                      </div>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                </template>
                <template #item="{ item, on, attrs }">
                  <v-list-item
                    :ripple="false"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item-action>
                      <v-checkbox
                        hide-details
                        :input-value="milestoneSelection(item.uid)"
                        class="field-theme mt-0 pt-0"
                        :ripple="false"
                        off-icon="icon-checkbox-off"
                        on-icon="icon-checkbox-on"
                      >
                        <template #label>
                          <v-tooltip
                            bottom
                            max-width="485px"
                            :disabled="item.name.length < 61"
                            content-class="tooltip-theme"
                          >
                            <template #activator="{ on, attrs }">
                              <span
                                class="fs-14px text-theme-label text-truncate"
                                v-bind="attrs"
                                v-on="on"
                              >
                                {{ item.name }}
                              </span>
                            </template>
                            <span>{{ item.name }}</span>
                          </v-tooltip>
                        </template>
                      </v-checkbox>
                    </v-list-item-action>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
          </v-row>
          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
              class="pt-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.create_testrun.priority') }}
              </v-label>
              <v-select
                v-model="selectedRun.priority"
                type="text"
                dense
                background-color="#F9F9FB"
                height="38px"
                :placeholder="$t('choosePriority')"
                :items="priorities"
                class="rounded-lg field-theme combo-box custom-prepend"
                append-icon="mdi-chevron-down"
                item-text="name"
                item-value="name"
                return-object
              />
            </v-col>
          </v-row>
          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
              class="pt-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.create_testrun.tags') }}
              </v-label>
              <v-select
                v-model="selectedRun.tags"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('chooseTags')"
                :items="tags"
                item-text="name"
                item-value="uid"
                class="rounded-lg field-theme custom-prepend mh-38px"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                multiple
                return-object
              >
                <template #selection="{ item }">
                  <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
                    <div class="text-theme-label label text-truncate mr-1">
                      {{ item.name }}
                    </div>
                    <v-icon
                      size="16px"
                      @click="onRemoveSelectedTags(item.uid)"
                    >
                      mdi-close
                    </v-icon>
                  </div>
                </template>

                <template #item="{ item, on, attrs }">
                  <v-list-item
                    :ripple="false"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item-action>
                      <v-checkbox
                        hide-details
                        :input-value="tagsSelection(item.uid)"
                        class="field-theme mt-0 pt-0"
                        :ripple="false"
                        off-icon="icon-checkbox-off"
                        on-icon="icon-checkbox-on"
                      >
                        <template #label>
                          <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                        </template>
                      </v-checkbox>
                    </v-list-item-action>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
          </v-row>

          <v-row justify="center">
            <v-col
              md="4"
              lg="3"
              class="pt-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('milestone.create_milestone.dueDate') }}
              </v-label>
              <v-menu
                v-model="menuDueDate"
                max-width="290"
                offset-x
                top
              >
                <template #activator="{ on }">
                  <div
                    class="calendar-textbox-container"
                    v-on="on"
                  >
                    <v-text-field
                      dense
                      single-line
                      filled
                      color="blue"
                      class="mr-0 custom_input rounded-lg calendar-textbox"
                      :value="dueDate"
                      placeholder="MM/DD/YY"
                      readonly
                    />
                    <calendarBlueIcon 
                      class="calendar-icon"  
                    />
                  </div>
                </template>
                <v-date-picker
                  v-model="dueDate"
                  @change="onDateChange"
                />
              </v-menu>
            </v-col>
          </v-row>
        </v-form>
      </template>
      <v-row
        justify="end"
        class="mt-4"
      >
        <v-col cols="12">
          <v-flex class="mt-6 d-sm-flex justify-end">
            <v-btn
              color="primary"
              depressed
              class="f-color-white ml-2 btn-theme text-capitalize"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
              height="38px"
              @click="handleAddCases"
            >
              {{ $t('next') }}
            </v-btn>
          </v-flex>
        </v-col>
      </v-row>
    </v-card>
    <ProjectDiscardDialog
      v-model="showConfirmBackDialog"
      :title="$t('testruns.create_testrun.close_dialog.title')"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmClick"
    />
  </v-container>
</template>

<script>
import { useCreateTestRun } from '@/composables/modules/testRun/create';
import ProjectDiscardDialog from '@/components/Project/ProjectDiscardDialog.vue';
import RunAddCaseView from '@/views/Tests/Runs/Create/RunAddCaseView.vue';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';

export default {
  components: {
    ProjectDiscardDialog,
    RunAddCaseView,
    calendarBlueIcon
  },

  setup() {
    // Additional method for handling back from cases view, if needed

    const {
      runNameRule,
      selectedRun,
      dueDate,
      menuOpen,
      showConfirmBackDialog,
      search,
      selectedCases,
      milestones,
      menuDueDate,
      tags,
      cases,
      showCases,
      onCreateLoading,
      statuses,
      priorities,
      onDateChange,
      activeMilestones,
      isTestPlanCreate,
      createTestRuns,
      handleAddCases,
      handleBackClick,
      onAddCases,
      handleCloseClick,
      handleDuplicateClick,
      handleConfirmClick,
      milestoneSelection,
      onRemoveSelectedMilestone,
      onRemoveSelectedTags,
      tagsSelection,
      updateCases,
      getCases,
      onBackShowCases,
      runForm,
      fileInput,
    } = useCreateTestRun();

    return {
      runNameRule,
      selectedRun,
      dueDate,
      menuOpen,
      showConfirmBackDialog,
      search,
      selectedCases,
      milestones,
      menuDueDate,
      tags,
      cases,
      showCases,
      onCreateLoading,
      onDateChange,
      statuses,
      priorities,
      activeMilestones,
      isTestPlanCreate,
      createTestRuns,
      handleAddCases,
      handleBackClick,
      onAddCases,
      handleCloseClick,
      handleDuplicateClick,
      handleConfirmClick,
      milestoneSelection,
      onRemoveSelectedMilestone,
      onRemoveSelectedTags,
      tagsSelection,
      updateCases,
      getCases,
      onBackShowCases,
      runForm,
      fileInput,
    };
  },
};
</script>

<style lang="scss" scoped>
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 9px;
}
.box-shadow-none {
  box-shadow: none;
}

.calendar-textbox-container {
  position: relative;
}
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}
.search_input {
  width: 100%;
  @media screen and (min-width: 600px) {
    width: 300px;
  }
}
.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}
.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}
</style>
