<template>
  <div
    id="case-index-container"
    class="d-flex flex-column align-stretch font-inter"
  >
    <v-row>
      <v-col class="pb-1">
        <v-card
          class="py-6 px-6"
          rounded="lg"
          elevation="0"
          width="100%"
        >
          <v-row v-if="caseType != 'EditCase'">
            <v-col flex>
              <div
                class="back-to-projects"
                @click="handleBackClick"
              >
                <v-icon color="blue">
                  mdi-chevron-left
                </v-icon>
                <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
                  {{ backTitle }} 
                </p>
              </div>
            </v-col>
            <v-col
              cols="auto"
              class="pointer"
              @click="handleCloseClick"
            >
              <v-icon>mdi-close</v-icon>
            </v-col>
          </v-row>
          <v-row class="pt-0">
            <v-col
              flex
              class="pt-0"
            >
              <template v-if="!skeletonLoaderState">
                <p
                  v-if="caseType == 'EditCase'"
                  class="text-start font-weight-bold mb-2 fs-24px"
                >
                  {{ $t('testruns.testCases') }}
                </p>
                <p
                  v-else
                  class="text-start font-weight-bold mb-2 fs-24px"
                >
                  {{ $t('testruns.addtestcases') }}
                </p>
              </template>
              <v-skeleton-loader
                v-else
                height="36"
                width="140"
                type="heading"
              />
              <div
                v-if="!skeletonLoaderState"
                class="mt-6 d-flex"
              >
                <v-chip
                  :class="{ 'blue--text': !tableFilter }"
                  width="200px"
                  :color="!tableFilter ? 'blue-light' : 'gray-light'"
                  label
                  @click="changeFilter(false)"
                >
                  <div class="font-weight-bold px-2">
                    {{ $t('testruns.unlinked') }} <span class="ml-2">{{ casesCount }}</span>
                  </div>
                </v-chip>
                <div class="ml-2">
                  <v-chip
                    :class="{ 'blue--text': tableFilter }"
                    width="200px"
                    :color="tableFilter ? 'blue-light' : 'gray-light'"
                    label
                    @click="changeFilter(true)"
                  >
                    <div class="font-weight-bold px-2">
                      {{ $t('testruns.linked') }} <span class="ml-2">{{ selectedRunCasesCount }}</span>
                    </div>
                  </v-chip>
                </div>
              </div>
              <div
                v-else
                class="mt-6 d-flex"
              >
                <v-skeleton-loader
                  class="rounded-sm d-flex gap-2 chip-primary"
                  height="32"
                  width="200"
                  type="button@2"
                />
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <CaseManagement
      :show-collapse="false"
      :show-create="false"
      :select-option="tableFilter"
      :quick-create="false"
      :allow-action="false"
      :cases="cases"
      :write-entity="writeEntity"
      :total-items="composableTotalCases"
      :delete-entity="deleteEntity"
      :folders="folders"
      :run-cases="selectedCases"
      :from-run="true"
      :set-selected-folder-uid.sync="folderUid"
      :pagination="paginationData"
      :current-page="composableCurrentPage"
      :items-per-page="composableItemsPerPage"
      :update-pagination="composableUpdatePagination"
      :relations-loading="composableRelationsLoading"
      :cases-loading="composableCasesLoading"
      :show-pagination="!tableFilter"
      @refresh-folders="getFolders"
      @selectedCases="handleCases"
      @updateCaseCount="UpdateCases"
      @folder-select="getCases"
      @update-cases="updateCases"
    >
      <template slot="control-area">
        <div
          v-if="!skeletonLoaderState"
          class="action-btn-wrapper pa-4 pl-6"
        >
          <v-row>
            <v-btn
              v-if="!isEditView"
              depressed
              height="40"
              class="fw-semibold black--text btn-theme mr-3"
              color="#F2F4F7"
              @click="handleBackClick"
            >
              <v-icon 
                color="blue" 
                size="16" 
                class="mr-1"
              >
                mdi-chevron-left
              </v-icon>
              {{ $t('back') }} 
            </v-btn>
            <v-col class="d-flex justify-end">
              <v-btn
                v-if="!isEditView"
                depressed
                height="40"
                class="fw-semibold black--text btn-theme mr-3"
                color="#F2F4F7"
                @click="onAddTestCase"
              >
                <v-icon
                  class="mr-1"
                  size="16"
                >
                  mdi-plus
                </v-icon> {{ $t('createNewTestCase') }} 
              </v-btn>
              <slot name="action" />
            </v-col>
          </v-row>
        </div>
      </template>
    </CaseManagement>
    
    <RunDiscardDialog
      v-model="showAddDialog"
      :title="ConfirmDialog_Title"
      :content="ConfirmDialog_Content"
      :content_part2="ConfirmDialog_Contentpart_two"
      :run_name="ConfirmDialog_RunName"
      :btn_label="ConfirmDialog_btn_label"
      :color="ConfirmDialog_btn_color"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick(ConfirmType)"
    />
  </div>
</template>

<script>
import RunDiscardDialog from '@/components/TestRuns/RunDiscardDialog.vue';
import CaseManagement from '@/components/Cases/CaseManagement'
import { createNamespacedHelpers } from 'vuex';
import { showErrorToast } from '@/utils/toast';
import handleLoading from '@/mixins/loader.js'
import { useTestCasesIndex } from '@/composables/modules/cases/index';
import { tableSelectedCases } from '@/composables/modules/cases/list';
import projectStatus from '@/mixins/projectStatus';
import permissions from '@/mixins/permissions';

const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'TestRunCreate',
  components: {
    CaseManagement,
    RunDiscardDialog,
  },
  mixins: [handleLoading, projectStatus, permissions],
  props: {
    value: {
      type: Array,
    },
    customCases: {
      type: Array,
    },
    caseType: {
      type: String,
    },
    backTitle: {
      type: String,
    },
    showPagination: {
      type: Boolean,
      default: true
    },
  },
  setup() {
    const { 
      cases, 
      folders, 
      getCases, 
      getFolders,
      totalCases,
      casesLoading,
      currentPage,
      itemsPerPage,
      totalPages,
      updatePagination,
      relationsLoading
    } = useTestCasesIndex();
    
    return {
      composableCases: cases,
      composableFolders: folders,
      composableGetCases: getCases,
      composableGetFolders: getFolders,
      composableTotalCases: totalCases,
      composableCasesLoading: casesLoading,
      composableCurrentPage: currentPage,
      composableItemsPerPage: itemsPerPage,
      composableTotalPages: totalPages,
      composableUpdatePagination: updatePagination,
      composableRelationsLoading: relationsLoading
    };
  },
  data() {
    return {
      showConfirmBackDialog: false,
      showAddDialog: false,
      tableFilter: false,
      cases: [],
      folders: [],
      selectedCases: [],
      isColumnFilter: false,
      isLoading: false,
      ConfirmDialog_Title: '',
      ConfirmDialog_Content: '',
      ConfirmDialog_Contentpart_two: '',
      ConfirmDialog_btn_label: '',
      ConfirmDialog_btn_color: 'primary',
      ConfirmDialog_RunName: '',
      ConfirmType: '',
      casesCount: 0,
      drawer: false,
      selectedCase: null,
      folderUid: null,
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    anySelectedCases() {
      return this.selectedCases?.length > 0;
    },
    selectedRunCases:{
      get() {
        return this.value; 
      },
      set(value) {
        this.selectedCases = value;
      }
    },
    deleteActivity(){
      return this.authorityTo('delete_activity')
    },
    deleteEntity(){
      return this.authorityTo('delete_entity')
    },
    writeEntity(){
      return this.authorityTo('write_entity')
    },
    isEditView() {
      return this.$route.name === 'TestRunEdit';
    },
    selectedCasesCount() {
      return this.value ? this.value?.length : 0;
    },
    selectedRunCasesCount: {
      get() {
        return this.selectedRunCases?.length;
      },
      set(value) {
        this.selectedRunCases = value;
      }
    },
    paginationData() {
      return {
        total: this.composableTotalCases,
        totalPages: this.composableTotalPages,
      };
    },
  },
  watch: {
    // Watch for changes in composable data and sync with local data
    composableCases: {
      handler(newCases) {
        this.cases = newCases;
        this.casesCount = this.composableTotalCases;
      },
      immediate: true
    },
    composableFolders: {
      handler(newFolders) {
        this.folders = newFolders;
      },
      immediate: true
    },
    // Watch for value prop changes (when parent explicitly sets selected cases)
    value: {
      handler(newValue) {
        // Only update if there's actually a change to prevent infinite loops
        if (JSON.stringify(this.selectedCases) !== JSON.stringify(newValue || [])) {
          this.selectedCases = newValue || [];
          
          // If parent resets to empty array, also clear the persistent selected cases
          if (!newValue || newValue.length === 0) {
            tableSelectedCases.value = [];
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // Ensure we start with clean state
    this.selectedCases = this.value || [];
    tableSelectedCases.value = [];
    if (this.caseType === 'EditCase') {
      this.tableFilter = false;
    }
  },
  async mounted() {
    try {
      await this.getFolders();

      if (this.$route.params.folderUid) {
        this.folderUid = this.$route.params.folderUid;
        await this.getCases(this.$route.params.folderUid);
      } else {
        await this.getAllCases();
        this.selectDefaultFolder();
      }
    
      // Safely iterate over cases if they exist
      if (this.cases && Array.isArray(this.cases)) {
        this.cases.forEach((cse) => {
          this.$set(cse, 'toBeSelected', false);
        });
      }
      
      this.selectedCases = this.value || [];
      this.tableFilter = false;
    } catch (error) {
      console.error('Error during component initialization:', error);
      // Don't block the UI if there's an error, just log it
    }
  },
  methods: {
    selectDefaultFolder() {
      if (this.folders && this.folders.length > 0 && !this.folderUid) {
        const initialSelectedFolder = this.$route.params.folderUid ?? this.folders[0].uid;
        this.folderUid = initialSelectedFolder;
        this.getCases(initialSelectedFolder);
      }
    },
    async getAllCases() {
      // Wait for folders to be loaded if they're not available yet
      if (!this.folders || this.folders.length === 0) {
        await this.getFolders();
      }
      if (this.folders && this.folders.length > 0) {
        await this.composableGetCases(this.folders[0].uid);
      }
    },
    async getCases(folderUID) {
      if(!folderUID) return;
      this.folderUid = folderUID;

      await this.composableGetCases(folderUID);

      if (this.isEditView && this.$route.params.folderUid !== folderUID) {
        try {
         await this.$router.replace({
           name: this.$route.name,
           params: {
            ...this.$route.params,
            folderUid: folderUID
          },
          query: {
            ...this.$route.query,
            ...(this.$route.query.isPlanRerunEdit && this.$route.params.planId
              ? { isPlanRerunEdit: 'true', planId: this.$route.params.planId }
              : {})
          }
        });
      } catch (error) {
         if (error?.name === 'NavigationDuplicated' || (error?.message && error.message.includes('NavigationDuplicated'))) {
             return; 
         }
         showErrorToast(this.$swal, 'fetchError', { item: 'cases' }, error?.response?.data);
       }
      }
    },
    toggleDrawer() {
      this.drawer = !this.drawer;
    },
    handleConfirmBtnClick(type) {
      this.showAddDialog = false;

      if (type == 'cancel')
        this.$router.replace({
          name: 'Runs',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key
          }
        });
    },
    handleCases(cases) {
      this.selectedCases = cases;
      this.$emit('input', this.selectedCases);
      
      // Ensure tableSelectedCases is also updated
      tableSelectedCases.value = cases;
    },
    async getFolders() {
      await this.composableGetFolders();
    },
    UpdateCases(selectedCount) {
      this.selectedCaseCount = selectedCount;
    },
    handleBackClick() {
      this.$emit('back')
    },
    handleAddClick(type) {
      if (type == 'add') {
        this.ConfirmDialog_Title = this.$t('testruns.test_case.addcase.title');
        this.ConfirmDialog_Content = this.$t('testruns.test_case.addcase.content');
        this.ConfirmDialog_Contentpart_two = '';
        this.ConfirmDialog_btn_label = this.$t('add');
        this.ConfirmDialog_btn_color = 'primary';
        this.ConfirmDialog_RunName = '';
        this.ConfirmType = 'add';
        this.showAddDialog = true;
      } else if (type == 'remove') {
        this.ConfirmDialog_Title = this.$t('testruns.test_case.removecase.title');
        this.ConfirmDialog_Content = this.$t('testruns.test_case.removecase.content');
        this.ConfirmDialog_Contentpart_two = '';
        this.ConfirmDialog_btn_label = this.$t('remove');
        this.ConfirmDialog_btn_color = 'danger';
        this.ConfirmDialog_RunName = '';
        this.ConfirmType = 'remove';
        this.showAddDialog = true;
      } else if (type == 'cancel') {
        this.ConfirmDialog_Title = this.$t('testruns.edit_testrun.title');
        this.ConfirmDialog_Content = this.$t('testruns.edit_testrun.content');
        this.ConfirmDialog_Contentpart_two = '';
        this.ConfirmDialog_btn_label = this.$t('testruns.edit_testrun.btn_label');
        this.ConfirmDialog_btn_color = 'primary';
        this.ConfirmDialog_RunName = '';
        this.ConfirmType = 'cancel';
        this.showAddDialog = true;
      }
    },
    onAddTestCase() {
      if(this.$route.params.id && this.$route.params.folderUid){
        this.$router.push({
          name: 'CreateTestCases',
          params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
        },
        query: {
          redirectTo: 'TestRunEdit',
          runId: this.$route.params.id,
          folderUid: this.$route.params.folderUid,
          },
        });
      }else{
        this.$router.push({
          name: 'CreateTestCases',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key,
          },
          query: {
            redirectTo: 'TestRunCreate',
          },
        });
      }
    },
    handleCloseClick() {
      this.$emit('close')
    },
    handleConfirmClick() {
      this.showConfirmBackDialog = false;
      this.$router.replace({
        name: 'TestRunCreate',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key
        },
      });
    },
    changeFilter(filter) {
      this.tableFilter = filter;
    },
    updateCases(newCases) {
      this.cases = newCases;
    },
    // Public method to reset the component (can be called by parent via $refs)
    reset() {
      this.resetComponentState();
    },
    resetComponentState() {
      // Reset local component state
      this.selectedCases = [];
      this.casesCount = 0;
      this.drawer = false;
      this.selectedCase = null;
      this.tableFilter = false;
      
      // Reset composable state by resetting to first page
      if (this.composableCurrentPage) {
        this.composableCurrentPage = 1;
      }
      
      // Clear the persistent selected cases from the list composable
      tableSelectedCases.value = [];
      
      // Emit empty selected cases to parent  
      this.$emit('input', []);
      
      // Force reactivity update
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.search_input {
  width: 100%;

  @media screen and (min-width: 600px) {
    width: 300px;
  }
}

.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}

.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}
.action-btn-wrapper {
  position: sticky;
    bottom: 10px;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}
</style>
