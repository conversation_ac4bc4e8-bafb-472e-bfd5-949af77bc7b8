<template>
  <div class="pl-3 pt-3">
    <DefectHeader
      :open-defects="openDefects"
      :closed-defects="closedDefects"
      :active-state="activeState"
      :integrations="integrationsList"
      :selected-integration="selectedIntegration"
      @update-state="setActiveState"
      @integration-change="handleIntegrationChange"
    />
    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center"
        >
          <SearchComponent
            :search="filter.name"
            :placeholder="$t('placeHolder.searchByName')"
            class="mr-2"
            @update:search="filter.name = $event"
          />
          <DefectFilterDialog
            :data="filter"
            :priorities="priorities"
            :statuses="statuses"
            :filter-count="filterCount"
            :tags="systemTags"
            :selected-integration="selectedIntegration"
            @update-filter-condition="updateFilterCondition"
          />
        </v-col>

        <v-col
          cols="12"
          md="auto"
        >
          <v-row
            justify="end"
            class="align-center"
          >
            <DefectSettingsMenu
              class="mr-3"
              :table-type="getTableType()"
              :selected-integration="selectedIntegration"
              @table-type-change="handleTableTypeChange"
            />
          </v-row>
        </v-col>
      </v-row>
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center flex-wrap"
        >
          <div v-if="filterCount > 0">
            <span
              class="fw-bold font-weight-medium no-bg pr-2"
              width="300px"
            >
              {{ $t('results') }} ({{ filteredDefects.length }})
            </span>
          </div>
          <div
            v-for="priorityId in filter.priorities"
            :key="priorityId"
          >
            <v-chip
              class="chips-style ma-2"
              close
              @click:close="removePriority(priorityId)"
            >
              {{ $t('Priority') }}:{{ getPriorityName(priorityId, priorities) }}
            </v-chip>
          </div>
          <div
            v-for="statusId in filter.statuses"
            :key="statusId"
          >
            <v-chip
              class="chips-style ma-2"
              close
              @click:close="removeStatus(statusId)"
            >
              {{ $t('status') }}:{{ getStatusName(statusId, statuses) }}
            </v-chip>
          </div>
          <div
            v-if="filter.startDate || filter.endDate"
            class="d-flex align-center ml-2"
          >
            <v-chip
              class="chips-style ma-2 black--text theme--light"
              close
              @click:close="removeDate()"
            >
              {{ $t('lastUpdate') }}: {{ filter.startDate }} - {{ filter.endDate }}
            </v-chip>
          </div>
          <div
            v-for="tagId in filter.tags"
            :key="tagId"
          >
            <v-chip
              class="chips-style ma-2"
              close
              @click:close="removeTag(tagId)"
            >
              {{ $t('tag') }}: {{ getTagName(tagId) }}
            </v-chip>
          </div>
          <v-btn
            v-if="hasActiveFilters"
            class="blue--text fw-semibold font-weight-medium"
            width="100px"
            text
            elevation="0"
            style="text-transform: none; font-weight: 600 !important"
            @click="clearFilter()"
          >
            <div class="p-2">
              {{ $t('clearAll') }}
            </div>
          </v-btn>
        </v-col>
      </v-row>

      <DefectTable
        :headers="filteredHeaders"
        :items="paginatedDefects"
        item-key="uid"
        :priorities="priorities"
        :statuses="statuses"
        :integration-type="selectedIntegration"
        :active-state="activeState"
        :write-defect="_writeDefect"
        @view="onViewDefect"
        @edit="onEditDefect"
        @close="onCloseDefect"
        @reopen="onReopenDefect"
      />

      <ViewDefectDialog
        v-model="showDetailDialog"
        :data="selectedDefect"
        :priorities="priorities"
        :statuses="statuses"
        :selected-integration="selectedIntegration"
        @add-comment="onAddNewComment"
        @close-dialog="closeDetailView"
      />

      <EditDialog
        v-model="showEditDialog"
        :data="selectedDefect"
        :priorities="priorities"
        :statuses="statuses"
        :status-scopes="statusScopes"
        :tags="systemTags"
        :selected-integration="selectedIntegration"
        :loading="editLoading"
        :is-loading-status-scopes="isLoadingStatusScopes"
        @edit="editDefect"
        @close-dialog="closeEditDialog"
      />

      <Pagination
        v-if="totalItems > 0"
        :page="currentPage"
        :items-per-page="perPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        @update:pagination="onUpdatePagination"
      />
    </v-card>
  </div>
</template>

<script>
import { createNamespacedHelpers, mapGetters, mapActions } from 'vuex';
import * as _ from 'lodash';

import makeDefectService from '@/services/api/defect';
import makeTagService from '@/services/api/tag';
import makeIntegrationsService from '@/services/api/integrations';
import loaderMixin from '@/mixins/loader';
import DefectHeader from '@/components/Defect/Header';
import DefectFilterDialog from '@/components/Defect/FilterDialog.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import DefectSettingsMenu from '@/components/Defect/DefectSettingsMenu.vue';
import DefectTable from '@/components/Defect/Table.vue';
import ViewDefectDialog from '@/components/Defect/ViewDefectDialog.vue';
import EditDialog from '@/components/Defect/EditDialog.vue';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import dayjs from 'dayjs';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import Pagination from '@/components/base/Pagination.vue';
const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'Defects',

  components: {
    DefectHeader,
    SearchComponent,
    DefectSettingsMenu,
    DefectFilterDialog,
    DefectTable,
    ViewDefectDialog,
    EditDialog,
    Pagination,
  },

  mixins: [loaderMixin, colorPreferencesMixin],
  data() {
    return {
      filter: {
        name: '',
        priorities: [],
        statuses: [],
        startDate: null,
        endDate: null,
        tags: [],
      },
      originalDefects: [],
      filteredDefects: [],
      headers: [],
      defectService: null,
      showDetailDialog: false,
      showEditDialog: false,
      selectedDefect: {
        uid: '',
        id: '',
        name: '',
        priority: '',
        creator: '',
        status: '',
        updatedAt: null,
        comments: [],
        description: '',
        attachments: null,
      },
      totalItems: 0,
      page: 1,
      itemsPerPage: 10,
      priorities: [],
      statuses: [],
      editLoading: false,
      systemTags: [],
      statusScopes: [],
      isLoadingStatusScopes: false,
      closedDefects: 0,
      openDefects: 0,
      activeState: 'active',
      filterCount: 0,
      selectedTags: [],
      selectedIntegration: null,
      integrationsList: ['Jira', 'Github'],
      currentPage: 1,
      perPage: 10
    };
  },

  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    currentHeaders() {
      return this.dynamicHeaders[this.getTableType()] || [];
    },
    filteredHeaders() {
      return this.currentHeaders.filter((header) => header.checked);
    },
    _writeDefect() {
      return this.authorityTo('write_defect');
    },
    projectId() {
      return this.$route.params.key;
    },
    hasActiveFilters() {
      return (
        this.filter.priorities.length > 0 ||
        this.filter.statuses.length > 0 ||
        this.filter.startDate ||
        this.filter.endDate ||
        this.filter.tags.length > 0
      );
    },
    activeFiltersCount() {
      let count = 0;
      if (this.filter.priorities.length) count++;
      if (this.filter.statuses.length) count++;
      if (this.filter.startDate || this.filter.endDate) count++;
      if (this.filter.tags.length) count++;
      return count;
    },
    integrationService() {
      return this.$route.params.integration;
    },
    selectedDefectId() {
      return this.$route.params.defectId;
    },
    selectedIntegrationUid() {
      return this.originalDefects.length > 0
        ? this.originalDefects[0].integrationSourceUid
        : null;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.perPage);
    },
    paginatedDefects() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.filteredDefects.slice(start, end);
    }
  },

  watch: {
    'filter.name': {
      handler: _.debounce(function () {
        this.filterDefects();
      }, 500),
    },
    selectedIntegration: {
      handler(newIntegration, oldIntegration) {
        if (!newIntegration || newIntegration == oldIntegration) return;
        
        // Save the preference for this specific project
        this.saveProjectIntegrationPreference();
        
        this.clearAllFilters();
        
        const tableType = this.getTableType();
        if (!this.dynamicHeaders[tableType]) {
          this.initializeHeaders({ type: tableType });
        }
        
        this.$nextTick(() => {
          this.headers = [...this.dynamicHeaders[tableType]];
          this.updateTableHeaders();
        });

        if (!this.defectService) {
          this.defectService = makeDefectService(this.$api);
        }
        
        if (this.defectService) {
          this.init([
            this.getDefects(),
          ]);
          this.getDefectsOpenCount();
          this.getDefectsClosedCount();
        }
      },
    },
    integrationService: {
      handler(newIntegration) {
        if (!newIntegration) return;
        
        const formattedIntegration = newIntegration.charAt(0).toUpperCase() + newIntegration.slice(1).toLowerCase();
        
        if (this.selectedIntegration !== formattedIntegration) {
          this.selectedIntegration = formattedIntegration;
        }
      },
    },
    selectedDefectId: {
      handler(newDefectId) {
        if (!newDefectId) return;
        if (this.selectedDefect.uid === '' || this.selectedDefect.uid !== newDefectId) {
          this.selectedDefect = this.originalDefects.find(
            (defect) => Number(defect.uid) === Number(this.selectedDefectId)
          );
        }
        this.showDetailDialog = true;
      },
    },
  },

  async created() {
    // Check for integration in URL params first
    if (this.integrationService) {
      this.selectedIntegration = this.integrationService.charAt(0).toUpperCase() + this.integrationService.slice(1).toLowerCase();
      this.saveProjectIntegrationPreference();
    } else {
      // Check for saved project-specific preference
      const savedProjectIntegration = this.$store.getters['defects/getProjectIntegrationPreference'](
        this.currentAccount.handle, 
        this.projectId
      );
      
      if (savedProjectIntegration) {
        this.selectedIntegration = savedProjectIntegration;
      } else {
        await this.checkAndSetDefaultIntegration();
      }
    }
    
    this.initializeHeaders({ type: this.getTableType() });
    this.updateTableHeaders();
  },

  async mounted() {
    this.defectService = makeDefectService(this.$api);
    
    // Ensure we have a selected integration before fetching data
    if (!this.selectedIntegration) {
      await this.checkAndSetDefaultIntegration();
    }
    
    await this.init([
      this.getDefects(), 
      this.getDefectPriorities(), 
      this.getDefectStatuses(), 
      this.getDefectTags()
    ]);
    this.getDefectsOpenCount()
    this.getDefectsClosedCount()
  },

  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    async getDefectsOpenCount() {
      try {
        const response = await this.defectService.getDefectsOpenCount(this.currentAccount.handle, this.projectId,this.selectedIntegration.toLowerCase());
        this.openDefects = Number(response.data.count);
        return Number(response.data.count);
      } catch (err) {
        showErrorToast(this.$swal, this.$t('defect.fetchError'), {}, err?.response?.data);
        return 0;
      }
    },
    async getDefectsClosedCount() {
      try {
        const response = await this.defectService.getDefectsClosedCount(this.currentAccount.handle, this.projectId,this.selectedIntegration.toLowerCase());
        this.closedDefects = Number(response.data.count);
        return Number(response.data.count);
      } catch (err) {
        showErrorToast(this.$swal, this.$t('defect.fetchError'), {}, err?.response?.data);
        return 0;
      }
    },
    async getDefects() {
      try {
        const response = await this.defectService.getDefects(this.currentAccount.handle, this.projectId, {
          page: 1,
          limit: 9999,
          integration: this.selectedIntegration,
        });
        this.originalDefects = response.data.data
          .map((defect) => ({
            uid: defect.uid,
            id: defect.customFields?.key || defect.customFields?.number,
            name: defect.name,
            priority: defect.priority,
            creator: defect.creator || defect.customFields?.creator?.displayName,
            status: defect.status,
            originalUpdatedAt: defect.updatedAt,
            updatedAt: dayjs(defect.updatedAt).format('DD/MM/YYYY'),
            issueType: defect.customFields?.issuetype?.name,
            comments: defect.customFields?.comments || [],
            description: defect.customFields?.description,
            integration: defect.integration || {},
            integrationSourceUid: defect.integrationSourceUid,
            linkedRuns: defect.linkedRuns || [],
            linkedExecutions: defect.linkedExecutions || [],
            externalId: defect.customFields?.key || defect.customFields?.number,
            projectName: defect.customFields?.project?.name || defect.customFields?.projectScope,
            archivedAt: defect.archivedAt,
            state: defect.customFields?.state,
            webUrl: defect.customFields?.webUrl,
            attachments: defect.attachments || [],
            repository: defect.customFields?.projectScope,
            customFields: {
              ...defect.customFields,
              tags:
                defect.customFields?.tags
                  ?.map((tagId) => {
                    const tag = this.systemTags.find((t) => Number(t.uid) === Number(tagId));
                    return tag
                      ? {
                          id: tag.uid,
                          name: tag.name,
                          color: tag.customFields?.color,
                        }
                      : null;
                  })
                  .filter(Boolean) || [],
            },
            projectUid: defect.projectUid,
            integrationStatus: defect.integrationStatus,
          }))
          .sort((a, b) => new Date(b.originalUpdatedAt) - new Date(a.originalUpdatedAt));

        this.totalItems = this.originalDefects.length;
        this.filterDefects();
        if (this.selectedDefectId && this.selectedDefectId !== this.selectedDefect.uid) {
          this.selectedDefect = this.originalDefects.find(
            (defect) => Number(defect.uid) === Number(this.selectedDefectId)
          );
          this.showDetailDialog = true;
        }
        showSuccessToast(this.$swal, this.$t('defect.fetchSuccess'));
      } catch (err) {
        console.error('Error in getDefects', err);
        this.errorMessage = err.message;
        showErrorToast(this.$swal, this.$t('defect.fetchError'), {}, err?.response?.data);
      }
    },

    async refreshDefects() {
      await this.getDefects();
    },
    removePriority(priorityId) {
      this.filter.priorities = this.filter.priorities.filter((p) => p !== priorityId);
      this.filterDefects();
    },
    removeStatus(statusId) {
      this.filter.statuses = this.filter.statuses.filter((s) => s !== statusId);
      this.filterDefects();
    },
    removeDate() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      this.filterDefects();
    },
    countFilter() {
      this.filterCount =
        this.filter.priorities.length +
        this.filter.statuses.length +
        (this.filter.startDate || this.filter.endDate ? 1 : 0) +
        this.filter.tags.length 
    },
    clearFilter() {
      this.filter = {
        priorities: [],
        statuses: [],
        startDate: null,
        endDate: null,
        tags: [],
      };
      this.filterDefects();
    },
    onViewDefect(item) {
      if (this.showDetailDialog || this.showEditDialog) {
        return;
      }
      this.selectedDefect = {
        uid: item.uid,
        id: item.id,
        name: item.name,
        priority: item.priority,
        creator: item.creator,
        status: item.status,
        updatedAt: item.updatedAt,
        comments: item.comments,
        description: item.description,
        linkedRuns: item.linkedRuns,
        linkedExecutions: item.linkedExecutions,
        customFields: item.customFields,
        projectUid: item.projectUid,
        externalId: item.externalId,
        projectName: item.projectName,
        issueType: item.issueType,
        state: item.state,
        webUrl: item.webUrl,
        attachments: item.attachments,
        archivedAt: item.archivedAt,
      };
      this.$router.push({
        name: 'DefectDetail',
        params: {
          defectId: item.uid,
          integration: this.selectedIntegration,
        },
      });
    },

    async onAddNewComment(comment) {
      try {
        const response = await this.defectService.addComment(this.currentAccount.handle, this.projectId, {
          uid: this.selectedDefect.uid,
          comment,
        });
        this.originalDefects = this.originalDefects.map((item) => {
          if (item.uid !== this.selectedDefect.uid) {
            return item;
          }

          item.comments.splice(0, 0, response.data);
          this.selectedDefect.comments.splice(0, 0, response.data);

          return item;
        });
      } catch (err) {
        this.errorMessage = err.message;
      }

      this.filterDefects();
    },

    async getDefectStatusScopes(defectUid) {
      try {
        this.isLoadingStatusScopes = true;
        const response = await this.defectService.getDefectStatusScopes(this.currentAccount.handle, this.projectId, defectUid);
        this.statusScopes = response.data;
      } catch (err) {
        console.error('Error fetching status scopes:', err);
        showErrorToast(this.$swal, this.$t('defect.fetchStatusScopesError'), {}, err?.response?.data);
      } finally {
        this.isLoadingStatusScopes = false;
      }
    },

    onEditDefect(item) {
      this.selectedDefect = {
        uid: item.uid || '',
        id: item.id || '',
        name: item.name || '',
        priority: item.priority || '',
        creator: item.creator || '',
        status: item.status || '',
        updatedAt: item.updatedAt || null,
        comments: item.comments || [],
        customFields: item.customFields || {},
        state: item.state,
        description: item.description,
        attachments: item.attachments,
        archivedAt: item.archivedAt,
      };

      this.getDefectStatusScopes(item.uid);
      this.showEditDialog = true;
    },
    onCloseDefect(item) {
      const payload = {
        uid: item.uid,
        name: item.name,
        state: 'closed',
        customFields: {
          tags: item.customFields.tags.map(({ id, ...rest }) => ({
            uid: id, // Change 'id' to 'uid'
            ...rest,
          })),
        },
      };
      this.editDefect(payload);
    },
    onReopenDefect(item) {
      const payload = {
        uid: item.uid,
        name: item.name,
        state: 'open',
        customFields: {
          tags: item.customFields.tags.map(({ id, ...rest }) => ({
            uid: id,
            ...rest,
          })),
        },
      };
      this.editDefect(payload);
    },

    async editDefect(item) {
      try {
        this.editLoading = true;
        await this.defectService.updateDefect(this.currentAccount.handle, this.projectId, item);

        // Wait for the getDefects to complete before filtering
        await this.getDefects();
        showSuccessToast(this.$swal, this.$t('defect.success'));
        this.showEditDialog = false;
        this.getDefectsClosedCount();
        this.getDefectsOpenCount();
      } catch (err) {
        this.errorMessage = err.message;
        showErrorToast(this.$swal, this.$t('defect.error'), {}, err?.response?.data);
      } finally {
        this.editLoading = false;
      }
    },

    updateFilterCondition(data) {
      this.filter = {
        ...this.filter,
        priorities: data.priorities,
        statuses: data.statuses,
        startDate: data.startDate,
        endDate: data.endDate,
        tags: data.tags,
      };

      this.filterDefects();
    },

    filterDefects() {
      let filteredDefects = _.cloneDeep(this.originalDefects);
      this.countFilter();
      filteredDefects = filteredDefects.filter((item) => {
        const isClosedStatus = item.archivedAt !== null;
        return this.activeState === 'closed' ? isClosedStatus : !isClosedStatus;
      });

      if (this.filter.name) {
        filteredDefects = filteredDefects.filter(
          (item) => item.name && item.name.toLowerCase().includes(this.filter.name.toLowerCase())
        );
      }

      if (this.filter.priorities.length > 0) {
        filteredDefects = filteredDefects.filter((item) => this.filter.priorities.includes(Number(item.priority)));
      }

      if (this.filter.statuses.length > 0) {
        filteredDefects = filteredDefects.filter((item) => this.filter.statuses.includes(Number(item.status)));
      }

      if (this.filter.startDate) {
        // Set start date to beginning of day (00:00:00)
        const startDate = new Date(this.filter.startDate);
        startDate.setHours(0, 0, 0, 0);
        filteredDefects = filteredDefects.filter(
          (item) => new Date(item.originalUpdatedAt).getTime() >= startDate.getTime()
        );
      }

      if (this.filter.endDate) {
        // Set end date to end of day (23:59:59.999)
        const endDate = new Date(this.filter.endDate);
        endDate.setHours(23, 59, 59, 999);
        filteredDefects = filteredDefects.filter(
          (item) => new Date(item.originalUpdatedAt).getTime() <= endDate.getTime()
        );
      }

      if (this.filter.tags.length > 0) {
        filteredDefects = filteredDefects.filter((item) =>
          this.filter.tags.some((filterTagId) => item.customFields?.tags?.some((tag) => tag.id === filterTagId))
        );
      }

      this.filteredDefects = filteredDefects;
      this.totalItems = filteredDefects.length;
      // Reset to first page when filtering
      this.currentPage = 1;
    },
    async getDefectPriorities() {
      try {
        const response = await this.defectService.getDefectPriorities(this.currentAccount.handle, this.projectId);
        this.priorities = response.data;
      } catch (err) {
        console.error('Error fetching priorities:', err);
        showErrorToast(this.$swal, this.$t('defect.fetchPrioritiesError'), {}, err?.response?.data);
      }
    },

    async getDefectStatuses() {
      try {
        const response = await this.defectService.getDefectStatuses(this.currentAccount.handle, this.projectId);
        this.statuses = response.data;
      } catch (err) {
        console.error('Error fetching statuses:', err);
        showErrorToast(this.$swal, this.$t('defect.fetchStatusesError'), {}, err?.response?.data);
      }
    },

    async getDefectTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.currentAccount.handle, 'defects');
        this.systemTags = response.data;
      } catch (err) {
        console.error('Error fetching defect tags:', err);
        showErrorToast(this.$swal, this.$t('defect.fetchTagsError'), {}, err?.response?.data);
      }
    },

    getTagInfo(tagId) {
      const tag = this.systemTags.find((t) => t.uid === tagId);
      return tag
        ? {
            id: tag.uid,
            name: tag.name,
            color: tag.customFields?.color,
          }
        : null;
    },

    setActiveState(state) {
      this.activeState = state;
      this.updateTableHeaders();
      this.filterDefects();
    },

    removePriorityFilter(priority) {
      this.filter.priorities = this.filter.priorities.filter((p) => p !== priority);
      this.filterDefects();
    },

    removeStatusFilter(status) {
      this.filter.statuses = this.filter.statuses.filter((s) => s !== status);
      this.filterDefects();
    },

    removeDateFilter() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      this.filterDefects();
    },

    clearAllFilters() {
      this.filter = {
        name: '',
        priorities: [],
        statuses: [],
        startDate: null,
        endDate: null,
        tags: [],
      };
      this.filterDefects();
    },

    getPriorityName(priorityId) {
      const priority = this.priorities.find((p) => p.id === priorityId);
      return priority?.name || priorityId;
    },

    getStatusName(statusId) {
      const status = this.statuses.find((s) => s.id === statusId);
      return status?.name || statusId;
    },

    getTagName(tagId) {
      const tag = this.systemTags.find((t) => t.uid === tagId);
      return tag?.name || tagId;
    },

    removeTagFilter(tagId) {
      this.filter.tags = this.filter.tags.filter((t) => t !== tagId);
      this.filterDefects();
    },

    removeTag(tagId) {
      this.filter.tags = this.filter.tags.filter((t) => t !== tagId);
      this.filterDefects();
    },

    handleIntegrationChange(integration) {
      if (this.selectedIntegration !== integration) {
        this.selectedIntegration = integration;
      }
    },

    getTableType() {
      const headerTypeMap = {
        Jira: 'projectsJiraDefect',
        Github: 'projectsGithubDefect',
      };
      return headerTypeMap[this.selectedIntegration] || 'projectsJiraDefect';
    },

    handleTableTypeChange(newType) {
      if (!this.dynamicHeaders[newType]) {
        this.initializeHeaders({ type: newType });
      }
      this.$nextTick(() => {
        this.headers = [...this.dynamicHeaders[newType]];
      });
    },

    closeDetailView() {
      this.$router.push({
        name: 'Defects',
        params: {
          handle: this.currentAccount.handle,
          key: this.projectId,
        },
      });
      this.showDetailDialog = false;
    },

    updateTableHeaders() {
      const tableType = this.getTableType();
      if (!this.dynamicHeaders[tableType]) {
        this.initializeHeaders({ type: tableType });
      }
      const updatedHeaders = this.dynamicHeaders[tableType].map((header) => {
        if (header.value === 'updatedAt' || header.value === 'closedAt') {
          return {
            ...header,
            text: this.activeState === 'closed' ? this.$t('closedAt') : this.$t('lastUpdate'),
          };
        }
        return header;
      });
      this.$store.dispatch('headers/updateHeaders', { type: tableType, headers: updatedHeaders });
      this.headers = [...updatedHeaders];
    },

    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
      }
    },
    
    closeEditDialog() {
      this.showEditDialog = false;
      this.statusScopes = [];
      this.isLoadingStatusScopes = false;
    },

    saveProjectIntegrationPreference() {
      this.$store.dispatch('defects/setProjectIntegrationPreference', {
        handle: this.currentAccount.handle,
        projectKey: this.projectId,
        integration: this.selectedIntegration
      });
    },

    async checkAndSetDefaultIntegration() {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        
        const params = `service=jira,github`;
        const response = await integrationsService.getIntegrations(this.currentAccount.handle, params);
        
        if (!response?.data) {
          this.selectedIntegration = 'Jira';
          this.saveProjectIntegrationPreference();
          return;
        }
        
        if (response?.data?.integrations && Array.isArray(response.data.integrations)) {
          const foundEntry = Object.entries(response.data.projects || {}).find(
            ([, project]) => project.key === this.projectId
          );
          
          if (foundEntry) {
            const testfiestaProjectUid = foundEntry[0];
            
            const projectIntegrations = response.data.integrations.filter(integration => 
              integration.configuration?.projectConfigurations?.some(config => 
                config.projects && config.projects[testfiestaProjectUid]
              )
            );
            
            if (projectIntegrations.length > 0) {
              const hasJira = projectIntegrations.some(i => i.service === 'jira');
              const hasGithub = projectIntegrations.some(i => i.service === 'github');

              if (hasJira && hasGithub) {
                this.selectedIntegration = 'Jira';
              } else if (hasJira) {
                this.selectedIntegration = 'Jira';
              } else if (hasGithub) {
                this.selectedIntegration = 'Github';
              }
            } else {
              this.selectedIntegration = 'Jira';
            }
            this.saveProjectIntegrationPreference();
            return;
          }
          else {
            this.selectedIntegration = 'Jira';
            this.saveProjectIntegrationPreference();
            return;
          }
        }
      } catch (error) {
        console.error('Error fetching integrations for default selection:', error);
        this.selectedIntegration = 'Jira';
        this.saveProjectIntegrationPreference();
      }
    },
  },
};
</script>

<style scoped>
.chips-style {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
}
</style>
