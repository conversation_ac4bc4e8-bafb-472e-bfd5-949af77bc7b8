<template>
  <v-container
    fluid
    style="padding: 0"
  >
    <v-card
      class="white py-6 px-6 mt-3 project-detail-height"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex align-center">
        <v-icon
          color="blue"
          @click="back"
        >
          mdi-chevron-left
        </v-icon>
        <v-btn
          text
          color="blue"
          class="font-weight-bold text-capitalize"
          @click="back"
        >
          {{
            $t('projects.create_project.back_to_projects')
          }}
        </v-btn>
      </div>
      <div class="d-flex align-center justify-space-between text-start mt-7">
        <div class="d-flex align-center">
          <div style="position: relative; display: inline-block;">
            <Avatar
              v-if="!skeletonLoaderState"
              :avatar="{ user: imageSrc ?? selectedProject?.avatarUrl }"
              :avatar-src="defaultImage"
              :size="85"
            />
            <v-skeleton-loader
              v-else
              height="85"
              width="85"
              type="avatar"
            />
            <v-btn 
              v-if="!skeletonLoaderState"
              icon
            >
              <upload-avatar
                emit-file
                profile-image="project"
                media-type="attachment"
                @croppedFile="handleFileChange"
              />
            </v-btn> 
          </div>
          <template v-if="!skeletonLoaderState">
            <template v-if="!editTitle">
              <div
                class="ml-4"
                @mouseenter="visibleTitle = true"
                @mouseleave="visibleTitle = false"
              >
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  {{ selectedProject.name }}
                  <v-btn
                    icon
                    elevation="0"
                    class=""
                    @click="handleClickEditTag"
                  >
                    <PencilIcon
                      class=""
                    />
                  </v-btn>
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular">
                  {{ selectedProject?.customFields?.description }}
                </div>
              </div>
            </template>
            <template v-else>
              <div class="ml-4">
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  <v-text-field
                    v-model="projectTitle"
                    dense
                    class="font-weight-bold fs-24px text-theme-base edit-title-input"
                    hide-details
                  />
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular edit-subtitle-input">
                  <v-text-field
                    v-model="projectSubTitle"
                    dense
                    height="16px"
                    class="fs-14px text-theme-secondary font-weight-regular"
                    hide-details
                  />
                </div>
              </div>
            </template>
          </template>
          <template v-else>
            <div class="d-flex flex-column ml-3">
              <v-skeleton-loader
                width="200"
                height="36"
                class="mb-2"
                type="text"
              />
              <v-skeleton-loader
                width="200"
                height="24"
                type="text"
              />
            </div>
          </template>
        </div>
        <div
          v-if="!skeletonLoaderState"
          class="d-flex align-center"
        >
          <div class="fs-14px text-theme-secondary font-weight-regular mr-2">
            {{ $t('projectKey') + ':' }} 
          </div>
          <span class="fs-16px fw-semibold">
            {{ selectedProject.key }}
          </span>
        </div>
        <v-skeleton-loader
          v-else
          width="200"
          height="24"
          type="text"
        />
      </div>
      <v-card
        class="mt-3"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div
          class="d-flex align-center justify-space-between"
        >
          <div
            class="d-flex align-center"
          >
            <SearchComponent
              class="mr-3"
              @update:search="setSearchFilter"
            />
            <ProjectUserFilter 
              :available-roles="roles"
              :available-tags="tags"
              :available-projects="projectsData"
              @filters="handleUserFilter"
            />
          </div>
          <div
            class="d-flex align-center justify-end"
          >
            <SettingsMenu 
              table-type="projectDetails" 
            />
          </div>
        </div>
        <ProjectViewUserTable
          :filtered-headers="filteredHeaders"
          :items="activeMembersHasData ? filteredItem : []"
          :item-key="itemKey"
          :tags="tags"
          :roles="roles"
          :projects="projectsData"
        />
      </v-card>
      <div
        v-if="!skeletonLoaderState"
        class="d-flex justify-end btn-table-action"
      >
        <v-btn
          color="gray-100"
          width="150px"
          class="mr-4 text-capitalize"
          elevation="0"
          @click="onProjectCancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          color="blue"
          dark
          width="150px"
          elevation="0"
          class="text-capitalize"
          @click="onProjectSave"
        >
          {{ $t('save') }}
        </v-btn>
      </div>
    </v-card>
    <v-dialog
      v-model="drawer"
      fullscreen
    >
      <v-card class="pa-4">
        <v-container>
          <v-row>
            <v-col
              cols="12"
              class="d-flex justify-space-between align-center"
            >
              <h5 class="font-weight-bold">
                {{ !title ? $t('tagCreationHeader') : $t('tagEditHeader') }}
              </h5>
              <v-btn
                icon
                @click="drawer = false"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="title"
                :label="$t('titleLabel')"
                filled
                :placeholder="$t('enterTitle')"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="description"
                :label="$t('descriptionLabel')"
                filled
                :placeholder="$t('enterDescription')"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="type"
                :label="$t('typeLabel')"
                filled
                :placeholder="$t('enterType')"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="12"
              md="6"
            >
              <v-btn
                color="gray-100"
                full-width
                class="w-full text-capitalize"
                elevation="0"
                @click="drawer = false"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              md="6"
            >
              <v-btn
                color="primary"
                class="w-full text-capitalize"
                depressed
                @click="onTagSave"
              >
                {{ $t('save') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { users } from '@/constants/data.js';
import { createNamespacedHelpers, mapGetters, mapActions as projectMapActions } from 'vuex';
import ProjectViewUserTable from '@/components/Project/ProjectViewUserTable.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import ProjectUserFilter from '@/components/Project/ProjectUserFilter.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import makeTagService from '@/services/api/tag';
import makeProjectService from '@/services/api/project';
import makeRoleService from '@/services/api/role';
import { showErrorToast, showSuccessToast} from '@/utils/toast';
import PencilIcon from '@/assets/svg/pencil.svg';
import { projectImageTypes } from '@/constants/fileTypes.js';
import fileValidator from '@/mixins/fileValidator.js';
import handleLoading from '@/mixins/loader.js'
import UploadAvatar from '@/components/Profile/UploadAvatar.vue';
import Avatar from "@/components/base/Avatar.vue"
import { useProjectsManagement } from '@/composables/modules/project/view';

const { mapState: mapProjectState, mapActions } = createNamespacedHelpers('project');
const { mapState: mapUserState } = createNamespacedHelpers('user');

export default {
  name: 'ProjectDetailView',
  components: {
    ProjectViewUserTable,
    SearchComponent,
    ProjectUserFilter,
    SettingsMenu,
    PencilIcon,
    UploadAvatar,
    Avatar
  },
  mixins: [ fileValidator, handleLoading ],
  setup() {
    const { clearCache } = useProjectsManagement();
    return {
      clearCache
    };
  },
  data() {
    return {
      projectImageTypes,
      uploadAvatar: false,
      file: null,
      imageSrc: null,
      placeholder: 'Search by name, email, etc',
      selectedRoles: [],
      selectedTags: [],
      selectedProjects: [],
      search: '',
      items: [],
      itemKey: 'uid',
      users: users,
      options: ['all', 'selected'],
      project: null,
      headers: [],
      tableClass: '',
      checkbox: false,
      drawer: false,
      title: '',
      description: '',
      type: '',
      visibleTitle: false,
      editTitle: false,
      projectTitle: '',
      projectSubTitle: '',
      selectedProject: {},
      searchFilter: '',
      filter: 'active',
      activeUserCount: 0,
      activeMembers: [],
      tags: [],
      projectsData: [],
      roles: [],
      handle: this.$route.params.handle,
      defaultImage: new URL('@/assets/png/project.png', import.meta.url).href,
    };
  },
  async mounted() {
    await this.init(this.handle);
  },
  async created() {
    if(!this.dynamicHeaders.projectDetails) {
      this.initializeHeaders({ type: 'projectDetails' });
    }
    this.headers = this.dynamicHeaders.projectDetails;
  
  },
  methods: {
    ...mapActions(['get', 'update']),
    ...projectMapActions({
      initializeHeaders: 'headers/initializeHeaders',
      uploadToServer: 'attachment/uploadToServer',
    }),
    back() {
      this.$router.push({
        name: 'ProjectsView'
      });
    },
    handleFileChange(file) {
      if(projectImageTypes.includes(file.type))
        this.previewImage(file)
    },
    async previewImage(file) {
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imageSrc = e.target.result;
        };
        reader.readAsDataURL(file);
        this.file = file;

        if(this.selectedProject.uid && this.file){
            const handle = this.$route.params.handle;
            const mediaType = 'attachment'
            const apiService = makeProjectService(this.$api);
            const params = {
              handle,
              projectKey: this.$route.params.key
            }
            try {
              await this.uploadToServer({handle, mediaType, file: this.file, apiService, params})
              showSuccessToast(this.$swal, this.$t('profileUpdated'));
            } catch (err) {
              if (err?.status == 507)
                showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
            }
        }
      }
    },
    async getMembers(projectKey) {
      const projectService = makeProjectService(this.$api);
      const handle = this.$route.params.handle;
      try {
        const response = await projectService.getUsers({
          handle,
          projectKey,
        });
        this.activeMembers = response?.data?.users
        if (this.activeMembers?.length > 0) {
          this.activeUserCount = this.activeMembers.length;
        }
      } catch (err) {
        console.log(err)
      }
    },
    all() {
      this.filter = 'all';
    },
    select() {
      this.filter = 'selected';
    },
    handleUserFilter(filter) {
      this.selectedRoles = filter.roles.map(item => item.name);
      this.selectedTags = filter.tags.map(item => item.uid);
      this.selectedProjects = filter.projects.map(item => item.name);

    },
   
    setSearchFilter(searchText) {
      this.searchFilter = searchText;
    },
    handleUpdateRole(role, userID) {
      let _users = [...this.users];
      this.users = _users.map((user) => {
        if (user.id === userID) {
          return {
            ...user,
            role,
          };
        } else return user;
      });
    },
    handleClickEditTag() {
      
      this.editTitle = true;
      this.projectSubTitle = this.selectedProject.customFields.description;
      this.projectTitle = this.selectedProject.name;
    },
    handleCreateTag() {
      this.title = '';
      this.description = '';
      this.type = '';
      this.drawer = !this.drawer;
    },
    handleEditTag(tag) {
      this.title = tag;
      this.description = '';
      this.type = '';
      this.drawer = !this.drawer;
    },
    clearAll() {
      this.selectedRoles = [];
      this.selectedTags = [];
    },
    onProjectCancel() {
      this.users.forEach((user) => {
        user.selected = false;
      });
      this.$router.go(-1);
    },
    async onProjectSave() {
      const payload = {
        customFields: {
          ...this.selectedProject.customFields,
          description: this.projectSubTitle,
        },
        name: this.projectTitle,
      };
      
     await this.update({
        swal: this.$swal,
        handle: this.$route.params.handle,
        oldProject: this.selectedProject,
        payload,
      });
      
      // Clear project cache after successful update
      this.clearCache();
      
      this.$router.go(-1)

    },
    onTagSave() { }, // TODO
    selectItem(item) {
      item.selected = true;
    },
    async init(handle) { 
      try {
        this.showSkeletonLoader();
        await Promise.all([this.getTags(handle), this.getRoles(handle), this.getProjects({}, handle)]);
      } catch (error) {
        console.error("Error loading data:", error);
      } finally {
        this.loading = false;
        this.hideSkeletonLoader();
      }
      if(this._readMember)
        await this.getMembers(this.$route.params.key);
    },
    async getTags() {
      const handle = this.$route.params.handle;
            const tagService = makeTagService(this.$api);
            try {
                const response = await tagService.getTags(handle);
                this.tags = response.data
            } catch (err) {
              showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
              return [];
            }
    }, 
    async getProjects(additionalParams = {}, handle) {
      if (!handle) handle = this.handle;
      const searchParams = new URLSearchParams();
      const projectService = makeProjectService(this.$api);
      searchParams.set('status', this.filter);
      searchParams.set('includeCount', true);
    
      Object.keys(additionalParams).forEach(key => {
        searchParams.set(key, additionalParams[key]);
      });

      this.loading = true;
      try {
        const response = await projectService.getProjects(handle, searchParams.toString());
        this.projectsData = response.data.items;
        this.selectedProject = response.data.items.find((project) => project.key === this.$route.params.key);
        this.projectTitle = this.selectedProject?.name;
        this.projectSubTitle = this.selectedProject?.customFields?.description;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
        return [];
      } 
    },
    async getRoles(handle) {
      const roleService = makeRoleService(this.$api);
      try {
        const projectKey = this.$route.params.key;
        const response = await roleService.getRoles(handle, projectKey);
        this.roles = response.data?.items;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, err?.response?.data);
      }

    },
  matchesFilter(item) {
  const lowerCaseFilter = this.searchFilter?.toLowerCase();
  
  return [item?.firstName, item?.lastName, item?.email]
    .some(field => field?.toLowerCase().includes(lowerCaseFilter));
}
  },
  computed: {
    ...mapProjectState(['projects']),
    ...mapUserState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders'
    }),
    paramID() {
      return this.$route.params.id;
    },
    customCheckboxClass() {
      return {
        'custom-checkbox': true,
      };
    },
    activeMembersHasData() {
      return this.activeMembers?.length > 0;
    },
    allCount() {
      return this.users.length;
    },
    selectedCount() {
      return this.users.filter((item) => item.selected === true).length;
    },
    _readMember() {
      return this.authorityTo('read_member');
    },
    filteredItem() {
  let filtered =
    this.filter === 'active'
      ? this.activeMembers
      : this.filter === 'pending'
        ? this.pendingInvites
        : this.requestedUsers;

  if (this.searchFilter.length > 0) filtered = filtered.filter((item) => this.matchesFilter(item));
  
  if (this.selectedRoles.length > 0) filtered = filtered.filter(user => user.role && this.selectedRoles.includes(user.role.name));
    
  if (this.selectedTags.length > 0) filtered = filtered.filter(user => user.tags?.some(tag => this.selectedTags.includes(tag.uid)));


  return filtered;
  },

    filteredUsers() {
      let filtered = this.users;

      if (this.filter === 'all') {
        filtered = this.users;
      } else if (this.filter === 'selected') {
        filtered = filtered.filter((item) => item.selected === true);
      }

      if (this.search) {
        const searchTerm = this.search.toLowerCase();
        filtered = filtered.filter((item) => {
          return item.name.toLowerCase().includes(searchTerm);
        });
      }

      if (this.selectedTags.length > 0) {
        filtered = filtered.filter((user) => {
          return this.selectedTags.every((tag) => user.tag.includes(tag));
        });
      }

      if (this.selectedRoles.length > 0) {
        filtered = filtered.filter((user) => {
          return this.selectedRoles.every((role) => user.role.includes(role));
        });
      }

      return filtered;
    },
    filteredHeaders() {
      const filtered = this.filteredMenuHeaders.filter((header) => header.checked);
      return filtered;
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.text != 'Actions');
      return filtered;
    },
  },
  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      next();
    }
  },
};
</script>

<style scoped>
.project-detail-height {
    min-height: calc(100vh - 24px - 6.25rem);
    height: 100%;
}
.custom-checkbox .v-input--selection-controls .v-input--checkbox .v-input__control .v-icon {
  border: none !important;
}

.custom-checkbox input[type='checkbox'] {
  border-radius: 20px;
  width: 16px;
  height: 16px;
}

.no-padding {
  padding: 0 !important;
  margin: 0 !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}

.v-expansion-panel-head__wrap {
  padding: 0 !important;
}

.custom-div {
  height: 15px;
  width: 15px;
  background-color: #eaecf0;
  border-radius: 4px;
}
.btn-table-action {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 1rem 1.5rem;
}
</style>
