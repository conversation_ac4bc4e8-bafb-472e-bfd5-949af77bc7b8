<template>
  <v-container
    fluid
    style="padding: 0"
  >
    <v-card
      class="white py-6 px-6 mt-3 project-detail-height"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex align-center">
        <v-icon
          color="blue"
          @click="back"
        >
          mdi-chevron-left
        </v-icon>
        <v-btn
          text
          color="blue"
          class="font-weight-bold text-capitalize"
          @click="back"
        >
          {{
            $t('projects.create_project.back_to_projects')
          }}
        </v-btn>
      </div>
      <div class="d-flex align-center justify-space-between text-start mt-7">
        <div class="d-flex align-center">
          <div style="position: relative; display: inline-block;">
            <Avatar
              v-if="!loader.project"
              :avatar="{ user: project?.logo }"
              :avatar-src="defaultImage"
              :size="85"
            />
            <v-skeleton-loader
              v-else
              height="85"
              width="85"
              type="avatar"
            />
            <v-btn 
              v-if="!loader.project"
              icon
            >
              <upload-avatar
                emit-file
                profile-image="project"
                media-type="attachment"
                @croppedFile="handleAvatarCropped"
              />
            </v-btn> 
          </div>
          <template v-if="!loader.project">
            <template v-if="edit.name === null">
              <div class="ml-4">
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  {{ project.name }}
                  <v-btn
                    icon
                    elevation="0"
                    class=""
                    @click="updateProjectField('name', project.name)"
                  >
                    <PencilIcon />
                  </v-btn>
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular">
                  {{ project.description }}
                </div>
              </div>
            </template>
            <template v-else>
              <div class="ml-4">
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  <v-text-field
                    v-model="edit.name"
                    dense
                    class="font-weight-bold fs-24px text-theme-base edit-title-input"
                    hide-details
                    @blur="updateProjectField('name', edit.name)"
                  />
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular edit-subtitle-input">
                  <v-text-field
                    v-model="edit.description"
                    dense
                    height="16px"
                    class="fs-14px text-theme-secondary font-weight-regular"
                    hide-details
                    @blur="updateProjectField('description', edit.description)"
                  />
                </div>
              </div>
            </template>
          </template>
          <template v-else>
            <div class="d-flex flex-column ml-3">
              <v-skeleton-loader
                width="200"
                height="36"
                class="mb-2"
                type="text"
              />
              <v-skeleton-loader
                width="200"
                height="24"
                type="text"
              />
            </div>
          </template>
        </div>
        <div
          v-if="!loader.project"
          class="d-flex align-center"
        >
          <div class="fs-14px text-theme-secondary font-weight-regular mr-2">
            {{ $t('projectKey') + ':' }} 
          </div>
          <span class="fs-16px fw-semibold">
            {{ project.key }}
          </span>
        </div>
        <v-skeleton-loader
          v-else
          width="200"
          height="24"
          type="text"
        />
      </div>
      <v-card
        class="mt-3"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div
          class="d-flex align-center justify-space-between"
        >
          <div
            class="d-flex align-center"
          >
            <SearchComponent
              v-model="searchTerm"
              class="mr-3"
              @reset="resetSearchTerm"
            />
            <ProjectUserFilter 
              :selected-filters="selectedFilters"
              @update:selected-filters="updateSelectedFilters"
              @delete:selected-filter="deleteSelectedFilters"
              @reset="resetFilter"
            />
          </div>
          <div
            class="d-flex align-center justify-end"
          >
            <SettingsMenu 
              table-type="projectDetails" 
            />
          </div>
        </div>
        <ProjectViewUserTable
          :items="filteredUsers"
        />
      </v-card>
      <div
        v-if="!loader.project"
        class="d-flex justify-end btn-table-action"
      >
        <v-btn
          color="gray-100"
          width="150px"
          class="mr-4 text-capitalize"
          elevation="0"
          @click="$router.push({ name: 'ProjectsView' })"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          color="blue"
          dark
          width="150px"
          elevation="0"
          class="text-capitalize"
          @click="onSubmit"
        >
          {{ $t('save') }}
        </v-btn>
      </div>
    </v-card>
    
    <!-- Avatar Upload Dialog -->
    <UploadAvatar
      v-model="isShowAvatarUpdateDialog"
      :upload-progress="uploadProgress"
      @close="closeUploadedAvatarDialog"
      @file-cropped="handleAvatarCropped"
    />
  </v-container>
</template>

<script>
import { defineComponent } from 'vue';
import ProjectViewUserTable from '@/components/Project/ProjectViewUserTable.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import ProjectUserFilter from '@/components/Project/ProjectUserFilter.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import PencilIcon from '@/assets/svg/pencil.svg';
import fileValidator from '@/mixins/fileValidator.js';
import UploadAvatar from '@/components/Profile/UploadAvatar.vue';
import Avatar from "@/components/base/Avatar.vue";
import { useProjectDetail } from '@/composables/modules/project/detail';

export default defineComponent({
  name: 'ProjectDetailView',
  
  components: {
    ProjectViewUserTable,
    SearchComponent,
    ProjectUserFilter,
    SettingsMenu,
    PencilIcon,
    UploadAvatar,
    Avatar
  },

  mixins: [fileValidator],

  setup() {
    const {
      // Reactive data
      project,
      projectUsers,
      searchTerm,
      selectedFilters,
      uploadProgress,
      isShowAvatarUpdateDialog,
      profileProgress,
      edit,
      loader,
      defaultImage,
      
      // Computed properties
      filteredUsers,
      
      // Methods
      resetFilter,
      resetSearchTerm,
      updateSelectedFilters,
      deleteSelectedFilters,
      closeUploadedAvatarDialog,
      handleAvatarCropped,
      updateProjectField,
      onSubmit,
      getProjectTags,
      saveProject,
      getRoles,
      getProject,
      clearCache,
      back
    } = useProjectDetail();

    return {
      // Expose all composable values to template
      project,
      projectUsers,
      searchTerm,
      selectedFilters,
      uploadProgress,
      isShowAvatarUpdateDialog,
      profileProgress,
      edit,
      loader,
      defaultImage,
      filteredUsers,
      resetFilter,
      resetSearchTerm,
      updateSelectedFilters,
      deleteSelectedFilters,
      closeUploadedAvatarDialog,
      handleAvatarCropped,
      updateProjectField,
      onSubmit,
      getProjectTags,
      saveProject,
      getRoles,
      getProject,
      clearCache,
      back
    };
  }
});
</script>

<style scoped>
.project-detail-height {
  min-height: calc(100vh - 140px);
}

.btn-table-action {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 1rem 1.5rem;
}

.edit-title-input {
  font-size: 24px !important;
  font-weight: bold !important;
}

.edit-subtitle-input {
  font-size: 14px !important;
  font-weight: regular !important;
}

.v-field__input {
  font-size: inherit !important;
  font-weight: inherit !important;
}
</style>
