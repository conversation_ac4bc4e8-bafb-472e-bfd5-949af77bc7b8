<template>
  <v-container
    id="create-org-container"
    fluid
    class="d-flex align-center justify-center"
  >
    <v-row class="d-flex align-center justify-center">
      <v-col
        cols="10"
      >
        <v-card
          class="pt-2 px-6 mt-3"
          rounded="lg"
          elevation="0"
          width="100%"
        >
          <v-form
            id="create-org-form"
            ref="form"
            v-model="validForm"
            lazy-validation
          >
            <v-row>
              <v-col
                cols="12"
                class="text-center max-width mb-5"
              >
                <h1 class="create-org-title">
                  {{ $t('organization.createOrganizationTitle') }}
                </h1>
                <p class="create-org-description">
                  {{ $t('organization.createOrganizationDescription') }}
                </p>

                <section class="d-flex ga-8 justify-center">
                  <div class="card bg-primary">
                    <span class="card-sub-title">
                      {{ $t('organization.freeTrial') }}
                    </span>
                    <p class="card-title">
                      <span class="font-weight-bold fs-32px">14</span>
                      {{ $t('organization.days') }}
                    </p>
                  </div>
                  <div class="card bg-grey">
                    <span class="card-sub-title">
                      {{ $t('organization.afterTrial') }}
                    </span>
                    <p class="card-title">
                      <v-skeleton-loader
                        v-if="loadingSubscriptionPlans"
                        type="text"
                        width="80"
                      />
                      <template v-else>
                        <span class="font-weight-bold fs-32px">{{ pricePerMonth }}</span>
                        {{ $t('organization.perUserPerMonth') }}
                      </template>
                    </p>
                  </div>
                </section>
              </v-col>
              <v-col
                cols="12"
                class="pb-0 max-width"
              >
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('orgName') }} <strong class="red--text text--lighten-1">*</strong>
                </p>
                <v-text-field
                  v-model="org.name"
                  type="text"
                  dense
                  filled
                  :placeholder="$t('enterOrgname')"
                  :rules="orgNameValidation"
                />
              </v-col>

              <v-col
                cols="12"
                class="pb-0 max-width"
              >
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('orgAccountName') }}  <strong class="red--text text--lighten-1">*</strong>
                </p>

                <v-text-field
                  v-model="org.handle"
                  type="text"
                  dense
                  filled
                  :placeholder="$t('enterOrgHandle')"
                  :loading="handleRequestState.isLoading"
                  :rules="accountNameValidation"
                  :hint="orgHandleHint"
                  persistent-hint
                />
              </v-col>

              <v-col
                cols="12"
                class="d-flex justify-center mt-4 max-width"
              >
                <div class="d-flex ga-3">
                  <v-btn

                    depressed
                    background-color="#F2F4F7"
                    class="font-inter text-capitalize black--text mr-4 "
                    height="40"
                    width="211px"
                    @click="skipToWorkspace()"
                  >
                    {{ $t('organization.skipForNow') }}
                  </v-btn>

                  <v-btn
                    color="blue"
                    width="211px"
                    elevation="0"
                    class="white--text text-capitalize rounded"
                    :disabled="!validForm || isLoading"
                    :loading="isLoading"
                    @click="createOrganization()"
                  >
                    {{ $t('organization.startFreeTrial') }}
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import {  mapActions } from "vuex";
import makeOrgService from '@/services/api/org';
import makeUserService from "@/services/api/user";
import { handleDuplicateMixin } from '@/mixins/handleDuplicate';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import fileValidator from '@/mixins/fileValidator.js';
import { orgImageTypes } from '@/constants/fileTypes.js';
import makeBillingService from '@/services/api/billing';

let orgService;
let userService;

export default {
  mixins: [handleDuplicateMixin, fileValidator],
  data() {
    return {
      orgImageTypes,
      orgNameValidation: [
        value => !!value || this.$t('error.requiredField'),
        value => (value.length >= 2 && value.length <= 50) || this.$t('min2max50Chars')
      ],
      validForm: false,
      org: {
        name: '',
        handle: '',
      },
      file: null,
      isLoading: false,
      loadingSubscriptionPlans: false,
      subscriptionPlans: [],
    };
  },
  computed: {
    orgHandleHint() {
      if (this.org.handle === '') {
        return this.$t('orgAccountNameLabel')
      }
      if (!this.handleError && this.handleAvailable) {
        return "handle is available"
      }
      return ''
    },
    accountNameValidation() {
      const defaultRules = [
        value => !!value || this.$t('error.requiredField'),
        value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || this.$t('invalidUsername'),
      ]
      if (this.handleError) {
        return [
          ...defaultRules,
        ]
      }
      return defaultRules
    },
  pricePerMonth(){
      if(this.subscriptionPlans.length){
        const price = this.subscriptionPlans.find(plan => plan.amount != 0).amount;
        return `$${price/100}`;
      }
      return '$0';
    },
  },
  watch: {
    'org.handle': {
      handler: 'usernameInUse',
      immediate: true,
    },
  },
  async created() {
    orgService = makeOrgService(this.$api);
    userService = makeUserService(this.$api);
    await this.getSubscriptionPlans();
  },
  methods: {
    ...mapActions({
      setOrgs: 'user/setOrgs',
      uploadToServer: 'attachment/uploadToServer'
    }),
    validate() {
      return this.$refs.form.validate();
    },
    reset() {
      return this.$refs.form.reset();
    },
    resetValidation() {
      return this.$refs.form.resetValidation();
    },
    async createOrganization() {
      const isValidForm = this.validate();

      if (!isValidForm) {
        return;
      }

      this.isLoading = true;
      const data = {
        name: this.org.name,
        handle: this.org.handle,
      };

      try {
        const response = await orgService.newOrg(data);
        
        const { 
          uid,
          name,
          handle,
          roleName,
          createdBy,
          createdAt,
        } = response.data;
        
        const org = {
          uid,
          name,
          handle,
          roleName,
          createdBy,
          createdAt,
          type: "org"
        };

        // Get current orgs and add the new one
        const userResponse = await userService.getOrgs(this.$store.state?.user?.user?.uid);
        this.setOrgs(userResponse.data?.orgs || []);

        showSuccessToast(this.$swal, this.$t('success.organizationCreated'));
        // gtag event for organization creation
        if(process.env.NODE_ENV === 'production') {
          this.$gtag.event('create_organization', {
            event_category: 'organization',
            event_label: `Org: ${org.handle}`,
            value: 1
          });
        }

        // Redirect to workspace
        this.$router.push({
          name: 'Workspace',
          params: { handle: org.handle },
        });
      } catch (err) {
        showErrorToast(this.$swal, err.response?.data?.message || err.response?.data?.error || err.response?.data[0]?.msg || this.$t('error.problemProcessingRequest'));
      } finally {
        this.isLoading = false;
      }
    },
    async skipToWorkspace() {
      try {
        // Get user handle from store state to navigate to personal workspace
        const userHandle = this.$store.state.user.user?.handle;
        
        if (userHandle) {
          this.$router.push({
            name: 'Workspace',
            params: { handle: userHandle },
          });
        } else {
          // Fallback in case user handle is not available
          const userResponse = await userService.getOrgs(this.$store.state?.user?.user?.uid);
          const orgs = userResponse.data?.orgs || [];
          
          if (orgs.length > 0) {
            // Navigate to the first available organization
            this.$router.push({
              name: 'Workspace',
              params: { handle: orgs[0].handle },
            });
          } else {
            // If no orgs available, show error
            showErrorToast(this.$swal, this.$t('error.unableToSkip'));
          }
        }
      } catch (err) {
        showErrorToast(this.$swal, this.$t('error.problemProcessingRequest'));
      }
    },
    async getSubscriptionPlans() {
      this.loadingSubscriptionPlans = true;
      try {
        const billingService = makeBillingService(this.$api);
        const response = await billingService.getSubscriptionPlans({
          model: 'org',
        });
        this.subscriptionPlans = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'subscription plans' }, error?.response?.data);
      } finally {
        this.loadingSubscriptionPlans = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.fs-32px{
  font-size: 32px;
}
.card{
  width: 215px;
  height: 112px;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
}
.card-sub-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
}

.card-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 12px;
}
.create-org-title {
  font-family: Inter;
  font-weight: 600;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  margin-top: 48px;
}

.create-org-description {
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 16px;
}
.rounded{
  border-radius: 6px !important;
}
.bg-primary{
  background-color: #0C2FF3;
  color: white;
}
.bg-grey{
  background-color: #F9F9FB;
  color: #667085;
}
.max-width{
  max-width: 512px !important;
  margin: 0 auto;
}
.ga-8 {
  gap: 8px;
}
</style>
