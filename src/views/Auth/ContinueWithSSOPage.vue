<template>
  <v-container
    id="continue-with-sso-container"
    fluid
    class="d-flex align-center justify-center bg-black min-h-screen"
  >
    <v-row class="d-flex align-center justify-center">
      <v-col
        offset="1"
        offset-sm="2"
        offset-md="2"
        offset-lg="3"
        cols="10"
        sm="8"
        md="8"
        lg="6"
      >
        <v-row
          class="text-h4 font-weight-bold mb-8"
          justify="center"
        >
          {{ $t("loginWithSSO") }}
        </v-row>

        <p class="text-subtitle-1 text-grey mb-8 text-center">
          {{ $t("portalFor") }} {{ orgHandle }}
        </p>
        
        <v-btn
          id="login-button"
          block
          color="primary"
          class="btn-theme"
          :depressed="true"
          width="188px"
          height="40"
          :disabled="loading"
          :loading="loading"
          :class="{ 'btn-loading-opacity': loading }"
          @click="login"
        >
          {{ $t("continueWithSSO") }}
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
const { mapActions, mapState } = createNamespacedHelpers("user");
import makeAuthService from "@/services/api/auth";
import makeUserService from "@/services/api/user";
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';

let authService;
let userService;

export default {
  name: 'ContinueWithSSOPage',
  mixins: [colorPreferencesMixin],
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    orgHandle() {
      return this.$route.params.handle;
    },
  },
  created() {
    authService = makeAuthService(this.$api);
    userService = makeUserService(this.$api);
  },
  async mounted() {
    const token = this.$route.query.loginToken;
    if (!this.orgHandle) {
      this.$router.push({ name: "Login" });
    }
    if (token) {
        this.loading = true;
        try {
          const response = await authService.validateSSOLogin(token)
          const user = response.data;
          const orgResponse = await userService.getOrgs(user.uid);
          const { orgs } = orgResponse.data

          let account = {
            handle: user.handle,
            type: "user",
            name: `${user.firstName} ${user.lastName}`,
            roleName: "owner",
            avatarUrl: user.avatarUrl
          };
          
          const savedAccount = this.currentAccount;
          if (savedAccount) {
            const matchingOrg = orgs.find((org) => org.uid === savedAccount.uid);
            if (matchingOrg) {
              account = matchingOrg;
            }
          }

          this.initSession({ user, currentAccount: account, orgs });
          this.updatePreferences(account.handle);

          this.$router.push({
            name: 'Workspace',
            params: { handle: account.handle },
          });

          const createdAtMs = new Date(user.createdAt).getTime();
          // Consider the user new if created within the last 60 seconds
          const ageInSeconds = (Date.now() - createdAtMs) / 1000;
          // gtag event for sso sign in
          if(process.env.NODE_ENV === 'production' && ageInSeconds <= 60) {
            this.$gtag.event('sso-signup', {
              event_category: 'user',
              event_label: `User: ${user.handle}`,
              value: 1
            });
        }

          showSuccessToast(this.$swal, this.$t('loginSuccess'));
        } catch (error) {
          showErrorToast(this.$swal, error.response?.data?.error || error.message);
        } finally {
          this.loading = false;
        }
      }
  },
  methods: {
    ...mapActions(["initSession"]),
    async login() {
      this.loading = true;
      try {
        const res = await authService.loginWithSSO(this.orgHandle);
        window.location.href = res.data.url;
      } catch (error) {
        this.loading = false;
        if(error.response?.status===404){
          showErrorToast(this.$swal,this.$t('error.ssoSigninFailed'))
        }else
        showErrorToast(this.$swal, 'error', { item: 'Authentication' }, error?.response?.data?.errors || error?.response?.data?.message || error?.message);
      }
      finally {
        this.loading = false;
      }
    },
  },
};
</script>
