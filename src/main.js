import './plugins/_globals';
import './plugins/vee-validate';
import 'sweetalert2/dist/sweetalert2.min.css';
import './assets/scss/custom.scss';

import App from './App.vue';
import { StripePlugin } from '@vue-stripe/vue-stripe';
import Vue from 'vue';
import VueInputAutowidth from 'vue-input-autowidth';
import VueSweetalert2 from 'vue-sweetalert2';
import { uuid } from 'vue-uuid';
import i18n from './i18n';
import makeAPI from './services/api/index';
import makeRouter from './router';
import makeStore from './store';
import vuetify from './plugins/vuetify';
import * as Sentry from '@sentry/vue';
import mixinPermissions from '@/mixins/permissions';
import {handleVersionHeaders,  handleLogoutReset } from '@/utils/apiHelpers';
import { checkFrontendVersionMismatch } from '@/utils/versionCheck';
import VueGtag from 'vue-gtag'


const api = makeAPI(import.meta.env.VITE_APP_SERVER_BASEURL);

export const $api = api;

const store = makeStore(api);
export const useStore = () => store;
const router = makeRouter(store);

api.interceptors.request.use(
  function (config) {
    const traceId = uuid.v4();
    config.headers['X-Trace-ID'] = traceId;

    const currentAppVersion = `v${import.meta.env.VITE_APP_VERSION}`;

    if (store?.state?.versions) {
      const frontendVersion = currentAppVersion || store.state.versions.frontendVersion;
      if (frontendVersion) {
        config.headers['x-tf-frontend-version'] = frontendVersion;
      }

      if (store.state.versions.backendVersion) {
        config.headers['x-tf-backend-version'] = store.state.versions.backendVersion;
      }

      const backendRedirectCount = store.getters['versions/backendRedirectCount'] || 0;
      config.headers['x-tf-backend-redirect'] = String(backendRedirectCount);
    }

    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  async (res) => {
    const url = res.config.url;
    const currentAppVersion = `v${import.meta.env.VITE_APP_VERSION}`;

    // Handle version headers for all responses
    if (res.headers && store) {
      handleVersionHeaders(res.headers, store);
      try {
        checkFrontendVersionMismatch(store, currentAppVersion);
      } catch (error) {
        console.error('Error checking version mismatch:', error);
      }

      if (res.headers['x-tf-reset']) {
        await handleLogoutReset(store, router);
        return res;
      }
    }

    // Handle 204 status - Backend version mismatch
    if (res.status === 204 && res.headers['x-tf-backend-redirect'] !== undefined) {
      const backendRedirectCount = store.getters['versions/backendRedirectCount'] || 0;

      if (!store.getters['versions/canBackendRedirect']) {
        console.error("Max backend redirect retries reached");
        Sentry.captureException(new Error('Max backend redirect retries reached'), {
          level: 'error',
          tags: {
            component: 'frontend',
            issue_type: 'backend-redirect-max-retries',
          },
          extra: {
            url,
            backendRedirectCount,
            versions: store.state.versions,
          },
        });
        return res;
      }

      store.dispatch('versions/trackBackendRedirect');

      try {
        const retryConfig = {
          ...res.config,
          _isRetry: true,
          headers: { ...res.config.headers },
        };

        return await api.request(retryConfig);
      } catch (error) {
        Sentry.captureException(error, {
          level: 'error',
          tags: {
            component: 'frontend',
            issue_type: 'backend-redirect-retry-failed',
          },
          extra: {
            originalUrl: url,
            backendRedirectCount,
            versions: store.state.versions,
            retryError: error.message,
          },
        });

        throw error;
      }
    }

    return res;
  },
  async (error) => {
    const res = error.response;
    const status = res?.status;
   
    if (status === 401 && router.history._startLocation !== '/login') {
      await handleLogoutReset(store, router);
    }
    if (status === 423) {
      router.push({ name: 'Maintenance' });
    }
    if (status === 503) {
      // This usually means the httproute is broken. We should remove headers.
      delete res.headers['x-tf-backend-version'];
      const currentAppVersion = `v${import.meta.env.VITE_APP_VERSION}`;
      if (res.headers && store) {
        handleVersionHeaders(res.headers, store);
        try {
          checkFrontendVersionMismatch(store, currentAppVersion);
        } catch (error) {
          console.error('Error checking version mismatch:', error);
        }
  
        if (res.headers['x-tf-reset']) {
          await handleLogoutReset(store, router);
        }
      }

      Sentry.captureException(error, {
        level: 'error',
        tags: {
          component: 'frontend',
          issue_type: 'backend-503',
        },
        extra: {
          versions: store.state.versions,
          retryError: error.message,
        },
      });

    }

    return Promise.reject(error);
  }
);

// ... rest of Vue initialization ...

Vue.use(i18n);
Vue.use(VueInputAutowidth);
Vue.use(VueSweetalert2);
Vue.use(StripePlugin, { pk: import.meta.env.VITE_APP_STRIPE_PUBLIC_KEY });

Vue.config.productionTip = false;


store.subscribe((mutation, { user }) => {
  localStorage.setItem('user', JSON.stringify(user.user));
  localStorage.setItem('orgs', JSON.stringify(user.orgs));
});

const app = new Vue({
  vuetify,
  router,
  store,
  i18n,
  api,
  mixinPermissions,
  render: (h) => h(App),
});

if (['production', 'staging'].includes(import.meta.env.MODE)) {
 Sentry.init({
   Vue,
   dsn: 'https://<EMAIL>/4508568917508096',
   integrations: [Sentry.browserTracingIntegration({ router }), Sentry.replayIntegration()],
   environment: import.meta.env.MODE,
   tracesSampleRate: 1.0,
   tracePropagationTargets: [/^https:\/\/(?:staging\.|)(?:app|api)\.testfiesta.com/],
   replaysSessionSampleRate: 0.1,
   replaysOnErrorSampleRate: 1.0,
 });
}
  // google analytics
  if(process.env.NODE_ENV == 'production'){
    Vue.use(VueGtag, {
      config: {
        id: import.meta.env.VITE_APP_GTAG_ID,
      }
    })
  } 

app.$mount('#app');

if (window.Cypress) {
  window.app = app;
}

