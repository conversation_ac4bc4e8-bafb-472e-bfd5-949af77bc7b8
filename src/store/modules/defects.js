export default function makeDefectStore() {
  return {
    namespaced: true,
    
    state: {
      projectIntegrationPreferences: {}, // Store per-project preferences: { handle_projectKey: integration }
    },
    
    mutations: {
      SET_PROJECT_INTEGRATION_PREFERENCE(state, { handle, projectKey, integration }) {
        const key = `${handle}_${projectKey}`;
        state.projectIntegrationPreferences[key] = integration;
      },
    },
    
    actions: {
      setProjectIntegrationPreference({ commit }, { handle, projectKey, integration }) {
        commit('SET_PROJECT_INTEGRATION_PREFERENCE', { handle, projectKey, integration });
      },
    },
    
    getters: {
      getProjectIntegrationPreference: (state) => (handle, projectKey) => {
        const key = `${handle}_${projectKey}`;
        return state.projectIntegrationPreferences[key] || null;
      },
    },
  };
} 