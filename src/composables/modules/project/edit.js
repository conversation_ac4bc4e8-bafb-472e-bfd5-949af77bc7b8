import { ref, computed, reactive, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import { useProjectsManagement } from '@/composables/modules/project/view';
import makeTagService from '@/services/api/tag';
import makeProjectService from '@/services/api/project';
import makeRoleService from '@/services/api/role';
import { projectImageTypes } from '@/constants/fileTypes.js';
import { users } from '@/constants/data.js';
import Swal from 'sweetalert2';

export const useProjectEdit = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { clearCache } = useProjectsManagement();
  const { authorityTo } = usePermissions();
  
  // Loading state
  const { isLoading: skeletonLoaderState, showSkeletonLoader, hideSkeletonLoader } = useLoading();
  const loading = ref(false);

  // Route parameters
  const handle = computed(() => route.params.handle);
  const projectKey = computed(() => route.params.key);

  // Project data
  const selectedProject = ref({});
  const projectTitle = ref('');
  const projectSubTitle = ref('');

  // UI state
  const editTitle = ref(false);
  const visibleTitle = ref(false);
  const drawer = ref(false);
  const title = ref('');
  const description = ref('');
  const type = ref('');

  // File handling
  const file = ref(null);
  const imageSrc = ref(null);
  const uploadAvatar = ref(false);

  // Filter and search state
  const selectedRoles = ref([]);
  const selectedTags = ref([]);
  const selectedProjects = ref([]);
  const searchFilter = ref('');
  const filter = ref('active');

  // Members data
  const activeMembers = ref([]);
  const activeUserCount = ref(0);

  // Reference data
  const tags = ref([]);
  const roles = ref([]);
  const projectsData = ref([]);
  const headers = ref([]);

  // Constants
  const users_data = ref(users);
  const itemKey = ref('uid');
  const defaultImage = new URL('@/assets/png/project.png', import.meta.url).href;

  // Computed properties
  const activeMembersHasData = computed(() => {
    return activeMembers.value?.length > 0;
  });

  const _readMember = computed(() => {
    return authorityTo('read_member');
  });

  const filteredItem = computed(() => {
    let filtered =
      filter.value === 'active'
        ? activeMembers.value
        : filter.value === 'pending'
          ? []
          : [];

    if (searchFilter.value.length > 0) {
      filtered = filtered.filter((item) => matchesFilter(item));
    }
    
    if (selectedRoles.value.length > 0) {
      filtered = filtered.filter(user => user.role && selectedRoles.value.includes(user.role.name));
    }
      
    if (selectedTags.value.length > 0) {
      filtered = filtered.filter(user => user.tags?.some(tag => selectedTags.value.includes(tag.uid)));
    }

    return filtered;
  });

  const filteredHeaders = computed(() => {
    const filtered = filteredMenuHeaders.value.filter((header) => header.checked);
    return filtered;
  });

  const filteredMenuHeaders = computed(() => {
    const filtered = headers.value.filter((header) => header.text != 'Actions');
    return filtered;
  });

  // Helper functions
  const matchesFilter = (item) => {
    const lowerCaseFilter = searchFilter.value?.toLowerCase();
    
    return [item?.firstName, item?.lastName, item?.email]
      .some(field => field?.toLowerCase().includes(lowerCaseFilter));
  };

  // API methods
  const getMembers = async (projectKey) => {
    const projectService = makeProjectService($api);
    const currentHandle = handle.value;
    try {
      const response = await projectService.getUsers({
        handle: currentHandle,
        projectKey,
      });
      activeMembers.value = response?.data?.users;
      if (activeMembers.value?.length > 0) {
        activeUserCount.value = activeMembers.value.length;
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getTags = async () => {
    const currentHandle = handle.value;
    const tagService = makeTagService($api);
    try {
      const response = await tagService.getTags(currentHandle);
      tags.value = response.data;
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'tags' }, err?.response?.data);
      return [];
    }
  };

  const getProjects = async (additionalParams = {}, currentHandle) => {
    if (!currentHandle) currentHandle = handle.value;
    const searchParams = new URLSearchParams();
    const projectService = makeProjectService($api);
    searchParams.set('status', filter.value);
    searchParams.set('includeCount', true);
  
    Object.keys(additionalParams).forEach(key => {
      searchParams.set(key, additionalParams[key]);
    });

    loading.value = true;
    try {
      const response = await projectService.getProjects(currentHandle, searchParams.toString());
      projectsData.value = response.data.items;
      selectedProject.value = response.data.items.find((project) => project.key === projectKey.value);
      projectTitle.value = selectedProject.value?.name;
      projectSubTitle.value = selectedProject.value?.customFields?.description;
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      return [];
    } 
  };

  const getRoles = async (currentHandle) => {
    const roleService = makeRoleService($api);
    try {
      const currentProjectKey = projectKey.value;
      const response = await roleService.getRoles(currentHandle, currentProjectKey);
      roles.value = response.data?.items;
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'roles' }, err?.response?.data);
    }
  };

  // Initialize headers
  const initializeHeaders = () => {
    if (!store.getters['headers/dynamicHeaders'].projectDetails) {
      store.dispatch('headers/initializeHeaders', { type: 'projectDetails' });
    }
    headers.value = store.getters['headers/dynamicHeaders'].projectDetails;
  };

  // File handling methods
  const handleFileChange = (uploadedFile) => {
    if (projectImageTypes.includes(uploadedFile.type)) {
      previewImage(uploadedFile);
    }
  };

  const previewImage = async (imageFile) => {
    if (imageFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        imageSrc.value = e.target.result;
      };
      reader.readAsDataURL(imageFile);
      file.value = imageFile;

      if (selectedProject.value.uid && file.value) {
        const currentHandle = handle.value;
        const mediaType = 'attachment';
        const apiService = makeProjectService($api);
        const params = {
          handle: currentHandle,
          projectKey: projectKey.value
        };
        try {
          await store.dispatch('attachment/uploadToServer', {
            handle: currentHandle, 
            mediaType, 
            file: file.value, 
            apiService, 
            params
          });
          showSuccessToast(Swal, 'Profile updated');
        } catch (err) {
          if (err?.status == 507) {
            showErrorToast(Swal, 'Upload limit reached', {}, 'limitReached', currentHandle);
          }
        }
      }
    }
  };

  // Business logic methods
  const handleUserFilter = (filterData) => {
    selectedRoles.value = filterData.roles.map(item => item.name);
    selectedTags.value = filterData.tags.map(item => item.uid);
    selectedProjects.value = filterData.projects.map(item => item.name);
  };

  const setSearchFilter = (searchText) => {
    searchFilter.value = searchText;
  };

  const handleClickEditTag = () => {
    editTitle.value = true;
    projectSubTitle.value = selectedProject.value.customFields.description;
    projectTitle.value = selectedProject.value.name;
  };

  const handleCreateTag = () => {
    title.value = '';
    description.value = '';
    type.value = '';
    drawer.value = !drawer.value;
  };

  const handleEditTag = (tag) => {
    title.value = tag;
    description.value = '';
    type.value = '';
    drawer.value = !drawer.value;
  };

  const clearAll = () => {
    selectedRoles.value = [];
    selectedTags.value = [];
  };

  // Navigation methods
  const back = () => {
    router.push({
      name: 'ProjectsView'
    });
  };

  const onProjectCancel = () => {
    users_data.value.forEach((user) => {
      user.selected = false;
    });
    router.go(-1);
  };

  const onProjectSave = async () => {
    const payload = {
      customFields: {
        ...selectedProject.value.customFields,
        description: projectSubTitle.value,
      },
      name: projectTitle.value,
    };
    
    await store.dispatch('project/update', {
      swal: Swal,
      handle: handle.value,
      oldProject: selectedProject.value,
      payload,
    });
    
    // Clear project cache after successful update
    clearCache();
    
    router.go(-1);
  };

  const onTagSave = () => {
    // TODO: Implement tag save functionality
  };

  // Additional utility methods
  const handleUpdateRole = (role, userID) => {
    let _users = [...users_data.value];
    users_data.value = _users.map((user) => {
      if (user.id === userID) {
        return {
          ...user,
          role,
        };
      } else return user;
    });
  };

  const selectItem = (item) => {
    item.selected = true;
  };

  const all = () => {
    filter.value = 'all';
  };

  const select = () => {
    filter.value = 'selected';
  };

  // Route change handler
  const handleRouteUpdate = async (to, from) => {
    const newHandle = to.params.handle;
    if (newHandle && newHandle !== from.params.handle) {
      try {
        await init(newHandle);
        return true;
      } catch (error) {
        return true;
      }
    } else {
      return true;
    }
  };

  // Initialization method
  const init = async (currentHandle) => {
    try {
      showSkeletonLoader();
      await Promise.all([getTags(currentHandle), getRoles(currentHandle), getProjects({}, currentHandle)]);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      loading.value = false;
      hideSkeletonLoader();
    }
    if (_readMember.value) {
      await getMembers(projectKey.value);
    }
  };

  return {
    // Reactive state
    selectedProject,
    projectTitle,
    projectSubTitle,
    editTitle,
    visibleTitle,
    drawer,
    title,
    description,
    type,
    file,
    imageSrc,
    uploadAvatar,
    selectedRoles,
    selectedTags,
    selectedProjects,
    searchFilter,
    filter,
    activeMembers,
    activeUserCount,
    tags,
    roles,
    projectsData,
    headers,
    users_data,
    itemKey,
    loading,
    skeletonLoaderState,
    
    // Computed properties
    activeMembersHasData,
    _readMember,
    filteredItem,
    filteredHeaders,
    filteredMenuHeaders,
    
    // Constants
    defaultImage,
    projectImageTypes,
    
    // Methods
    getMembers,
    getTags,
    getProjects,
    getRoles,
    initializeHeaders,
    handleFileChange,
    previewImage,
    handleUserFilter,
    setSearchFilter,
    handleClickEditTag,
    handleCreateTag,
    handleEditTag,
    clearAll,
    back,
    onProjectCancel,
    onProjectSave,
    onTagSave,
    handleUpdateRole,
    selectItem,
    all,
    select,
    handleRouteUpdate,
    init,
    showSkeletonLoader,
    hideSkeletonLoader,
  };
};
