import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import makeProjectService from '@/services/api/project';
import makeRoleService from '@/services/api/role';
import makeTagService from '@/services/api/tag';
import { showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import { useProjectsManagement } from '@/composables/modules/project/view';
import Swal from 'sweetalert2';

export const useProjectDetail = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  // Reactive data
  const project = ref({});
  const projectUsers = ref([]);
  const searchTerm = ref('');
  const selectedFilters = ref([]);
  const uploadProgress = ref(0);
  const isShowAvatarUpdateDialog = ref(false);
  const profileProgress = ref(0);
  const availableRoles = ref([]);
  const availableTags = ref([]);
  const availableProjects = ref([]);
  const edit = ref({
    name: null,
    description: null,
    status: null,
    tags: null,
    maxTestersPerCase: null,
    casePrefix: null,
    allowDuplicateCases: false,
    hideNumbersInCaseTitle: false
  });
  const loader = ref({
    projectTags: false,
    users: false,
    project: false,
    roles: false
  });
  const defaultImage = new URL('@/assets/png/project.png', import.meta.url).href;

  // Computed properties
  const filteredUsers = computed(() => {
    let filtered = projectUsers.value;
    
    // Apply search filter
    if (searchTerm.value.length > 0) {
      filtered = filtered.filter((user) => 
        user.fullName?.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.value.toLowerCase())
      );
    }

    // Apply role, tag, and project filters
    if (selectedFilters.value.length > 0) {
      filtered = filtered.filter((user) => {
        return selectedFilters.value.some(filter => {
          switch (filter.type) {
            case 'role':
              return user.role === filter.value || user.roleName === filter.value;
            case 'tag':
              return user.tags?.some(tag => tag === filter.value || tag.name === filter.value);
            case 'project':
              return user.projectKey === filter.value || user.project?.key === filter.value;
            default:
              // Legacy support for old filter structure
              return user.role === filter.value;
          }
        });
      });
    }

    return filtered;
  });

  // Methods
  const resetFilter = () => {
    selectedFilters.value = [];
  };

  const resetSearchTerm = () => {
    searchTerm.value = '';
  };

  const updateSelectedFilters = (filters) => {
    // Handle the filters object from ProjectUserFilter
    if (filters && typeof filters === 'object') {
      const allFilters = [];
      
      // Add role filters
      if (filters.roles && Array.isArray(filters.roles)) {
        filters.roles.forEach(role => {
          allFilters.push({
            id: `role_${role.id}`,
            type: 'role',
            value: role.name || role.value,
            label: role.name || role.label
          });
        });
      }
      
      // Add tag filters
      if (filters.tags && Array.isArray(filters.tags)) {
        filters.tags.forEach(tag => {
          allFilters.push({
            id: `tag_${tag.id}`,
            type: 'tag',
            value: tag.name || tag.value,
            label: tag.name || tag.label
          });
        });
      }
      
      // Add project filters
      if (filters.projects && Array.isArray(filters.projects)) {
        filters.projects.forEach(project => {
          allFilters.push({
            id: `project_${project.id}`,
            type: 'project',
            value: project.key || project.value,
            label: project.name || project.label
          });
        });
      }
      
      selectedFilters.value = allFilters;
    } else {
      // Fallback for direct array assignment
      selectedFilters.value = filters || [];
    }
  };

  const deleteSelectedFilters = (filterToDelete) => {
    selectedFilters.value = selectedFilters.value.filter(
      filter => filter.id !== filterToDelete.id
    );
  };

  const closeUploadedAvatarDialog = () => {
    isShowAvatarUpdateDialog.value = false;
  };

  const handleAvatarCropped = async (data) => {
    isShowAvatarUpdateDialog.value = false;
    profileProgress.value = 0;
    
    try {
      if (data) {
        profileProgress.value = 1;
        
        const apiService = makeProjectService($api);
        const formData = new FormData();
        formData.append('logo', dataURLtoFile(data.objectURL, 'avatar.png'));
        
        const response = await apiService.uploadLogo(route.params.handle, route.params.key, formData, {
          onUploadProgress: (event) => {
            profileProgress.value = Math.round((event.loaded * 100) / event.total);
          }
        });
        
        showSuccessToast(Swal, 'Profile updated');
        project.value.logo = response.data.logo;
      }
    } catch (e) {
      profileProgress.value = 0;
      showErrorToast(Swal, 'Upload failed');
    }
  };

  const updateProjectField = async (field, value) => {
    if (edit.value[field] === null) {
      edit.value[field] = value;
      return;
    }
    
    try {
      const projectService = makeProjectService($api);
      await projectService.updateProject(route.params.handle, route.params.key, {
        [field]: value
      });
      
      project.value[field] = value;
      edit.value[field] = null;
      showSuccessToast(Swal, 'Project updated');
    } catch (error) {
      showErrorToast(Swal, 'Update failed');
    }
  };

  const onSubmit = () => {
    saveProject();
  };

  const getProjectTags = async () => {
    loader.value.projectTags = true;

    try {
      // Use the tag service directly instead of Vuex action
      const tagService = makeTagService($api);
      const response = await tagService.getTags(route.params.handle, '', {
        projectKey: route.params.key
      });
      
      // Update project tags if needed
      if (response.data && response.data.length) {
        project.value.tags = response.data;
      }
      
      loader.value.projectTags = false;
    } catch (error) {
      loader.value.projectTags = false;
      showErrorToast(Swal, 'Failed to fetch project tags');
    }
  };

  const saveProject = async () => {
    try {
      loader.value.project = true;

      const fields = {};
      for (const [key, value] of Object.entries(edit.value)) {
        if (value !== null) {
          fields[key] = value;
        }
      }

      if (Object.keys(fields).length) {
        const projectService = makeProjectService($api);
        await projectService.updateProject(route.params.handle, route.params.key, fields);

        // Update local project data
        Object.assign(project.value, fields);
        
        // Reset edit fields
        for (const key of Object.keys(fields)) {
          edit.value[key] = null;
        }
        
        showSuccessToast(Swal, 'Project saved');
      }

      loader.value.project = false;
    } catch (error) {
      loader.value.project = false;
      if (error?.response?.status === 422) {
        if (error?.response?.data?.errors) {
          for (const [key] of Object.entries(error?.response?.data?.errors)) {
            if (edit.value[key] !== null) {
              edit.value[key] = null;
            }
          }
        }
      }
      showErrorToast(Swal, 'Save failed');
    }
  };

  const getProject = async () => {
    loader.value.project = true;

    try {
      const projectService = makeProjectService($api);
      const response = await projectService.getProject(route.params.handle, route.params.key);

      project.value = response.data.project || response.data;
      projectUsers.value = response.data.members || [];

      // Initialize edit values
      for (const key of Object.keys(edit.value)) {
        edit.value[key] = project.value[key];
      }

      loader.value.project = false;
    } catch (error) {
      loader.value.project = false;
      showErrorToast(Swal, 'Failed to fetch project');
    }
  };

  const getRoles = async () => {
    loader.value.roles = true;
    
    try {
      const roleService = makeRoleService($api);
      const response = await roleService.getRoles(route.params.handle, route.params.key);
      availableRoles.value = response.data.items || response.data || [];
      loader.value.roles = false;
    } catch (err) {
      loader.value.roles = false;
      showErrorToast(Swal, 'Failed to fetch roles');
    }
  };

  const getTags = async () => {
    try {
      const tagService = makeTagService($api);
      const response = await tagService.getTags(route.params.handle, 'users');
      availableTags.value = response.data || [];
    } catch (err) {
      showErrorToast(Swal, 'Failed to fetch tags');
    }
  };

  const getProjects = async () => {
    try {
      const projectService = makeProjectService($api);
      const response = await projectService.getProjects(route.params.handle, '');
      availableProjects.value = response.data.items || [];
    } catch (err) {
      showErrorToast(Swal, 'Failed to fetch projects');
    }
  };

  const clearCache = () => {
    const { clearCache: clearProjectsCache } = useProjectsManagement();
    clearProjectsCache();
  };

  const back = () => {
    router.push({
      name: 'ProjectsView'
    });
  };

  // Helper function
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Lifecycle
  onMounted(() => {
    getProject();
    getProjectTags();
    getRoles();
    getTags();
    getProjects();
  });

  return {
    // Reactive data
    project,
    projectUsers,
    searchTerm,
    selectedFilters,
    uploadProgress,
    isShowAvatarUpdateDialog,
    profileProgress,
    edit,
    loader,
    defaultImage,
    availableRoles,
    availableTags,
    availableProjects,
    
    // Computed properties
    filteredUsers,
    
    // Methods
    resetFilter,
    resetSearchTerm,
    updateSelectedFilters,
    deleteSelectedFilters,
    closeUploadedAvatarDialog,
    handleAvatarCropped,
    updateProjectField,
    onSubmit,
    getProjectTags,
    saveProject,
    getRoles,
    getProject,
    clearCache,
    back,
    getTags,
    getProjects
  };
};
