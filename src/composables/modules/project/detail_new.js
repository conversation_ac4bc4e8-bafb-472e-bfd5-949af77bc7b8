import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import makeProjectService from '@/services/api/project';
import makeRoleService from '@/services/api/role';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import { useProjectsManagement } from '@/composables/modules/project/view';
import Swal from 'sweetalert2';

export const useProjectDetail = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  // Reactive data
  const project = ref({});
  const projectUsers = ref([]);
  const searchTerm = ref('');
  const selectedFilters = ref([]);
  const uploadProgress = ref(0);
  const isShowAvatarUpdateDialog = ref(false);
  const profileProgress = ref(0);
  const edit = ref({
    name: null,
    description: null,
    status: null,
    tags: null,
    maxTestersPerCase: null,
    casePrefix: null,
    allowDuplicateCases: false,
    hideNumbersInCaseTitle: false
  });
  const loader = ref({
    projectTags: false,
    users: false,
    project: false,
    roles: false
  });
  const defaultImage = new URL('@/assets/png/project.png', import.meta.url).href;

  // Computed properties
  const filteredUsers = computed(() => {
    let filtered = projectUsers.value;
    
    if (searchTerm.value.length > 0) {
      filtered = filtered.filter((user) => 
        user.fullName?.toLowerCase().includes(searchTerm.value.toLowerCase())
      );
    }

    if (selectedFilters.value.length) {
      filtered = filtered.filter((user) => {
        for (let selectedFilter of selectedFilters.value) {
          if (user.role === selectedFilter.value) {
            return true;
          }
        }
        return false;
      });
    }

    return filtered;
  });

  // Methods
  const resetFilter = () => {
    selectedFilters.value = [];
  };

  const resetSearchTerm = () => {
    searchTerm.value = '';
  };

  const updateSelectedFilters = (filters) => {
    selectedFilters.value = filters;
  };

  const deleteSelectedFilters = (filterToDelete) => {
    selectedFilters.value = selectedFilters.value.filter(
      filter => filter.id !== filterToDelete.id
    );
  };

  const closeUploadedAvatarDialog = () => {
    isShowAvatarUpdateDialog.value = false;
  };

  const handleAvatarCropped = async (data) => {
    isShowAvatarUpdateDialog.value = false;
    profileProgress.value = 0;
    
    try {
      if (data) {
        profileProgress.value = 1;
        
        const apiService = makeProjectService();
        const formData = new FormData();
        formData.append('logo', dataURLtoFile(data.objectURL, 'avatar.png'));
        
        const response = await apiService.uploadLogo(route.params.id, formData, {
          onUploadProgress: (event) => {
            profileProgress.value = Math.round((event.loaded * 100) / event.total);
          }
        });
        
        showSuccessToast(Swal, 'Profile updated');
        project.value.logo = response.data.logo;
      }
    } catch (e) {
      profileProgress.value = 0;
      showErrorToast(Swal, 'Upload failed');
    }
  };

  const updateProjectField = async (field, value) => {
    if (edit.value[field] === null) {
      edit.value[field] = value;
      return;
    }
    
    try {
      const projectService = makeProjectService();
      await projectService.update(route.params.id, {
        [field]: value
      });
      
      project.value[field] = value;
      edit.value[field] = null;
      showSuccessToast(Swal, 'Project updated');
    } catch (error) {
      showErrorToast(Swal, 'Update failed');
    }
  };

  const onSubmit = () => {
    saveProject();
  };

  const getProjectTags = async () => {
    loader.value.projectTags = true;

    try {
      await store.dispatch('project/getTags');
      loader.value.projectTags = false;
    } catch (error) {
      loader.value.projectTags = false;
      showErrorToast(Swal, 'Failed to fetch tags');
    }
  };

  const saveProject = async () => {
    try {
      loader.value.project = true;

      const fields = {};
      for (const [key, value] of Object.entries(edit.value)) {
        if (value !== null) {
          fields[key] = value;
        }
      }

      if (Object.keys(fields).length) {
        const projectService = makeProjectService();
        await projectService.update(route.params.id, fields);

        // Update local project data
        Object.assign(project.value, fields);
        
        // Reset edit fields
        for (const key of Object.keys(fields)) {
          edit.value[key] = null;
        }
        
        showSuccessToast(Swal, 'Project saved');
      }

      loader.value.project = false;
    } catch (error) {
      loader.value.project = false;
      if (error?.response?.status === 422) {
        if (error?.response?.data?.errors) {
          for (const [key] of Object.entries(error?.response?.data?.errors)) {
            if (edit.value[key] !== null) {
              edit.value[key] = null;
            }
          }
        }
      }
      showErrorToast(Swal, 'Save failed');
    }
  };

  const getProject = async () => {
    loader.value.project = true;

    try {
      const projectService = makeProjectService();
      const response = await projectService.getById(route.params.id);

      project.value = response.data.project;
      projectUsers.value = response.data.members;

      // Initialize edit values
      for (const key of Object.keys(edit.value)) {
        edit.value[key] = project.value[key];
      }

      loader.value.project = false;
    } catch (error) {
      loader.value.project = false;
      showErrorToast(Swal, 'Failed to fetch project');
    }
  };

  const getRoles = async () => {
    loader.value.roles = true;
    
    try {
      const roleService = makeRoleService();
      await roleService.get();
      loader.value.roles = false;
    } catch (err) {
      loader.value.roles = false;
      showErrorToast(Swal, 'Failed to fetch roles');
    }
  };

  const clearCache = () => {
    const { clearCache: clearProjectsCache } = useProjectsManagement();
    clearProjectsCache();
  };

  const back = () => {
    router.push({
      name: 'ProjectsView'
    });
  };

  // Helper function
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Lifecycle
  onMounted(() => {
    getProject();
    getProjectTags();
    getRoles();
  });

  return {
    // Reactive data
    project,
    projectUsers,
    searchTerm,
    selectedFilters,
    uploadProgress,
    isShowAvatarUpdateDialog,
    profileProgress,
    edit,
    loader,
    defaultImage,
    
    // Computed properties
    filteredUsers,
    
    // Methods
    resetFilter,
    resetSearchTerm,
    updateSelectedFilters,
    deleteSelectedFilters,
    closeUploadedAvatarDialog,
    handleAvatarCropped,
    updateProjectField,
    onSubmit,
    getProjectTags,
    saveProject,
    getRoles,
    getProject,
    clearCache,
    back
  };
};
