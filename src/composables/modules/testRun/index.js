import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { useNetworkError } from '@/composables/utils/networkError';
import { usePermissions } from '@/composables/utils/permissions';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { useTestRunCount } from '@/composables/modules/testRun/count';
import { useRelations } from '@/composables/utils/relations';
import { runStateMap } from '@/constants/grid';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import Swal from 'sweetalert2';

import makePlanService from '@/services/api/plan';
import makeMilestonesService from '@/services/api/milestone';
import makeConfigurationService from '@/services/api/configuration';
import makeTagService from '@/services/api/tag';
import makeRunService from '@/services/api/run';

// Module-level state for caching test runs across component instances
const testRunsCache = ref([]);
const totalRowsCache = ref(0);
const lastFetchedPage = ref(0);
const lastFetchedFilter = ref('');
const lastFetchedSearch = ref('');
const lastFetchedHandle = ref('');
const lastFetchedProjectKey = ref('');
const lastFetchedApiFilters = ref(null);
const lastFetchedSortBy = ref([]);
const lastFetchedSortDesc = ref([]);
const hasInitiallyLoaded = ref(false);
// Pagination state
const currentPage = ref(1);
const perPage = ref(10);

// Table-specific loading state for search/filter operations
const tableLoading = ref(false);

// Backend sorting state
const sortBy = ref([]);
const sortDesc = ref([]);

export const useRunsIndex = (runViewType) =>
{
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  // Composables
  const { showSkeletonLoader, hideSkeletonLoader, isLoading: skeletonLoaderState } = useLoading();
  const { redirectOnError } = useNetworkError();
  const { authorityTo } = usePermissions();
  const { formatDate } = useDateFormatter();
  const { activeCount, archivedCount, getEntityCount } = useTestRunCount();
  const { relationsLoading, relationLoadingStates, fetchRunRelations, resetRelationLoadingStates, clearEntityTypeCache } = useRelations();

  // Services
  let planService;
  let runsService;
  let milestoneService;
  let configurationService;
  let tagService;

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, filterValue, searchValue, handleValue, projectKeyValue, apiFiltersValue, sortByValue, sortDescValue) =>
  {
    // Always fetch if context changed
    if (filterValue !== lastFetchedFilter.value ||
      searchValue !== lastFetchedSearch.value ||
      handleValue !== lastFetchedHandle.value ||
      projectKeyValue !== lastFetchedProjectKey.value ||
      JSON.stringify(apiFiltersValue) !== JSON.stringify(lastFetchedApiFilters.value) ||
      JSON.stringify(sortByValue) !== JSON.stringify(lastFetchedSortBy.value) ||
      JSON.stringify(sortDescValue) !== JSON.stringify(lastFetchedSortDesc.value)) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = testRunsCache.value.length;

    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalRowsCache.value;
  };

  // Helper function to clear cache
  const clearCache = () =>
  {
    testRunsCache.value = [];
    totalRowsCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    lastFetchedApiFilters.value = null;
    lastFetchedSortBy.value = [];
    lastFetchedSortDesc.value = [];
    hasInitiallyLoaded.value = false;
    clearEntityTypeCache('run');
  };

  // Data properties
  const planName = ref('');
  const displayedRuns = ref('all');
  const appliedFilters = ref({});
  const apiFilters = ref(null);
  const createButtonLoading = ref(false);
  const isCustomizeDisplayed = ref(false);
  const selectedRun = ref({});
  const selectedStatus = ref(Object.values(runStateMap)[0]);
  const savingInProgress = ref(false);
  const headers = ref([]);
  const expanded = ref([]);
  const searchFilter = ref('');
  const menuOpen = ref(false);
  const addToMilestoneDialog = ref(false);
  const addToTestPlansDialog = ref(false);
  const duplicateAndApplyConfigDialog = ref(false);
  const duplicateType = ref('');
  const openedRow = ref(undefined);
  const loading = ref(false);
  const checked = ref(false);
  const selecteditems = ref([]);
  const itemKey = ref('uid');
  const filter = ref('ongoing');
  const options = ref(['ongoing', 'archived']);
  const clearSelection = ref(false);
  const isFilter = ref(false);
  const showConfirmDialog = ref(false);
  const ConfirmDialog_Title = ref('');
  const ConfirmDialog_Content = ref('');
  const ConfirmDialog_Contentpart_two = ref('');
  const ConfirmDialog_Items = ref([]);
  const ConfirmDialog_btn_label = ref('');
  const ConfirmDialog_btn_color = ref('primary');
  const ConfirmDialog_RunName = ref('');
  const ConfirmType = ref('');
  const isProjectMenuCollapsed = ref(false);
  const rowClass = ref(() => 'project-item');
  const isToPlanExpanded = ref(false);
  const plans = ref([]);
  const milestones = ref([]);
  const configurations = ref([]);
  const tags = ref([]);
  const statuses = ref([]);
  const priorities = ref([]);
  const buttonLoading = ref(false);
  const buttonLoadingItems = ref(false);
  const globalConfiguration = ref({});
  const menuDueDate = ref(false);
  const menuDueDateParent = ref(false);
  const getDueDate = ref('');
  const loadingConfigurations = ref(false);
  const lastPage = ref(0);



  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const readEntity = computed(() => authorityTo('read_entity'));

  const currentAccount = computed(() => store.state.user.currentAccount);
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);
  const queryParams = computed(() => route.query);
  const currentView = computed(() => queryParams.value.view || 'list');

  const filteredHeaders = computed(() =>
  {
    const filtered = headers.value?.filter((header) => header.checked) || [];
    return filtered;
  });

  const filteredRuns = computed(() =>
  {
    // During initial load, return empty array to prevent "No matching results" from showing
    if (!hasInitiallyLoaded.value && testRunsCache.value.length === 0) {
      return [];
    }
    // Return the current page slice of cached test runs
    const startIndex = (currentPage.value - 1) * perPage.value;
    const endIndex = startIndex + perPage.value;
    return testRunsCache.value.slice(startIndex, endIndex);
  });

  const isDuplicateRunViewType = computed(() =>
  {
    return runViewType === 'Duplicate';
  });

  const isSelected = computed(() =>
  {
    return selecteditems.value.length > 0;
  });

  const hasOneSelectedItem = computed(() =>
  {
    return selecteditems.value.length === 1;
  });

  const runs = computed(() =>
  {
    return testRunsCache.value?.map((item) =>
    {
      return {
        ...item,
      };
    }) || [];
  });

  const selectedProjectKey = computed(() =>
  {
    return route.params.key;
  });

  const footerProps = computed(() =>
  {
    return {
      itemsPerPageOptions: [15, 50],
      showCurrentPage: true,
      showFirstLastPage: true,
      hideDefaultFooter: true,
    };
  });

  const isFilterArchived = computed(() =>
  {
    return filter.value === 'archived';
  });

  const activeMilestones = computed(() =>
  {
    return milestones.value?.filter(
      (milestone) => !milestone?.archivedAt && !milestone?.deletedAt
    ) || [];
  });

  const getActivePlans = computed(() =>
  {
    return plans.value?.filter((plan) => !plan.archivedAt && !plan.deletedAt) || [];
  });

  const getActivePlansCount = computed(() =>
  {
    return plans.value?.filter((plan) => plan.archivedAt == null)?.length || 0;
  });

  const configurationSorted = computed(() =>
  {
    return configurations.value || [];
  });

  const totalPages = computed(() =>
  {
    return Math.ceil(totalRowsCache.value / perPage.value);
  });

  const isProjectArchived = computed(() =>
  {
    // This would need to be implemented based on your project status logic
    return false;
  });

  // Create debounced search method
  let debouncedRefreshData;

  // Methods
  const init = async () =>
  {
    try {
      showSkeletonLoader();
      let handle = route.params.handle;
      const projectKey = route.params.key;
      await Promise.all([
        getProjectRun({
          handle,
          projectKey,
          perPage: perPage.value,
          currentPage: currentPage.value
        }),
        getEntityCount(handle, projectKey)
      ]);
      getMilestones();
      getTestPlans();
      getConfigurations();
      getTags();
    } catch (error) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'test runs' },
        error?.response?.data
      );
    } finally {
      hideSkeletonLoader();
    }
  };

  const updateFilter = (newFilter) =>
  {
    filter.value = newFilter;
    selecteditems.value = [];
    currentPage.value = 1;
    tableLoading.value = true;

    clearCache();

    refreshData();
  };

  const onUpdatePagination = (options) =>
  {
    const newPage = options.page || currentPage.value;
    const newItemsPerPage = options.itemsPerPage || perPage.value;
    const newSortBy = options.sortBy || [];
    const newSortDesc = options.sortDesc || [];

    // Check if anything actually changed
    const pageChanged = newPage !== currentPage.value;
    const itemsPerPageChanged = newItemsPerPage !== perPage.value;
    const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(sortBy.value);
    const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(sortDesc.value);

    // Update state
    currentPage.value = newPage;
    perPage.value = newItemsPerPage;
    sortBy.value = newSortBy;
    sortDesc.value = newSortDesc;

    // Only trigger API call if something actually changed
    if (pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) {
      refreshData(false);
    }
  };

  const refreshData = async () =>
  {
    let handle = currentAccount.value.handle;
    const projectKey = route.params.key;
    await Promise.all([
      getProjectRun({ handle, projectKey, perPage: perPage.value, currentPage: currentPage.value }),
    ]);
    getEntityCount(handle, projectKey);
  };

  const getProjectRun = async ({ handle, projectKey, perPage, currentPage, forceRefresh = false }) =>
  {
    if (!runsService) {
      return;
    }

    try {
      const currentFilter = filter.value;
      const currentSearch = searchFilter.value?.trim() || '';
      const currentApiFilters = apiFilters.value;
      const currentSortBy = sortBy.value;
      const currentSortDesc = sortDesc.value;
      const pageNumber = currentPage || 1;
      const pageSize = perPage || 10;

      // Check if we need to fetch new data
      if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFilter, currentSearch, handle, projectKey, currentApiFilters, currentSortBy, currentSortDesc)) {
        // We have enough cached data, no need to fetch
        return;
      }

      // If context changed, clear cache and start fresh
      const contextChanged = currentFilter !== lastFetchedFilter.value ||
        currentSearch !== lastFetchedSearch.value ||
        handle !== lastFetchedHandle.value ||
        projectKey !== lastFetchedProjectKey.value ||
        JSON.stringify(currentApiFilters) !== JSON.stringify(lastFetchedApiFilters.value) ||
        JSON.stringify(currentSortBy) !== JSON.stringify(lastFetchedSortBy.value) ||
        JSON.stringify(currentSortDesc) !== JSON.stringify(lastFetchedSortDesc.value);

      if (contextChanged || forceRefresh) {
        clearCache();
      }

      // Calculate what data we need to fetch
      const startIndex = contextChanged ? 0 : testRunsCache.value.length;
      const offset = startIndex;
      const limit = contextChanged ? pageNumber * pageSize : pageSize;

      const queryParams = {
        limit: limit,
        offset: offset,
        ...(currentFilter === 'archived' ? { archived: true } : {})
      }

      // Add backend sorting parameters
      if (currentSortBy && currentSortBy.length > 0) {
        const frontendSortColumn = currentSortBy[0]; // Take first sort column
        const sortDirection = currentSortDesc[0] ? 'desc' : 'asc';

        // Map frontend column names to API field names (case sensitive)
        const sortColumnMapping = {
          'name': 'name',
          'creationdate': 'createdAt',
          'duedate': 'dueAt'
        };

        const apiSortColumn = sortColumnMapping[frontendSortColumn] || frontendSortColumn;

        queryParams.orderBy = apiSortColumn;
        queryParams.order = sortDirection;
      }

      // Add search query if present
      if (currentSearch) {
        queryParams.q = currentSearch;
      }

      // Apply advanced filters
      if (currentApiFilters) {
        Object.entries(currentApiFilters).forEach(([key, value]) =>
        {
          if (Array.isArray(value) && value.length === 0) return;
          if (value === '') return;
          queryParams[key] = value;
        });
      }

      const response = await runsService.getRuns(handle, projectKey, queryParams);
      const newRuns = response.data?.items || [];

      if (contextChanged || forceRefresh) {
        // Replace cache with new data
        testRunsCache.value = newRuns;
      } else {
        // Append new data to cache
        testRunsCache.value.push(...newRuns);
      }

      totalRowsCache.value = response?.data.count || 0;

      // Update tracking variables
      lastFetchedPage.value = Math.ceil(testRunsCache.value.length / pageSize);
      lastFetchedFilter.value = currentFilter;
      lastFetchedSearch.value = currentSearch;
      lastFetchedHandle.value = handle;
      lastFetchedProjectKey.value = projectKey;
      lastFetchedApiFilters.value = currentApiFilters;
      lastFetchedSortBy.value = currentSortBy;
      lastFetchedSortDesc.value = currentSortDesc;
      hasInitiallyLoaded.value = true;

      // Fetch relations for the returned runs
      if (testRunsCache.value.length > 0) {
        // Reset relation loading states before fetching
        resetRelationLoadingStates();

        fetchRunRelations(
          runsService,
          handle,
          projectKey,
          testRunsCache.value
        ).catch(error =>
        {
          showErrorToast(Swal, 'fetchError', { item: 'runs relation' }, error?.response?.data);
        });
      }
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      testRunsCache.value = [];
      totalRowsCache.value = 0;
    } finally {
      tableLoading.value = false;
    }
  };

  const getMilestones = async () =>
  {
    if (!readEntity.value) return;
    if (!milestoneService) {
      return
    }

    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await milestoneService.getMilestones(handle, projectKey);
      milestones.value = response.data?.items;
      return response.data?.items;
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'milestones' },
        err?.response?.data
      );
      return [];
    }
  };

  const getTestPlans = async () =>
  {
    if (!readEntity.value) {
      return;
    }
    if (!planService) {
      return
    }

    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await planService.getPlans(
        handle,
        projectKey,
        1000,
        0
      );
      plans.value = response.data?.items;
      return response.data?.items;
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'test plans' },
        err?.response?.data
      );
      return [];
    }
  };

  const getConfigurations = async (perPage = 10, offset = 1) =>
  {
    if (!configurationService) {
      return
    }

    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      loadingConfigurations.value = true;
      const response = await configurationService.getConfigurations(
        handle,
        projectKey,
        perPage,
        offset,
        'name', // Sort by name
        'asc'   // Ascending order
      );
      configurations.value = response.data?.configurations;
      lastPage.value = response.data?.last_page;
      return response.data?.configurations;
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
      return [];
    } finally {
      loadingConfigurations.value = false;
    }
  };

  const getTags = async () =>
  {
    if (!tagService) {
      return
    }

    const handle = route.params.handle;
    try {
      const response = await tagService.getTags(handle, 'runs');
      tags.value = response.data;
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'tags' }, err?.response?.data);
      return
    }
  };

  const convertToLocal = (timestamp) =>
  {
    return dayjs(timestamp).format('ddd, MMM DD hh:mm A');
  };

  const determineType = (test) =>
  {
    return test.source === 'pinata' ? 'Exploratory' : 'Manual';
  };

  const setselected = (selectedItems) =>
  {
    clearSelection.value = false;
    selecteditems.value = selectedItems;
  };

  const handleFilters = (filters) =>
  {
    appliedFilters.value = filters;

    if (filters && filters.ui && filters.api) {
      appliedFilters.value = filters.ui;
      apiFilters.value = filters.api;
    } else {
      apiFilters.value = extractApiFilters(filters);
    }

    isFilter.value = !!filters;
    currentPage.value = 1; // Reset to first page when filters are applied
    refreshData();
  };

  const extractApiFilters = (uiFilters) =>
  {
    if (!uiFilters) return null;
    const apiFilters = {};

    Object.entries(uiFilters).forEach(([key, filter]) =>
    {
      if (
        filter.type === 'array' &&
        Array.isArray(filter.value) &&
        filter.value.length
      ) {
        const ids = filter.value
          .map((item) => item.uid ?? item.id)
          .filter((id) => id != null);

        if (ids.length) {
          switch (key) {
            case 'panelPriority':
              apiFilters.priorityUids = ids;
              break;
            case 'panelStatus':
              apiFilters.statusUids = ids;
              break;
            case 'panelMilestone':
              apiFilters.milestoneUids = ids;
              break;
            case 'panelTag':
              apiFilters.tagUids = ids;
              break;
            case 'panelConfigurations':
              apiFilters.configUids = ids;
              break;
          }
        }
      } else if (filter.type === 'range' && Array.isArray(filter.value)) {
        const [min, max] = filter.value;
        if (key === 'testCasesRange') {
          if (min > 0) apiFilters.minTestCases = min;
          if (max < 100) apiFilters.maxTestCases = max;
        } else if (key === 'progressRange') {
          if (min > 0) apiFilters.minProgress = min;
          if (max < 100) apiFilters.maxProgress = max;
        }
      } else if (
        filter.type === 'dateRange' &&
        filter.value &&
        (filter.value.start || filter.value.end)
      ) {
        const { start, end } = filter.value;
        if (start) apiFilters.minCreatedAt = start;
        if (end) apiFilters.maxCreatedAt = end;
      }
    });

    return apiFilters;
  };

  const clearFilters = async () =>
  {
    appliedFilters.value = null;
    isFilter.value = false;
    currentPage.value = 1; // Reset to first page when clearing filters
    tableLoading.value = true;
    clearCache();
    await refreshData();
  };

  const applyFilters = (filters) =>
  {
    const hasAny = filters && Object.keys(filters).length > 0;
    if (hasAny) {
      if (filters.ui && filters.api) {
        appliedFilters.value = filters.ui;
        apiFilters.value = filters.api;
      } else {
        appliedFilters.value = filters;
        apiFilters.value = {};
      }
      isFilter.value = true;
    } else {
      appliedFilters.value = {};
      apiFilters.value = {};
      isFilter.value = false;
    }
    currentPage.value = 1; // Reset to first page when filters are applied
    tableLoading.value = true;

    clearCache();
    refreshData();
  };

  // Initialize services and headers
  const initializeServices = () =>
  {
    planService = makePlanService($api);
    runsService = makeRunService($api);
    milestoneService = makeMilestonesService($api);
    configurationService = makeConfigurationService($api);
    tagService = makeTagService($api);
  };

  const initializeHeaders = () =>
  {
    if (!dynamicHeaders.value.run) {
      store.dispatch('headers/initializeHeaders', { type: 'run' });
    }
    headers.value = dynamicHeaders.value.run;
  };

  const getStatuses = (entity) =>
  {
    return store.getters['user/getStatuses'] ? store.getters['user/getStatuses'](entity) : [];
  };

  const getPriorities = (entity) =>
  {
    return store.getters['user/getPriorities'] ? store.getters['user/getPriorities'](entity) : [];
  };



  watch(searchFilter, () =>
  {
    tableLoading.value = true;
    // Reset to first page when searching
    currentPage.value = 1;
    debouncedRefreshData();
  });

  // Lifecycle hooks
  onMounted(async () =>
  {
    initializeServices();
    initializeHeaders();

    // Create debounced search method
    debouncedRefreshData = debounce(refreshData, 300);

    await init();
    priorities.value = getPriorities('testRun');
    statuses.value = getStatuses('testRun');
  });

  return {
    // Reactive state
    planName,
    displayedRuns,
    appliedFilters,
    apiFilters,
    createButtonLoading,
    isCustomizeDisplayed,
    selectedRun,
    selectedStatus,
    savingInProgress,
    headers,
    expanded,
    searchFilter,
    menuOpen,
    addToMilestoneDialog,
    addToTestPlansDialog,
    duplicateAndApplyConfigDialog,
    duplicateType,
    openedRow,
    loading,
    checked,
    selecteditems,
    itemKey,
    filter,
    options,
    clearSelection,
    isFilter,
    showConfirmDialog,
    ConfirmDialog_Title,
    ConfirmDialog_Content,
    ConfirmDialog_Contentpart_two,
    ConfirmDialog_Items,
    ConfirmDialog_btn_label,
    ConfirmDialog_btn_color,
    ConfirmDialog_RunName,
    ConfirmType,
    isProjectMenuCollapsed,
    rowClass,
    isToPlanExpanded,
    plans,
    milestones,
    configurations,
    tags,
    items: testRunsCache, // Return cached test runs
    statuses,
    priorities,
    buttonLoading,
    buttonLoadingItems,
    globalConfiguration,
    menuDueDate,
    menuDueDateParent,
    getDueDate,
    loadingConfigurations,
    lastPage,
    totalRows: totalRowsCache, // Return cached total rows
    currentPage,
    perPage,
    tableLoading,
    sortBy,
    sortDesc,
    skeletonLoaderState,

    // Computed properties
    writeEntity,
    deleteEntity,
    readEntity,
    currentAccount,
    dynamicHeaders,
    queryParams,
    currentView,
    filteredHeaders,
    filteredRuns,
    isDuplicateRunViewType,
    isSelected,
    hasOneSelectedItem,
    runs,
    selectedProjectKey,
    footerProps,
    isFilterArchived,
    activeMilestones,
    getActivePlans,
    getActivePlansCount,
    configurationSorted,
    totalPages,
    isProjectArchived,

    // From other composables
    activeCount,
    archivedCount,
    formatDate,
    relationsLoading,
    relationLoadingStates,

    // Methods
    init,
    updateFilter,
    onUpdatePagination,
    refreshData,
    getProjectRun,
    getMilestones,
    getTestPlans,
    getConfigurations,
    getTags,
    convertToLocal,
    determineType,
    setselected,
    handleFilters,
    extractApiFilters,
    clearFilters,
    applyFilters,
    showSkeletonLoader,
    hideSkeletonLoader,
    redirectOnError,
    initializeHeaders,
    getStatuses,
    getPriorities,
    clearCache, // Expose cache clearing method

    // Dialog handlers
    handleAddToMilestoneDialog,
    onCloseAddToMilestoneDialog,
    handleAddToTestPlansDialog,
    onCloseAddToTestPlansDialog,
    handleDuplicateAndApplyConfigDialog,
    onCloseDuplicateAndApplyConfigDialog,

    // CRUD operations
    handleConfirmDialog,
    handleConfirmBtnClick,
    handleCloseClick,
    confirmArchiveRun,
    confirmUnArchiveRun,
    confirmDeleteRun,
    handleConfirmOneArchiveClick,
    handleConfirmArchiveClick,
    handleConfirmDeleteClick,
    handleConfirmDeleteMultiClick,
    handleAddMilestone,
    handleConfirmTestPlanClick,
    handleDuplicateAndApplyConfig,

    // Date and misc handlers
    handleDueDateClick,
    onDateChange,
    editItem,
    handleRowClick,
    onClickPlanActive,
    reloadTestRuns,
    planCreated,
    createTestPlan,

    // Configuration management
    updateConfiguration,
    addConfiguration,
    addConfigurationItems,
    deleteConfigurationGroup,
    deleteConfigurationItem,
    editConfigurationGroup,

    // Project menu handlers
    toggleProjectMenu,
    searchCollapsedMenu,
    unlinkedCollapsedMenu,
    linkedCollapsedMenu,
    onToPlanExpanded,
    hasInitiallyLoaded,
  };

  // Additional methods that need to be implemented
  function handleAddToMilestoneDialog(item)
  {
    selecteditems.value = Array.isArray(item) ? item : [item];
    addToMilestoneDialog.value = true;
  }

  function onCloseAddToMilestoneDialog()
  {
    addToMilestoneDialog.value = false;
  }

  function handleAddToTestPlansDialog(item)
  {
    selecteditems.value = Array.isArray(item) ? item : [item];
    addToTestPlansDialog.value = true;
  }

  function onCloseAddToTestPlansDialog()
  {
    addToTestPlansDialog.value = false;
  }

  function handleDuplicateAndApplyConfigDialog(data)
  {
    selecteditems.value = Array.isArray(data.items) ? data.items : [data.items];
    duplicateType.value = data.type;
    duplicateAndApplyConfigDialog.value = true;
  }

  function onCloseDuplicateAndApplyConfigDialog()
  {
    duplicateAndApplyConfigDialog.value = false;
  }

  async function handleConfirmDialog(type)
  {
    showConfirmDialog.value = true;
    switch (type) {
      case 'multi_archive':
        ConfirmDialog_Title.value = 'Archive Test Runs';
        ConfirmDialog_Content.value = 'Are you sure you want to archive these test runs?';
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Archive';
        ConfirmDialog_btn_color.value = 'primary';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'multi_archive';
        break;
      case 'multi_unarchive':
        ConfirmDialog_Title.value = 'Unarchive Test Runs';
        ConfirmDialog_Content.value = 'Are you sure you want to unarchive these test runs?';
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Unarchive';
        ConfirmDialog_btn_color.value = 'primary';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'multi_unarchive';
        break;
      case 'multi_delete':
        ConfirmDialog_Title.value = 'Delete Test Runs';
        ConfirmDialog_Content.value = 'Are you sure you want to delete these test runs?';
        ConfirmDialog_Contentpart_two.value = filter.value == 'ongoing' ? 'This action cannot be undone.' : '';
        ConfirmDialog_btn_label.value = 'Delete';
        ConfirmDialog_btn_color.value = 'danger';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'multi_delete';
        break;
      case 'archive':
        ConfirmDialog_Title.value = 'Archive Test Run';
        ConfirmDialog_Content.value = 'Are you sure you want to archive this test run?';
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Archive';
        ConfirmDialog_btn_color.value = 'primary';
        ConfirmDialog_RunName.value = selectedRun.value.name;
        ConfirmType.value = 'archive';
        break;
      case 'unarchive':
        ConfirmDialog_Title.value = 'Unarchive Test Run';
        ConfirmDialog_Content.value = 'Are you sure you want to unarchive this test run?';
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Unarchive';
        ConfirmDialog_btn_color.value = 'primary';
        ConfirmDialog_RunName.value = selectedRun.value.name;
        ConfirmType.value = 'unarchive';
        break;
      case 'delete':
        ConfirmDialog_Title.value = 'Delete Test Run';
        ConfirmDialog_Content.value = 'Are you sure you want to delete this test run?';
        ConfirmDialog_Contentpart_two.value = filter.value == 'ongoing' ? 'This action cannot be undone.' : '';
        ConfirmDialog_btn_label.value = 'Delete';
        ConfirmDialog_btn_color.value = 'danger';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'delete';
        break;
      case 'multi_milestone':
        ConfirmDialog_Title.value = 'Link to Milestone';
        ConfirmDialog_Content.value = 'Select milestone to link these test runs to:';
        ConfirmDialog_Items.value = await getMilestones();
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Link';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'multi_milestone';
        ConfirmDialog_btn_color.value = 'primary';
        break;
      case 'multi_testplan':
        ConfirmDialog_Title.value = 'Link to Test Plan';
        ConfirmDialog_Content.value = 'Select test plan to link these test runs to:';
        ConfirmDialog_Items.value = await getTestPlans();
        ConfirmDialog_Contentpart_two.value = '';
        ConfirmDialog_btn_label.value = 'Link';
        ConfirmDialog_RunName.value = '';
        ConfirmType.value = 'multi_testplan';
        ConfirmDialog_btn_color.value = 'primary';
        break;
    }
  }

  function handleConfirmBtnClick(type, items)
  {
    switch (type) {
      case 'multi_archive':
        handleConfirmArchiveClick(true);
        break;
      case 'multi_unarchive':
        handleConfirmArchiveClick(false);
        break;
      case 'multi_delete':
        handleConfirmDeleteMultiClick();
        break;
      case 'archive':
        handleConfirmOneArchiveClick(true);
        break;
      case 'unarchive':
        handleConfirmOneArchiveClick(false);
        break;
      case 'delete':
        handleConfirmDeleteClick();
        break;
      case 'multi_milestone':
        handleConfirmMilestoneClick(items);
        break;
      case 'multi_testplan':
        handleConfirmTestPlanClick(items);
        break;
    }
  }

  function handleCloseClick()
  {
    showConfirmDialog.value = false;
    selectedRun.value = [];
    selecteditems.value = [];
    ConfirmDialog_Items.value = [];
    clearSelection.value = true;
    addToMilestoneDialog.value = false;
    addToTestPlansDialog.value = false;
    duplicateAndApplyConfigDialog.value = false;
  }

  function confirmArchiveRun(item)
  {
    selectedRun.value = item;
    handleConfirmDialog('archive');
  }

  function confirmUnArchiveRun(item)
  {
    selectedRun.value = item;
    handleConfirmDialog('unarchive');
  }

  function confirmDeleteRun(item)
  {
    selectedRun.value = item;
    handleConfirmDialog('delete');
  }

  function editItem(item)
  {
    selectedRun.value = item;
    const currentFolderUid = route.params.folderUid;
    router.push({
      name: 'TestRunEdit',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: item.uid,
        folderUid: currentFolderUid
      },
    });

  }

  function handleRowClick(item)
  {
    router.push({
      name: 'TestRunCaseEdit',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: item.uid,
        folderUid: 1,
      },
    });
  }

  async function onClickPlanActive(planId)
  {
    if (planId != displayedRuns.value) {
      displayedRuns.value = planId;
      if (planId === 'all') {
        await reloadTestRuns();
      } else {
        const params = {
          planUid: planId,
        };
        await reloadTestRuns(params);
      }
    }
  }

  async function reloadTestRuns(params)
  {
    if (!runsService) {
      return;
    }

    const handle = route.params.handle;
    try {
      const response = await runsService.getRuns(
        handle,
        route.params.key,
        {
          limit: 10,
          offset: 0,
          ...params
        }
      );
      // Clear cache and update with new data
      testRunsCache.value = response.data.items;

      // Fetch relations for reloaded runs
      if (testRunsCache.value.length > 0) {
        // Reset relation loading states before fetching
        resetRelationLoadingStates();

        fetchRunRelations(
          runsService,
          handle,
          route.params.key,
          testRunsCache.value
        ).catch(error =>
        {
          showErrorToast(Swal, 'fetchError', { item: 'runs relation' }, error?.response?.data);
        });
      }
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'runs' }, err?.response?.data);
    }
  }

  async function planCreated(planName)
  {
    planName.value = planName;
    await createTestPlan();
  }

  async function createTestPlan()
  {
    if (!isFilterArchived.value) {
      const payload = {
        name: planName.value,
        source: 'testfiesta',
        runs: [],
        customFields: {
          archived: false,
        },
      };

      try {
        createButtonLoading.value = true;
        const response = await planService.createTestPlan(
          route.params.handle,
          route.params.key,
          payload
        );
        if (response.status === 200) {
          const newPlan = response.data;
          if (newPlan) {
            plans.value?.push({ ...newPlan, runs: [] });
          }
          planName.value = '';
        }
        showSuccessToast(Swal, 'createSuccess', { item: 'Test plan' });
      } catch (err) {
        showErrorToast(
          Swal,
          err.response?.data?.message || 'Internal server error'
        );
      } finally {
        createButtonLoading.value = false;
      }
    }
  }

  async function handleDueDateClick()
  {
    buttonLoading.value = true;
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const uids = Array.isArray(selecteditems.value)
      ? selecteditems.value.map((item) => item.uid)
      : [selecteditems.value.uid];
    const payload = {
      uids: uids,
      action: 'updateDueDate',
      milestoneUids: [],
      planUids: [],
    };
    getDueDate.value != '' && (payload.dueAt = getDueDate.value);
    try {
      const response = await runsService.updateTestRuns(handle, projectKey, payload);
      if (response.status === 200) {
        showSuccessToast(Swal, 'updateSuccess', { item: 'Due date' });
        reloadTestRuns({ planUid: displayedRuns.value });
      }
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'Due date' },
        err?.response?.data
      );
    } finally {
      buttonLoading.value = false;
      menuOpen.value = false;
      menuDueDate.value = false;
    }
  }

  function onDateChange()
  {
    menuDueDate.value = false;
  }

  async function handleConfirmOneArchiveClick(status)
  {
    const payload = {
      customFields: {
        ...selectedRun.value.customFields,
      },
      archive: status,
      name: selectedRun.value.name,
    };

    try {
      showSkeletonLoader();
      await runsService.updateTestRun(
        route.params.handle,
        route.params.key,
        selectedRun.value.uid,
        payload
      );
      currentPage.value = 1;
      clearCache();
      await refreshData();
      showSuccessToast(Swal, status ? 'archiveSuccess' : 'unarchiveSuccess', {
        item: 'Test run',
      });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(
        Swal,
        status ? 'archiveError' : 'unarchiveError',
        { item: 'Test run' },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  }

  async function handleConfirmArchiveClick(status)
  {
    const uids = Array.isArray(selecteditems.value)
      ? selecteditems.value.map((item) => item.uid)
      : [selecteditems.value.uid];
    const action = status ? 'archive' : 'unarchive';
    let payload = {
      uids: uids,
      milestoneUids: [],
      planUids: [],
      dueAt: new Date(),
      action: action,
    };

    try {
      showSkeletonLoader();
      await runsService.updateTestRuns(
        route.params.handle,
        route.params.key,
        payload
      );

      currentPage.value = 1;
      clearCache();

      // Refresh data and counts
      await refreshData();
      showSuccessToast(Swal, 'archiveSuccess', { item: 'Test run' });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(
        Swal,
        'archiveError',
        { item: 'Test run' },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  }

  async function handleConfirmDeleteClick()
  {
    try {
      showSkeletonLoader();
      await runsService.deleteTestRuns(
        route.params.handle,
        route.params.key,
        {
          runUids: [selectedRun.value.uid],
        }
      );
      currentPage.value = 1;
      clearCache();
      await refreshData();
      showSuccessToast(Swal, 'deleteSuccess', { item: 'Test run' });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(
        Swal,
        'deleteError',
        { item: 'Test run' },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  }

  async function handleConfirmDeleteMultiClick()
  {
    var runUids = [];
    selecteditems.value.map((item) =>
    {
      runUids = [...runUids, item.uid];
    });

    try {
      showSkeletonLoader();
      await runsService.deleteTestRuns(
        route.params.handle,
        route.params.key,
        { runUids: runUids }
      );
      currentPage.value = 1;
      clearCache();
      await refreshData();
      showSuccessToast(Swal, 'deleteSuccess', { item: 'Test runs' });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(
        Swal,
        'deleteError',
        { item: 'Test runs' },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  }

  async function handleAddMilestone(items)
  {
    const uids = Array.isArray(selecteditems.value)
      ? selecteditems.value.map((item) => item.uid)
      : [selecteditems.value.uid];

    const handle = route.params.handle;
    const key = route.params.key;

    const requests = [];
    const actionLabels = [];

    if (uids.length <= 1) {
      if (items.addedMilestoneUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            milestoneUids: items.addedMilestoneUids,
            action: 'addMilestones',
          })
        );
        actionLabels.push('linkedSuccess');
      }

      if (items.removedMilestoneUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            milestoneUids: items.removedMilestoneUids,
            action: 'removeMilestones',
          })
        );
        actionLabels.push('unlinkedSuccess');
      }
    } else {
      const activeMilestoneUids = activeMilestones.value.map(
        (milestone) => milestone.uid
      );
      const addedMilestoneUids = activeMilestoneUids.filter((uid) =>
        items.selectedMilestoneUids.includes(uid)
      );
      const removedMilestoneUids = activeMilestoneUids.filter(
        (uid) => !items.selectedMilestoneUids.includes(uid)
      );

      if (addedMilestoneUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            milestoneUids: addedMilestoneUids,
            action: 'addMilestones',
          })
        );
        actionLabels.push('linkedSuccess');
      }

      if (removedMilestoneUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            milestoneUids: removedMilestoneUids,
            action: 'removeMilestones',
          })
        );
        actionLabels.push('unlinkedSuccess');
      }
    }

    if (requests.length === 0) return;

    try {
      showSkeletonLoader();
      const results = await Promise.allSettled(requests);

      results.forEach((result, index) =>
      {
        const actionKey = actionLabels[index];

        if (result.status === 'fulfilled') {
          showSuccessToast(Swal, actionKey, {
            item: 'testRunsToMilestone',
          });
        } else {
          showErrorToast(
            Swal,
            actionKey === 'linkedSuccess' ? 'linkedError' : 'unlinkedError',
            { item: 'testRunsToMilestone' },
            result.reason?.response?.data
          );
        }
      });
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
      clearCache();
      refreshData();
    }
  }

  async function handleConfirmTestPlanClick(items)
  {
    const uids = Array.isArray(selecteditems.value)
      ? selecteditems.value.map((item) => item.uid)
      : [selecteditems.value.uid];

    const handle = route.params.handle;
    const key = route.params.key;

    const requests = [];
    const actionLabels = [];

    if (uids.length <= 1) {
      if (items.addedPlanUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            planUids: items.addedPlanUids,
            action: 'addPlans',
          })
        );
        actionLabels.push('linkedWithSuccess');
      }

      if (items.removedPlanUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            planUids: items.removedPlanUids,
            action: 'removePlans',
          })
        );
        actionLabels.push('unlinkedWithSuccess');
      }
    } else {
      const addedPlanUids = items.addedPlanUids;
      const removedPlanUids = items.removedPlanUids;

      if (addedPlanUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            planUids: items.addedPlanUids,
            action: 'addPlans',
          })
        );
        actionLabels.push('linkedWithSuccess');
      }

      if (removedPlanUids?.length) {
        requests.push(
          runsService.updateTestRuns(handle, key, {
            uids,
            planUids: items.removedPlanUids,
            action: 'removePlans',
          })
        );
        actionLabels.push('unlinkedWithSuccess');
      }
    }

    if (requests.length === 0) return;

    try {
      showSkeletonLoader();
      const results = await Promise.allSettled(requests);

      results.forEach((result, index) =>
      {
        const actionKey = actionLabels[index];

        if (result.status === 'fulfilled') {
          showSuccessToast(Swal, actionKey, {
            item1: `${selecteditems.value?.length} Test Runs`,
            item2: `${actionKey === 'linkedWithSuccess' ? items.addedPlanUids?.length : items.removedPlanUids?.length} Test Plans`
          });
        } else {
          showErrorToast(
            Swal,
            actionKey === 'linkedSuccess' ? 'linkedError' : 'unlinkedError',
            { item: 'testRunsToPlan' },
            result.reason?.response?.data
          );
        }
      });
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
      refreshData();
    }
  }

  async function handleDuplicateAndApplyConfig(items)
  {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    let runUids = selecteditems.value?.map((item) => ({ uid: item.uid })) || [];
    const payload = { testRuns: runUids };

    if (items.sets.length > 0) {
      if (duplicateType.value == 'specificDuplicate') {
        runUids.forEach((item) => (item.configuration = items));
      } else {
        payload.configuration = items;
      }
    }

    try {
      await runsService.duplicateTestRun(handle, projectKey, payload);
      showSuccessToast(Swal, 'addSuccess', { item: 'Duplicate run created' });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(
        Swal,
        'addError',
        { item: 'Error in creating duplicating run' },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
    }
    refreshData();
  }

  async function updateConfiguration(uid, name, options)
  {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    try {
      await configurationService.updateConfiguration(handle, projectKey, uid, {
        name: name,
        options: options,
      });
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    }
  }

  async function addConfiguration(configuration, currentPage = 1)
  {
    buttonLoading.value = true;
    try {
      await configurationService.createConfigurations(
        route.params.handle,
        route.params.key,
        {
          name: configuration.newConfigurationName,
          options: configuration.options,
        }
      );
      await getConfigurations(currentPage, 1);
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    } finally {
      buttonLoading.value = false;
    }
  }

  async function addConfigurationItems(item, currentPage = 1)
  {
    buttonLoadingItems.value = true;
    try {
      const formatOptions = () =>
      {
        return item.items.map((opt) =>
        {
          if (opt.uid) {
            return { name: opt.name, uid: opt.uid };
          } else {
            return { name: opt };
          }
        });
      };
      await updateConfiguration(item.uid, item.name, formatOptions());
      await getConfigurations(currentPage, 1);
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    } finally {
      buttonLoadingItems.value = false;
    }
  }

  async function deleteConfigurationGroup(group, currentPage = 1)
  {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    try {
      await configurationService.deleteConfiguration(handle, projectKey, group.uid);
      await getConfigurations(currentPage, 1);
      showSuccessToast(Swal, 'deleteSuccess', { item: 'configuration group' });
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    }
  }

  async function deleteConfigurationItem(data, currentPage = 1)
  {
    try {
      await updateConfiguration(data.uid, data.name, data.items);
      await getConfigurations(currentPage, 1);
      showSuccessToast(Swal, 'deleteSuccess', { item: 'configuration item' });
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    }
  }

  async function editConfigurationGroup(data, currentPage = 1)
  {
    try {
      await updateConfiguration(data.uid, data.name, data?.items);
      await getConfigurations(currentPage, 1);
      showSuccessToast(Swal, 'updateSuccess', { item: 'configuration item' });
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'configurations' },
        err?.response?.data
      );
    }
  }

  function toggleProjectMenu()
  {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
  }

  function searchCollapsedMenu()
  {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
  }

  function unlinkedCollapsedMenu()
  {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
    displayedRuns.value = 'unlinked';
  }

  function linkedCollapsedMenu()
  {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
    isToPlanExpanded.value = true;
  }

  function onToPlanExpanded()
  {
    isToPlanExpanded.value = !isToPlanExpanded.value;
  }

  // Helper function for milestone click handler
  async function handleConfirmMilestoneClick(items)
  {
    await handleAddMilestone(items);
  }
}; 