import { ref, computed, onMounted, watch, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import makeCasesService from '@/services/api/case';
import {  showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import { $api, useStore } from '@/main';
import { useTestCasesIndex } from '@/composables/modules/cases/index';
import { useRelations } from '@/composables/utils/relations';
import Swal from 'sweetalert2';

 // Reactive data
  const isTreeViewCollapsed = ref(false);
  const isDetailViewCollapsed = ref(false);
  const selectedItem = ref(null);
  const breadCrumbs = ref([]);
  const selectedCase = ref({});
  const selectedCases = ref([]);
  const selectedFolder = ref({});
  const folderUid = ref(null);
  const dialog = ref(false);
  const handleSelected = ref(null);
  const tableFilter = ref(false);
  const preventWatch = ref(0);
  const isTestCaseLoading = ref(false);
  
export const useCaseManagement = (props, { emit }) => {
  const route = useRoute();
  const store = useStore();
  const router = useRouter();
  
  // Import cache clearing function
  const { clearCache } = useTestCasesIndex();
  
  // Import relations cache clearing function
  const { clearEntityCache } = useRelations();
 

  // Computed properties
  const currentAccount = computed(() => store.state.user.currentAccount);
  
  const selectedFolderUid = computed({
    get: () => folderUid.value ?? props.setSelectedFolderUid,
    set: (value) => {
      folderUid.value = value;
      emit('update:setSelectedFolderUid', value);
    }
  });



  const filterCases = computed(() => {
    if (props.selectOption) {
      return selectedCases.value;
    }
    return props.cases.filter(item => item.active === true);
  });

  const currentSelectedCasesIndex = computed(() => {
    return filterCases.value.indexOf(selectedCase.value);
  });

  const isCurrentSelectedCasesFirstIndex = computed(() => {
    return currentSelectedCasesIndex.value === 0;
  });

  const isCurrentSelectedCasesLastIndex = computed(() => {
    return currentSelectedCasesIndex.value === filterCases.value.length - 1;
  });

  const nextCase = computed(() => {
    return filterCases.value[currentSelectedCasesIndex.value + 1];
  });

  const previousCase = computed(() => {
    return filterCases.value[currentSelectedCasesIndex.value - 1];
  });

 const caseUid = computed(() => {
    return route.params.caseUid;
    });

  // Methods
  function confirmEditing(item) {
    handleSelected.value = item;

      if (props.fromTestCase) {
        openDetailView(item);

        const basePath = `/${route.params.handle}/${route.params.key}/cases`;

        if (!route.params.caseUid) {
          const newPath = `${basePath}/${item.testCaseRef}`;
          if (route.path !== newPath) {
            router.push({ path: newPath });
          }
        } else {
          const replacedPath = `${basePath}/${item.testCaseRef}`;
          if (route.path !== replacedPath) {
            router.replace({ path: replacedPath });
          }
        }


      
      return;
    }
    const executionUid = item.runs.find(element => element.runUid === route.params.id)?.executionUid;
    openDetailView({...item, executionUid});
  }

  function toggleMenu() {
    isTreeViewCollapsed.value = !isTreeViewCollapsed.value;
  }

  function changeExpansion(item) {
    selectedItem.value = item;
    isDetailViewCollapsed.value = true;
  }

  function reloadCases() {
    emit('reload-cases');
    if (isDetailViewCollapsed.value && selectedCase.value?.testCaseRef) {
      reloadCase();
    }
  }

  async function reloadCase(){
    const selectedTestCaseRef = selectedCase.value?.testCaseRef;
    await getCase(selectedTestCaseRef);
  }

  function updateCasesHistoryData(data) {
    emit('updateCasesHistoryData', data);
  }

  async function removeFile() {
    const selectedTestCaseRef = selectedCase.value?.testCaseRef;
    await getCase(selectedTestCaseRef);
  }

  async function closeDetailView() {
    selectedCase.value = {};
    isDetailViewCollapsed.value = false;
    let newPath = route.path;

    if (route.params.caseUid) {
      newPath = route.path.replace(/\/cases\/[^/]+/, '/cases/')
    }

    await router.replace({ path: newPath });

  }

  function handleCases(cases) {
    selectedCases.value = cases;
    emit('selectedCases', cases);
    emit('updateCaseCount', cases.length, props.cases.length);
  }

  function updateRouteCase() {
    const currentUid = route.params.caseUid;
    const newUid = selectedCase.value?.testCaseRef;
  
    if (!newUid || currentUid === newUid) return;
  
    const newPath = route.path.replace(/\/cases\/[^/]+/, `/cases/${newUid}`);
  
    router.replace({ path: newPath }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.warn('Router navigation error:', err);
      }
    });
  }
  

  watch(() => props.selectOption, (newValue) => {
    tableFilter.value = newValue;
  });

  function viewPreviousCase() {
    selectedCase.value = previousCase.value;
    updateRouteCase();
  }

  function viewNextCase() {
    selectedCase.value = nextCase.value;
    updateRouteCase();
  }

  function buildBreadCrumbs(searchUID, currentFolders, chain) {
    if (!currentFolders || currentFolders.length === 0) {
      return;
    }
    for (let idx = 0; idx < currentFolders.length; idx++) {
      let folder = currentFolders[idx];
      if (folder.uid === searchUID) {
        chain.push({ text: folder.name, uid: folder.uid });
        breadCrumbs.value = chain;
        break;
      } else if (folder.children && folder.children.length > 0) {
        let newChain = [...chain, { text: folder.name, uid: folder.uid }];
        buildBreadCrumbs(searchUID, folder.children, newChain);
      }
    }
  }

  async function selectFolder(folder) {
    selectedFolder.value = folder;
    let folderUID = folder;
    if (folderUID) {
      selectedFolderUid.value = folderUID.uid ? folderUID.uid : folderUID;
    } else {
      folderUID = selectedFolderUid.value;
    }

    buildBreadCrumbs(folderUID, props.folders, []);
    preventWatch.value++;
    if(preventWatch.value > 1){
      isDetailViewCollapsed.value = false;
    }
    emit('folder-select', folderUID);
  }

  async function getCase(uid) {
    const findCaseUid = uid || caseUid.value;
    const caseService = makeCasesService($api);
    isTestCaseLoading.value = true;
    
    try {
      const res = await caseService.getCase(route.params.handle, route.params.key, findCaseUid);
      if (res.status == 200) {
        const caseData = res.data;
        
        // Fetch tag relations for this case
        if (caseData && caseData.testCaseRef) {
          try {
            const tagRelationsRes = await caseService.getCaseRelations(route.params.handle, route.params.key, 'tag', [caseData.testCaseRef]);
            if (tagRelationsRes.status === 200) {
              const tagRelations = tagRelationsRes.data;
              
              const caseTags = tagRelations[caseData.testCaseRef] || [];
              caseData.tags = caseTags.map(tag => ({
                uid: tag.uid,
                name: tag.name
              }));
            }
          } catch (tagErr) {
            console.warn('Failed to fetch tag relations for case:', tagErr);
            // Continue without tags rather than failing completely
          }
        }
        
        selectedCase.value = caseData;
      } else {
        showErrorToast(Swal, 'fetchError', { item: 'Case' });
      }
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'Case' }, error?.response?.data);
    } finally{
      isTestCaseLoading.value = false;
    }
  }

  function tagFomation(originalTags, newTags) {
    const tagIdsToRemove = originalTags.filter((tagId) => !newTags?.includes(tagId));
    const tagIdsToAdd = newTags.filter((tagId) => !originalTags?.includes(tagId));

    const tagReplacements = [];

    if (tagIdsToRemove.length > 0) {
      tagReplacements.push({
        existingTagUids: tagIdsToRemove,
        newTagUids: [],
      });
    }

    if (tagIdsToAdd.length > 0) {
      tagReplacements.push({
        existingTagUids: [],
        newTagUids: tagIdsToAdd,
      });
    }

    return {
      tagUids: tagIdsToAdd,
      tagReplacements,
    };
  }

  async function updateCase(data){
    const selectedTestCaseRef = selectedCase.value?.testCaseRef;
    const testCase = filterCases.value?.filter(item => item.testCaseRef == selectedTestCaseRef)[0] ?? {};
    const caseService = makeCasesService($api);
    let payload = {};
    if (data.property == 'tags') {
      const originalTags = testCase.tags?.map((tag) => tag.uid) || [];
      const newTags = data?.value?.map(tag => tag?.uid);
      const { tagUids, tagReplacements } = tagFomation(originalTags, newTags);
      payload = {
        ...selectedCase.value,
        tagIds: tagUids,
        tagReplacements
      }
      
      // Immediately update the table's localCaseItems for instant UI feedback
      const caseIndex = filterCases.value.findIndex(c => c.testCaseRef === selectedTestCaseRef);
      if (caseIndex !== -1) {
        filterCases.value[caseIndex].tags = [...data.value];
      }
      
      // Emit the updated data to parent component to sync with table
      emit('updateCasesHistoryData', filterCases.value);
    }

    try {
      const res = await caseService.updateTestCase(route.params.handle, route.params.key, selectedTestCaseRef, payload);
      if (res.status == 200) {
        selectedCase.value = res.data;
        // Clear cache to ensure fresh data after update
        clearCache();
        
        // Clear tag relations cache specifically for this case to ensure fresh tag data
        if (data.property == 'tags') {
          clearEntityCache('case', [selectedTestCaseRef]);
        }
        
        showSuccessToast(Swal, 'updateSuccess', { item: 'Case' });
      } else {
        showErrorToast(Swal, 'updateError', { item: 'Case' });
      }
      reloadCases();
    } catch (error) {
      // If API call fails, revert the local change
      if (data.property == 'tags') {
        const caseIndex = filterCases.value.findIndex(c => c.testCaseRef === selectedTestCaseRef);
        if (caseIndex !== -1) {
          filterCases.value[caseIndex].tags = testCase.tags || [];
        }
        emit('updateCasesHistoryData', filterCases.value);
      }
      showErrorToast(Swal, 'updateError', { item: 'Case' }, error?.response?.data);
    }
  }

  async function onCreateCase(caseItem) {
    const caseService = makeCasesService($api);
    try {
      const res = await caseService.createTestCase(
        route.params.handle,
        route.params.key,
        caseItem
      );

      if (res.status == 200) {
        let newCaseItem = res.data;
        // Clear cache so the new case appears immediately
        clearCache();
        // Emit an event to update cases instead of modifying the prop directly
        if (Array.isArray(newCaseItem)) {
          emit('update-cases', [...newCaseItem, ...props.cases]);
        } else {
          emit('update-cases', [newCaseItem, ...props.cases]);
        }
      } else {
        showErrorToast(Swal, 'createError', { item: 'Case' });
      }
    } catch (error) {
      showErrorToast(Swal, 'createError', { item: 'Case' }, error?.response?.data);
    }
  }

  const onDeleteCase = async (caseId) => {
    const caseService = makeCasesService($api);
    try {
      const res = await caseService.deleteCase(
        route.params.handle,
        route.params.key,
        caseId
      );

      if (res.status == 200) {
        // Clear cache to ensure fresh data after deletion
        clearCache();
        emit('update-cases', props.cases.filter(item => item.uid !== caseId));
        showSuccessToast(Swal, 'deleteSuccess', { item: 'Case' });

      } else {
        showErrorToast(Swal, 'deleteError', { item: 'Case' });
      }
    } catch (error) {
      showErrorToast(Swal, 'deleteError', { item: 'Case' }, error?.response?.data);
    }
  }
    const onBulkDeleteCases = async (cases) => {
      
    const caseService = makeCasesService($api);
    try {
      const res = await caseService.deleteCases(
        route.params.handle,
        route.params.key,
        cases
      );

      if (res.status == 200) {
        // Clear cache to ensure fresh data after deletion
        clearCache();
        emit('update-cases', props.cases.filter(item => !cases.ids?.includes(item.uid)));
        showSuccessToast(Swal, 'deleteSuccess', { item: 'Case' });

      } else {
        showErrorToast(Swal, 'deleteError', { item: 'Case' });
      }
    } catch (error) {
      showErrorToast(Swal, 'deleteError', { item: 'Case' }, error?.response?.data);
    }
  }

  function openDetailView(item) {
    selectedCase.value = item ?? handleSelected.value;
    isDetailViewCollapsed.value = true;
    dialog.value = false;
    getCase(selectedCase.value?.testCaseRef || selectedCase.value?.uid);

  }

  function deleteFolder() {
    // Only clear cases if this function is called (which means the deleted folder was selected)
    // This preserves cases in other folders when a non-selected folder is deleted
    emit('update-cases', []);
  }

  function updateSelectedCases(payload) {
    emit('updateSelectedCases', payload);
  }

  watch(() => props.runCases, (newVal) => {
    selectedCases.value = newVal;
  });

  // Lifecycle hooks
  onMounted(async () => {
    if (props.runCases) {
      selectedCases.value = props.runCases;
    }
    
    await new Promise(resolve => setTimeout(() => {
      buildBreadCrumbs(parseInt(selectedCase.value?.parentUid), props.folders, []);
      resolve();
    }, 1000));  

    isDetailViewCollapsed.value = route.params.caseUid ? true : false;
    if(route.params.caseUid){
      await getCase();
    }

  });

  onBeforeMount(() => {
    if(route.name == "TestRunEdit" || !route.params.caseUid){
      isDetailViewCollapsed.value = false;
    }
  });


  return {
    // Reactive data
    isTreeViewCollapsed,
    isDetailViewCollapsed,
    selectedItem,
    breadCrumbs,
    selectedCase,
    selectedCases,
    selectedFolder,
    dialog,
    handleSelected,
    tableFilter,
    isTestCaseLoading,
    
    // Computed
    currentAccount,
    selectedFolderUid,
    filterCases,
    currentSelectedCasesIndex,
    isCurrentSelectedCasesFirstIndex,
    isCurrentSelectedCasesLastIndex,
    nextCase,
    previousCase,
    caseUid,
    
    // Methods
    confirmEditing,
    toggleMenu,
    changeExpansion,
    reloadCases,
    updateCasesHistoryData,
    closeDetailView,
    handleCases,
    viewPreviousCase,
    viewNextCase,
    buildBreadCrumbs,
    selectFolder,
    onCreateCase,
    openDetailView,
    deleteFolder,
    updateSelectedCases,
    getCase,
    onDeleteCase,
    onBulkDeleteCases,
    removeFile,
    reloadCase,
    updateCase,
  };
}; 