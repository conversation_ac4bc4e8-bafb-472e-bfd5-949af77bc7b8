<template>
  <div>
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between py-6">
            <h2 class="text-theme-label">
              {{ data.name }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <div>
            <h4 class="fw-semibold fs-16px text-theme-label text-start">
              {{ $t('defect.overview') }}
            </h4>

            <v-list class="list-theme d-flex flex-column">
              <v-list-item class="px-0">
                <template>
                  <div class="d-flex flex-grow-1">
                    <div
                      v-if="selectedIntegration == 'Jira'"
                      class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('defect.issueType') }}
                      </h5>
                      <div class="align-left contents fw-semibold fs-14px">
                        {{ data.issueType || $t('none') }}
                      </div>
                    </div>
                    <div
                      v-else
                      class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('defect.creator') }}
                      </h5>
                      <div class="align-left contents fw-semibold fs-14px">
                        {{ data.creator || $t('none') }}
                      </div>
                    </div>
                    <div
                      class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                      style="background-color: #f9fafb"
                      @click="openUrl(data.webUrl)"
                    >
                      <div class="d-flex justify-space-between align-center">
                        <h5 class="mb-0">
                          {{ $t(`defect.viewDefect.serviceId`, { service: selectedIntegration }) }}
                        </h5>
                        <v-icon
                          class="external-link-icon"
                          size="16"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </div>
                      <div class="align-left contents fw-semibold fs-14px">
                        {{ data.externalId || $t('none') }}
                      </div>
                    </div>
                  </div>
                </template>
              </v-list-item>
              <v-list-item class="px-0">
                <template>
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="block rounded-lg px-3 py-2 mt-2"
                    style="background-color: #f9fafb; width: 100%"
                    @click="data.linkedExecutions && data.linkedExecutions.length === 1 ? openUrl(data.linkedExecutions[0].url) : null"
                  >
                    <div class="d-flex justify-space-between align-center">
                      <h5 class="align-left mb-0">
                        {{ $t('defect.execution') }}
                        <span
                          v-if="data.linkedExecutions && data.linkedExecutions.length > 1"
                          class="ml-1 text-caption"
                        >
                          ({{ data.linkedExecutions.length }})
                        </span>
                      </h5>
                      <v-icon
                        v-if="data.linkedExecutions && data.linkedExecutions.length > 0"
                        class="external-link-icon"
                        size="16"
                      >
                        mdi-arrow-top-right
                      </v-icon>
                    </div>
                    <div class="align-left contents fw-semibold fs-14px">
                      <template v-if="!data.linkedExecutions || data.linkedExecutions.length === 0">
                        {{ $t('none') }}
                      </template>
                      <template v-else-if="data.linkedExecutions.length === 1">
                        <span class="cursor-pointer">
                          {{ data.linkedExecutions[0].name }}
                        </span>
                      </template>
                      <v-menu
                        v-else
                        offset-y
                      >
                        <template #activator="{ on, attrs }">
                          <div
                            class="d-flex align-center cursor-pointer"
                            v-bind="attrs"
                            v-on="on"
                          >
                            {{ data.linkedExecutions[0].name }}
                            <v-icon
                              small
                              class="ml-1"
                            >
                              mdi-chevron-down
                            </v-icon>
                          </div>
                        </template>
                        <v-list dense>
                          <v-list-item
                            v-for="execution in data.linkedExecutions"
                            :key="execution.uid"
                            dense
                            @click="openUrl(execution.url)"
                          >
                            <v-list-item-title>{{ execution.name }}</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </div>
                  </div>
                  <div
                    v-else
                    class="block rounded-lg px-3 py-2 mt-2"
                    style="background-color: #f9fafb; width: 100%"
                    @click="openUrl(`https://github.com/${data.projectName}/issues`)"
                  >
                    <div class="d-flex justify-space-between align-center">
                      <h5 class="align-left">
                        {{
                          $t('defect.viewDefect.serviceProject', {
                            service: selectedIntegration,
                            entity: entity[selectedIntegration.toLowerCase()],
                          })
                        }}
                      </h5>
                      <v-icon
                        class="external-link-icon"
                        size="16"
                      >
                        mdi-arrow-top-right
                      </v-icon>
                    </div>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ data.projectName || $t('none') }}
                    </div>
                  </div>
                </template>
              </v-list-item>
              <v-list-item
                v-if="!expandOverviewPanel"
                class="px-0 mt-2"
              >
                <template>
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="d-flex flex-grow-1"
                  >
                    <div
                      class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('status') }}
                      </h5>

                      <v-chip
                        v-if="data.status"
                        label
                        class="status-chip"
                        :style="{
                          color: getStatusColor(data.status),
                          backgroundColor: `${getStatusColor(data.status)}10`,
                        }"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ getStatusName(data.status) }}
                        </div>
                      </v-chip>
                      <v-chip
                        v-else
                        label
                        class="status-chip"
                        :style="{
                          color: data.customFields?.state === 'open' ? '#008000' : '#f2284e',
                          backgroundColor: data.customFields?.state === 'open' ? '#00800015' : '#f2284e15',
                        }"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ data.customFields?.state }}
                        </div>
                      </v-chip>
                    </div>
                    <div
                      class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('priority') }}
                      </h5>
                      <v-chip
                        v-if="data.priority"
                        label
                        class="status-chip"
                        :style="{
                          color: getPriorityColor(data.priority),
                          backgroundColor: `${getPriorityColor(data.priority)}10`,
                        }"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ getPriorityName(data.priority) }}
                        </div>
                      </v-chip>
                      <v-chip
                        v-else
                        label
                        class="status-chip"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ 'None' }}
                        </div>
                      </v-chip>
                    </div>
                  </div>
                  <div
                    v-else
                    class="d-flex flex-grow-1"
                  >
                    <div
                      class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('defect.viewDefect.testRun') }}
                      </h5>
                      <DefectTestLinkedSkeleton
                        :key="data.uid"
                        :endpoint-func="() => getDefectRuns(data.uid)"
                      />
                    </div>
                    <div
                      class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('defect.viewDefect.testExecution') }}
                      </h5>
                      <DefectTestLinkedSkeleton
                        :key="data.uid"
                        :endpoint-func="() => getDefectExecutions(data.uid)"
                      />
                    </div>
                  </div>
                </template>
              </v-list-item>
              <v-list-item
                v-if="!expandOverviewPanel && selectedIntegration == 'Github'"
                class="px-0 mt-2"
              >
                <template>
                  <div class="d-flex flex-grow-1">
                    <div
                      class="block rounded-lg px-3 py-2 w-50 mr-4 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('typeLabel') }}
                      </h5>
                      <div class="align-left contents fw-semibold fs-14px">
                        {{ data.type || $t('none') }}
                      </div>
                    </div>

                    <div
                      class="block rounded-lg px-3 py-2 w-50 mh-56px"
                      style="background-color: #f9fafb"
                    >
                      <h5 class="align-left">
                        {{ $t('lastUpdate') }}
                      </h5>
                      <div class="align-left contents fw-semibold fs-14px">
                        {{ formatDate(data.updatedAt) }}
                      </div>
                    </div>
                  </div>
                </template>
              </v-list-item>
              <v-list-item
                v-if="!expandOverviewPanel"
                class="px-0"
              >
                <template>
                  <div
                    class="block rounded-lg px-3 py-2 mt-2"
                    style="background-color: #f9fafb; width: 100%"
                  >
                    <h5 class="align-left">
                      {{ $t('defect.viewDefect.labels') }}
                    </h5>
                    <div
                      v-if="data.customFields?.labels.length > 0 || data.customFields?.tags.length > 0"
                      class="tags-grid"
                    >
                      <v-chip
                        v-for="(tag, index) in selectedIntegration == 'Jira'
                          ? data.customFields?.labels
                          : data.customFields?.tags.slice(0, 5)"
                        :key="index"
                        class="custom-chip-theme fs-12px ma-1"
                        style="height: 24px;"
                        :style="
                          selectedIntegration == 'Github'
                            ? {
                              backgroundColor: `${selectedIntegration == 'Github' ? tag.color : '#42526E'}10`,
                              color: selectedIntegration == 'Github' ? tag.color : '#42526E',
                              border: `1px solid ${selectedIntegration == 'Github' ? tag.color : '#42526E'}`,
                              fontWeight: 'bold',
                            }
                            : { border: 'none' }
                        "
                        :label="selectedIntegration == 'Github' ? false : true"
                      >
                        {{ selectedIntegration == 'Github' ? tag.name : tag }}
                      </v-chip>
                      <v-tooltip
                        bottom
                        left
                        max-width="485px"
                        content-class="tooltip-theme"
                      >
                        <template #activator="{ on, attrs }">
                          <span
                            v-if="data.customFields?.tags.length > 5"
                            class="custom-attribute text-truncate text-theme-table-text"
                            v-bind="attrs"
                            v-on="on"
                          >
                            +{{ data.customFields?.tags.length - 5 }}
                          </span>
                        </template>
                        <v-chip
                          v-for="(tag, index) in selectedIntegration == 'Jira'
                            ? data.customFields?.labels
                            : data.customFields?.tags.slice(5)"
                          :key="index"
                          class="custom-chip-theme ma-1"
                          :style="
                            selectedIntegration == 'Github'
                              ? {
                                backgroundColor: `${selectedIntegration == 'Github' ? tag.color : '#42526E'}10`,
                                color: selectedIntegration == 'Github' ? tag.color : '#42526E',
                                border: `1px solid ${selectedIntegration == 'Github' ? tag.color : '#42526E'}`,
                                fontWeight: 'bold',
                              }
                              : { border: 'none' }
                          "
                          :label="selectedIntegration == 'Github' ? false : true"
                        >
                          {{ selectedIntegration == 'Github' ? tag.name : tag }}
                        </v-chip>
                      </v-tooltip>
                    </div>
                    <div v-else>
                      {{ $t('none') }}
                    </div>
                  </div>
                </template>
              </v-list-item>
             
              <v-list-item
                v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                class="px-0"
              >
                <div class="d-flex flex-grow-1">
                  <div
                    class="block rounded-lg px-3 py-2 mt-4 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb; width: 100%"
                  >
                    <h5 class="align-left">
                      {{ $t('defect.parentTicket') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ data.customFields?.parent?.key || $t('none') }}
                    </div>
                  </div>
                </div>
              </v-list-item>
              <v-list-item
                v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                class="px-0"
              >
                <div class="d-flex flex-grow-1">
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="block rounded-lg px-3 py-2 mt-4 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('defect.fixVersions') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ data.customFields?.fixVersions || $t('none') }}
                    </div>
                  </div>
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="block rounded-lg px-3 py-2 mt-4 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('defect.restrictTo') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ data.customFields?.restrictTo || $t('none') }}
                    </div>
                  </div>
                </div>
              </v-list-item>
            
              <v-list-item
                v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                class="px-0"
              >
                <div class="d-flex flex-grow-1">
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="block rounded-lg px-3 py-2 mt-4 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('defect.assignee') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ data.customFields?.assignee?.displayName || $t('none') }}
                    </div>
                  </div>
                  
                  <div
                    v-if="selectedIntegration == 'Jira'"
                    class="block rounded-lg px-3 py-2 mt-4 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('lastUpdate') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ formatDate(data.updatedAt) }}
                    </div>
                  </div>
                </div>
              </v-list-item>

              <div class="mt-6">
                <h4
                  v-if="!expandOverviewPanel"
                  class="fw-semibold fs-16px text-theme-label mb-4 text-left"
                >
                  {{ $t('description') }}
                </h4>
                <template v-if="!expandOverviewPanel">
                  <div v-if="data.description">
                    <div 
                      class="bg-gray-theme tiptap-theme readonly-editor"
                      v-html="sanitizeHTML(data.description)"
                    />
                  </div>
                </template>
              </div>

              <div
                v-if="data.attachments && data.attachments.length > 0"
                class="mt-6"
              >
                <h4 class="fw-semibold fs-16px text-theme-label mb-4 text-left">
                  {{ $t('attachments') }}
                </h4>
                <div class="attachments-grid">
                  <div
                    v-for="(attachment, index) in data.attachments"
                    :key="index"
                    class="attachment-item"
                  >
                    <!-- Image files -->
                    <template v-if="isImageFile(attachment.fileType)">
                      <img
                        :src="attachment.previewUrl"
                        :alt="attachment.name"
                        class="attachment-preview"
                        @click="openAttachmentPreview(attachment)"
                      >
                    </template>

                    <!-- Video files -->
                    <template v-else-if="isVideoFile(attachment.fileType)">
                      <video
                        :src="attachment.previewUrl"
                        class="attachment-preview"
                        controls
                      >
                        {{ $t('browserNotSupported') }}
                      </video>
                    </template>

                    <!-- Other file types -->
                    <template v-else>
                      <div class="file-type-container">
                        <div class="attachment_file-type">
                          <v-icon size="55">
                            mdi-file-outline
                          </v-icon>
                          <span class="file-extension">{{ getFileExtension(attachment.name) }}</span>
                        </div>
                        <div class="attachment_file-details">
                          <p
                            id="fileName"
                            :title="attachment.name"
                          >
                            {{ attachment.name }}
                          </p>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <h4
                v-if="expandOverviewPanel"
                class="f-color-blue toggle-btn text-start text-color"
                @click="expandOverviewPanel = !expandOverviewPanel"
              >
                {{ $t('defect.showMore') }}
              </h4>
              <h4
                v-else
                class="f-color-blue toggle-btn text-start text-color"
                @click="expandOverviewPanel = !expandOverviewPanel"
              >
                {{ $t('defect.showLess') }}
              </h4>
            </v-list>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Image Preview Dialog -->
    <v-dialog
      v-model="showPreview"
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
      content-class="image-preview-dialog"
    >
      <div class="preview-overlay">
        <div class="preview-toolbar">
          <v-btn
            icon
            class="close-btn"
            @click="showPreview = false"
          >
            <v-icon color="white">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <div class="preview-content">
          <img
            v-if="selectedAttachment && isImageFile(selectedAttachment.fileType)"
            :src="selectedAttachment.previewUrl"
            :alt="selectedAttachment.name"
            class="preview-image"
          >
        </div>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import DOMPurify from 'dompurify';
import makeDefectsService from '@/services/api/defect';
import DefectTestLinkedSkeleton from '@/components/Skeletons/Defect/DefectTestLinkedSkeleton.vue';

let defectsService;

export default {
  name: 'ViewDefectDialog',
  components: {
    DefectTestLinkedSkeleton,
  },
  mixins: [colorPreferencesMixin],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    priorities: {
      type: Array,
      default: () => [],
    },
    statuses: {
      type: Array,
      default: () => [],
    },
    selectedIntegration: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      expandOverviewPanel: false,
      commentsPanel: 0,
      newComment: '',
      showTiptap: false,
      entity: {
        jira: 'Project',
        github: 'Repository',
      },
      showPreview: false,
      selectedAttachment: null,
    };
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('close-dialog', v);
      },
    },
  },
  watch: {
    showDialog(value) {
      if (!value) {
        return;
      }

      this.expandOverviewPanel = false;
      this.newComment = '';
    },
  },
  created() {
    defectsService = makeDefectsService(this.$api);
  },
  methods: {
    async getDefectExecutions(defectUid) {
      if (!defectUid) return;
      const handle = this.$route.params.handle;
      const key = this.$route.params.key;
      return await defectsService.getDefectExecutions(handle, key, defectUid);
    },

    async getDefectRuns(defectUid) {
      if (!defectUid) return;
      const handle = this.$route.params.handle;
      const key = this.$route.params.key;
      return await defectsService.getDefectRuns(handle, key, defectUid);
    },

    sanitizeHTML(html) {
      if (!html || html === '') {
        return '';
      }
      return DOMPurify.sanitize(html);
    },
    formatDate(date) {
      if (!date) {
        return '';
      }

      // Handle both date string and Date object cases
      try {
        if (typeof date === 'string') {
          // If date is already in DD/MM/YYYY format, return as is
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
            return date;
          }
          // Otherwise parse and format
          const parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) {
            return '';
          }
          return formatDate(parsedDate, 'MM/dd/yy');
        } else if (date instanceof Date) {
          if (isNaN(date.getTime())) {
            return '';
          }
          return formatDate(date, 'MM/dd/yy');
        }
        return '';
      } catch (error) {
        console.error('Error formatting date:', error);
        return '';
      }
    },

    getStatusColor(statusId) {
      const status = this.statuses.find((s) => Number(s.id) === Number(statusId));
      return status?.color || '#0c111d';
    },

    getStatusName(statusId) {
      const status = this.statuses.find((s) => Number(s.id) === Number(statusId));
      return status?.name || '';
    },

    getPriorityColor(priorityId) {
      const priority = this.priorities.find((p) => Number(p.id) === Number(priorityId));
      return priority?.color || '#0c111d';
    },

    getPriorityName(priorityId) {
      const priority = this.priorities.find((p) => Number(p.id) === Number(priorityId));
      return priority?.name || '';
    },


    openUrl(url) {
      if (url) {
        window.open(url, '_blank');
      }
    },
    isImageFile(type) {
      return type && type.startsWith('image/');
    },

    isVideoFile(type) {
      return type && type.startsWith('video/');
    },

    openAttachmentPreview(attachment) {
      if (this.isImageFile(attachment.fileType)) {
        this.selectedAttachment = attachment;
        this.showPreview = true;
      }
    },
    getFileExtension(fileName) {
      const parts = fileName.split('.');
      return parts[parts.length - 1];
    },
  },
};
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.text-caption {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}
.text-color {
  color: #0000ff;
}
:deep(.tiptap-theme) {
  background-color: #f9f9fb;
  border-radius: 8px;
  padding: 12px;
}

:deep(.tiptap-theme .ProseMirror) {
  min-height: unset;
  pointer-events: none;
}

:deep(.tiptap-theme p) {
  margin-bottom: 0;
  line-height: 1.2;
}
.description-content {
  text-align: left;
  white-space: pre-wrap;
  font-family: inherit;
  padding: 12px;
}

.description-content :deep(ul) {
  list-style-type: disc;
  padding-left: 20px;
  margin: 4px 0;
}

.description-content :deep(li) {
  display: list-item;
  padding: 0;
  margin: 0;
}

/* Remove the flex styling that was causing alignment issues */
.description-content :deep(li::before),
.description-content :deep(li::marker) {
  display: none;
}

/* Ensure proper text wrapping */
.description-content :deep(p) {
  margin: 0;
  padding: 0;
  text-align: left;
}

/* 
 * Note: v-html is used for rendering sanitized ProseMirror content.
 * The content is structured and controlled, coming from a trusted source.
 */

.block {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.block:hover {
  background-color: #f2f4f7;
}

.external-link-icon {
  color: #344054 !important;
}

h5 {
  color: #667085;
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 12px;
}

.contents {
  color: #344054;
}

.justify-space-between {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.attachment-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #eaecf0;
  cursor: pointer;
  background-color: #f9fafb;
}

.attachment-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  background: rgba(0, 0, 0, 0.9);
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.preview-image {
  max-width: 95%;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 4px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1) !important;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Smooth transition */
.dialog-bottom-transition-enter-active,
.dialog-bottom-transition-leave-active {
  transition: all 0.3s ease;
}

.dialog-bottom-transition-enter,
.dialog-bottom-transition-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

video.attachment-preview {
  background: #f9fafb;
}

.file-type-container {
  display: flex;
  align-items: center;
  padding: 8px;
}

.attachment_file-type {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.attachment_file-details {
  flex: 1;
}

.file-extension {
  font-size: 0.875rem;
  font-weight: 600;
  color: #344054;
}
</style>

<style>
/* GitHub Markdown Styling */
.markdown-body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  text-align: left;
}

.markdown-body pre {
  background: #f6f8fa;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
}

.markdown-body code {
  background: #f6f8fa;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-body blockquote {
  border-left: 4px solid #ccc;
  padding-left: 10px;
  color: #666;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #ddd;
  padding: 8px;
}

.markdown-body th {
  background: #f6f8fa;
  font-weight: bold;
}
.more-count {
  color: #666;
  font-size: 15px;
  font-weight: 500;
}
</style>
