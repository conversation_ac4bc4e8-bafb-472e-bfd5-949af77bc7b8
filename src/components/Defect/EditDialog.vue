<template>
  <div>
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <template v-if="loading">
        <v-card>
          <v-card-text class="flex-grow-1 pa-0">
            <div class="px-4 pt-6 pb-4">
              <div class="d-flex align-center justify-space-between">
                <h2 class="black--text">
                  {{ $t('defect.editDefect') }}
                </h2>
                <v-btn
                  icon
                  @click="showDialog = false"
                >
                  <v-icon color="black">
                    mdi-close
                  </v-icon>
                </v-btn>
              </div>
            </div>

            <div class="d-flex mx-auto justify-center flex-column h-100">
              <v-progress-circular
                class="position-relative"
                :size="150"
                width="6"
                color="primary"
                indeterminate
              >
                <div class="svg-container">
                  <UploadCloud class="upload-cloud-icon" />
                </div>
              </v-progress-circular>
              <p class="title font-weight-regular text-center mt-4 mx-auto">
                {{ $t('defect.updatingData') }}
              </p>
            </div>
          </v-card-text>
        </v-card>
      </template>

      <v-card v-else>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('defect.editDefect') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <v-form
            ref="form"
            v-model="validForm"
            lazy-validation
            class="mt-10"
          >
            <v-row>
              <!-- <v-col cols="12" class="pb-0" v-if="selectedIntegration === 'Jira'">
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('Choose project') }}
                </div>
                <v-select
                  v-model="defect.priority"
                  :items="priorities"
                  dense
                  item-text="name"
                  item-value="id"
                  append-icon="mdi-chevron-down"
                  class="rounded-lg field-theme custom-prepend"
                  height="38px"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  clearable
                >
                  <template #selection="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>

                  <template #item="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>
                </v-select>
              </v-col>
              <v-col cols="12" class="pb-0" v-if="selectedIntegration === 'Jira'">
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.createNewDefectDialog.typeLabel') }}
                </div>
                <v-select
                  v-model="defect.priority"
                  :items="priorities"
                  dense
                  item-text="name"
                  item-value="id"
                  append-icon="mdi-chevron-down"
                  class="rounded-lg field-theme custom-prepend"
                  height="38px"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  clearable
                >
                  <template #selection="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>

                  <template #item="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>
                </v-select>
              </v-col> -->
              <v-col
                v-if="selectedIntegration === 'Jira'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('Title') }}
                </div>
                <v-text-field
                  v-model="defect.name"
                  class="round-8 field-theme"
                  background-color="#F9F9FB"
                  dense
                  height="38px"
                  :placeholder="$t('name')"
                  :rules="requiredRules"
                />
              </v-col>
              <v-col
                v-if="selectedIntegration === 'Github'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('Title') }}
                </div>
                <v-text-field
                  v-model="defect.name"
                  class="round-8 field-theme"
                  background-color="#F9F9FB"
                  dense
                  height="38px"
                  :placeholder="$t('name')"
                  :rules="requiredRules"
                />
              </v-col>
              <v-col
                v-if="selectedIntegration === 'Jira'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('status') }}
                </div>
                <v-select
                  v-model="defect.status"
                  :items="filteredStatuses"
                  dense
                  item-text="name"
                  item-value="id"
                  append-icon="mdi-chevron-down"
                  class="rounded-lg field-theme custom-prepend"
                  height="38px"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  :loading="isLoadingStatusScopes"
                  :disabled="isLoadingStatusScopes"
                  clearable
                >
                  <template #selection="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>

                  <template #item="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>
                </v-select>
              </v-col>
              <v-col
                v-if="selectedIntegration === 'Jira'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('priority') }}
                </div>
                <v-select
                  v-model="defect.priority"
                  :items="priorities"
                  dense
                  item-text="name"
                  item-value="id"
                  append-icon="mdi-chevron-down"
                  class="rounded-lg field-theme custom-prepend"
                  height="38px"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  clearable
                >
                  <template #selection="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>

                  <template #item="{ item }">
                    <span :style="{ color: item.color }">{{ item.name }}</span>
                  </template>
                </v-select>
              </v-col>

              <v-col
                v-if="selectedIntegration === 'Github'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.createNewDefectDialog.tagsLabel') }}
                </div>
                <v-select
                  v-model="defect.tags"
                  :items="tags"
                  type="text"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="$t('defect.createNewDefectDialog.selectTags')"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  append-icon="mdi-chevron-down"
                  item-text="name"
                  item-value="name"
                  :menu-props="{
                    offsetY: true,
                    closeOnClick: false,
                    closeOnContentClick: false,
                  }"
                  multiple
                  chips
                  deletable-chips
                  :loading="isLoadingTags"
                  :disabled="isLoadingTags"
                >
                  <template #prepend-item>
                    <v-list-item
                      ripple
                      @click="toggleSelectAll"
                    >
                      <v-list-item-action>
                        <v-checkbox
                          :input-value="isAllSelected"
                          :indeterminate="isIndeterminate"
                          class="field-theme"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                          indeterminate-icon="icon-indeterminate"
                          :hide-details="true"
                          dense
                        />
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>
                          <span class="fs-14px text-theme-label">{{ $t('selectAll') }}</span>
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <v-divider />
                  </template>

                  <template #selection="{ item, index }">
                    <v-chip
                      v-if="index < 3"
                      close
                      class="custom-chip-theme ma-1"
                      @click:close="removeTag(item)"
                    >
                      {{ item.name }}
                    </v-chip>
                    <span
                      v-if="index === 3"
                      class="text-caption grey--text text-truncate"
                    >
                      (+{{ defect.tags.length - 3 }} {{ $t('more') }})
                    </span>
                  </template>

                  <template #item="{ item }">
                    <v-list-item @click.stop="toggleTag(item)">
                      <v-list-item-action>
                        <v-checkbox
                          :input-value="isTagSelected(item)"
                          class="field-theme"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                          :hide-details="true"
                          dense
                        />
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>
                          <span class="fs-14px text-theme-label">{{ item.name }}</span>
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>

              <v-col
                v-if="selectedIntegration === 'Github'"
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.state') }}
                </div>
                <v-select
                  v-model="defect.state"
                  :items="defect.states"
                  dense
                  append-icon="mdi-chevron-down"
                  class="rounded-lg field-theme custom-prepend"
                  height="38px"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  clearable
                />
              </v-col>

              <!-- Description Field with TipTap Editor -->
              <v-col
                cols="12"
                class="pb-0"
              >
                <div class="text-left fs-14px text-theme-label font-weight-medium mb-2">
                  {{ $t('description') }}
                </div>
                <TiptapEditor
                  v-model="defect.description"
                  class="defect-description-editor"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('close-dialog')"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          :disabled="!validForm"
          @click="onEdit()"
        >
          {{ $t('defect.sendUpdate') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import UploadCloud from '@/assets/svg/upload-cloud.svg';
import DOMPurify from 'dompurify';
import TiptapEditor from '@/components/base/TipTapEditor.vue';
import convertToADF from 'html-to-adf-converter';

export default {
  name: 'UpdateDefect',
  components: {
    UploadCloud,
    TiptapEditor,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },

    data: {
      type: Object,
      default: () => ({}),
    },

    priorities: {
      type: Array,
      default: () => [],
    },

    statuses: {
      type: Array,
      default: () => [],
    },

    statusScopes: {
      type: Array,
      default: () => [],
    },

    tags: {
      type: Array,
      default: () => [],
    },

    loading: {
      type: Boolean,
      default: false,
    },

    isLoadingTags: {
      type: Boolean,
      default: false,
    },
    isLoadingStatusScopes: {
      type: Boolean,
      default: false,
    },
    selectedIntegration: {
      type: String,
      default: 'Jira',
    },
  },

  data() {
    return {
      requiredRules: [(value) => !!value || this.$t('error.requiredField')],
      validForm: true,
      defect: {
        uid: '',
        name: '',
        priority: '',
        status: '',
        tags: [],
        state: '',
        description: '',
        states: [],
      },
    };
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },
    isAllSelected() {
      return this.tags.length > 0 && this.defect.tags.length === this.tags.length;
    },
    isIndeterminate() {
      return this.defect.tags.length > 0 && !this.isAllSelected;
    },
    filteredStatuses() {
      if (!this.statusScopes || !this.statusScopes.length) {
        return [];
      }
      
        const scopedStatuses = this.statuses.filter((status) => this.statusScopes.includes(String(status.id)));
      
      if (this.defect.status) {
        const currentStatus = this.statuses.find(status => status.id === this.defect.status);
        if (currentStatus && !scopedStatuses.find(s => s.id === currentStatus.id)) {
          scopedStatuses.push(currentStatus);
        }
      }
      
      return scopedStatuses;
    },
  },

  watch: {
    showDialog(value) {
      if (!value) return;

      const priorityId = this.data.priority ? Number(this.data.priority) : '';
      const statusId = this.data.status ? Number(this.data.status) : '';

      // Parse the description - now only HTML
      let parsedDescription = '';
      if (this.data.description) {
        parsedDescription = this.sanitizeHTML(this.data.description);
      }

      this.defect = {
        uid: this.data.uid || '',
        name: this.data.name || '',
        priority: priorityId,
        status: statusId,
        state: this.data.state,
        states: ['open', 'closed'],
        description: parsedDescription,
        attachments: this.defect.attachments,
        tags:
          this.data.customFields?.tags?.map((tag) => ({
            uid: tag.id,
            name: tag.name,
            color: tag.color,
          })) || [],
      };
    },
    statusScopes: {
      handler(newScopes) {
        if (newScopes && newScopes.length > 0 && this.defect.status) {
          this.$forceUpdate();
        }
      },
      deep: true
    }
  },
  methods: {
    onEdit() {
      const isValidForm = this.$refs.form.validate();
      if (!isValidForm) {
        return;
      }

      // Convert HTML description to ADF for Jira
      let description = this.defect.description;
      if (this.selectedIntegration === 'Jira' && description) {
        try {
          // Convert HTML to ADF format
          description = convertToADF(description);
        } catch (error) {
          console.error('Error converting HTML to ADF:', error);
          // Fallback to original description if conversion fails
        }
      }

      const payload = {
        uid: this.defect.uid,
        name: this.defect.name,
        state: this.defect.state,
        description: description,
        ...(this.defect.priority && { priority: this.defect.priority }),
        ...(this.defect.status && { status: this.defect.status }),
        ...(this.selectedIntegration === 'Github' && {
          customFields: {
            tags: this.defect.tags,
          },
        }),
      };

      this.$emit('edit', payload);
    },
    getPriorityColor(priorityId) {
      const priority = this.priorities.find((p) => p.id === priorityId);
      return priority?.color || '#0c111d';
    },

    getPriorityName(priorityId) {
      const priority = this.priorities.find((p) => p.id === priorityId);
      return priority?.name || '';
    },

    getStatusColor(statusId) {
      const status = this.statuses.find((s) => s.id === statusId);
      return status?.color || '#0c111d';
    },

    getStatusName(statusId) {
      const status = this.statuses.find((s) => s.id === statusId);
      return status?.name || '';
    },

    getDefaultPriority() {
      return this.priorities.find((p) => p.isDefault)?.id;
    },

    getCompletedStatuses() {
      return this.statuses.filter((s) => s.isCompleted).map((s) => s.id);
    },

    getPriorityByName(name) {
      const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
      return this.priorities.find((p) => p.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
    },

    getStatusByName(name) {
      const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
      return this.statuses.find((s) => s.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
    },

    isTagSelected(tag) {
      return this.defect.tags.some((t) => t.name === tag.name);
    },

    toggleSelectAll() {
      if (this.isAllSelected) {
        this.defect.tags = [];
      } else {
        this.defect.tags = this.tags.map((tag) => ({
          name: tag.name,
          uid: tag.uid,
        }));
      }
    },

    toggleTag(item) {
      const index = this.defect.tags.findIndex((tag) => tag.name === item.name);
      if (index === -1) {
        this.defect.tags.push({
          name: item.name,
          uid: item.uid,
        });
      } else {
        this.defect.tags.splice(index, 1);
      }
    },

    removeTag(tag) {
      const index = this.defect.tags.findIndex((t) => t.name === tag.name);
      if (index >= 0) {
        this.defect.tags.splice(index, 1);
      }
    },

    sanitizeHTML(html) {
      if (!html || html === '') {
        return '';
      }
      return DOMPurify.sanitize(html);
    },

  },
};
</script>

<style scoped>
:deep(.custom-chip-theme) {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  height: 24px !important;
}

:deep(.custom-chip-theme .v-chip__close) {
  opacity: 1;
  font-size: 16px;
}

.h-100 {
  height: 100%;
}

.position-relative {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.svg-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.title {
  color: #344054;
  font-size: 16px;
}
.v-select__selections .v-chip:not(.v-chip--active) {
  background: white !important;
  border: solid 2px #d0d5dd !important;
  border-radius: 50px !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

/* TipTap Editor Styles for Defect Description */
:deep(.defect-description-editor .tiptap-theme) {
  background-color: #F9F9FB;
  border-radius: 8px;
  border: 1px solid #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar) {
  background-color: #F9F9FB;
  border-bottom: 1px solid #E0E0E0;
  border-radius: 8px 8px 0 0;
}

:deep(.defect-description-editor .tiptap-editor) {
  background-color: #F9F9FB;
  border: none;
  border-radius: 0 0 8px 8px;
  min-height: 120px;
}

:deep(.defect-description-editor .tiptap-footer) {
  background-color: #F9F9FB;
  border-top: 1px solid #E0E0E0;
  border-radius: 0 0 8px 8px;
}

:deep(.defect-description-editor .ProseMirror) {
  background-color: #F9F9FB;
  padding: 12px;
  min-height: 100px;
  border: none;
  outline: none;
  overflow: visible !important;
}

:deep(.defect-description-editor .ProseMirror:focus) {
  outline: none;
  border: none;
}

:deep(.defect-description-editor .tiptap-toolbar button) {
  color: #344054;
  transition: all 0.2s ease;
}

:deep(.defect-description-editor .tiptap-toolbar button:hover) {
  background-color: #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar button.is-active) {
  background-color: #1976D2;
  color: white;
}

/* Simple media styling */
:deep(.defect-description-editor .ProseMirror img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

:deep(.defect-description-editor .ProseMirror video) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}
</style>