<template>
  <div>
    <v-dialog
      v-model="createDefectDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      persistent
      width="485px"
    >
      <!-- Show loader when loading -->
      <template v-if="isLoading">
        <v-card>
          <v-overlay
            :value="true"
            absolute
          >
            <v-progress-circular
              indeterminate
              size="64"
            />
          </v-overlay>
        </v-card>
      </template>

      <!-- Show empty state when no integrations -->
      <template v-else-if="totalIntegrations === 0">
        <v-card class="d-flex flex-column fill-height">
          <!-- Header -->
          <div class="px-4 pt-6 pb-4">
            <div class="d-flex align-center justify-space-between">
              <h2 class="black--text">
                {{
                  actionSelected === 'Create new defect'
                    ? $t('defect.createNewDefectDialog.title')
                    : $t('defect.createNewDefectDialog.linkTitle')
                }}
              </h2>
              <v-btn
                icon
                @click="closeDrawer"
              >
                <v-icon color="black">
                  mdi-close
                </v-icon>
              </v-btn>
            </div>
          </div>
          <v-card-text class="d-flex flex-column align-center justify-center flex-grow-1">
            <h3 class="mb-4">
              {{ $t('defect.createNewDefectDialog.noIntegrations') }}
            </h3>
            <p class="mb-6 text-grey">
              {{ $t('defect.createNewDefectDialog.addIntegrationMessage') }}
            </p>
          </v-card-text>
        </v-card>
      </template>

      <!-- Show normal form when integrations exist -->
      <template v-else>
        <v-card class="d-flex flex-column fill-height">
          <v-card-text
            v-if="saveLoading"
            class="flex-grow-1 pa-0"
          >
            <!-- Header -->
            <div class="px-4 pt-6 pb-4">
              <div class="d-flex align-center justify-space-between">
                <h2 class="black--text">
                  {{
                    actionSelected === 'Create new defect'
                      ? $t('defect.createNewDefectDialog.title')
                      : $t('defect.createNewDefectDialog.linkTitle')
                  }}
                </h2>
                <v-btn
                  icon
                  @click="closeDrawer"
                >
                  <v-icon color="black">
                    mdi-close
                  </v-icon>
                </v-btn>
              </div>
            </div>

            <div class="d-flex mx-auto justify-center flex-column h-100">
              <v-progress-circular
                class="position-relative"
                :size="150"
                width="6"
                color="primary"
                indeterminate
              >
                <div class="svg-container">
                  <UploadCloud class="upload-cloud-icon" />
                </div>
              </v-progress-circular>
              <p class="title font-weight-regular text-center mt-4 mx-auto">
                {{ $t('defect.createNewDefectDialog.uploadingData', { service: selectedIntegration.service }) }}...
              </p>
            </div>
          </v-card-text>
          <v-card-text
            v-else
            class="flex-grow-1 pa-0"
          >
            <!-- Header -->
            <div class="px-4 pt-6 pb-1">
              <div class="d-flex align-center justify-space-between">
                <h2 class="black--text">
                  {{
                    actionSelected === 'Create new defect'
                      ? $t('defect.createNewDefectDialog.title')
                      : $t('defect.createNewDefectDialog.linkTitle')
                  }}
                </h2>
                <v-btn
                  icon
                  @click="closeDrawer"
                >
                  <v-icon color="black">
                    mdi-close
                  </v-icon>
                </v-btn>
              </div>
            </div>
            <!-- Service selection section -->
            <div class="px-4 mb-6">
              <div class="d-flex align-center">
                <span class="text-grey-darken-1 text-body-2">
                  {{ $t('defect.createNewDefectDialog.projectIntegration') }}: {{ getServiceName(selectedService) }}
                </span>

                <span
                  v-if="services.length > 1"
                  class="text-primary cursor-pointer text-body-2 ml-2"
                  style="color: #0c2ff3; font-weight: 500"
                  @click="toggleService"
                >
                  {{ $t('defect.createNewDefectDialog.change') }}
                </span>
              </div>
            </div>

            <!-- Scrollable Form Content -->
            <div class="form-wrapper">
              <v-form class="px-4">
                <div>
                  <!-- INTEGRATION SELECT -->
                  <div class="d-flex flex-column mb-6">
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ $t('defect.createNewDefectDialog.integrationLabel') }} <span
                          v-if="actionSelected === 'Create new defect'"
                          class="required-asterisk"
                        >*</span>
                      </v-label>
                    </div>
                    <v-select
                      v-model="selectedIntegration.integrationUid"
                      type="text"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.selectIntegration')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                      append-icon="mdi-chevron-down"
                      :items="integrationsForSelect"
                      item-text="title"
                      item-value="value"
                      :menu-props="{ offsetY: true }"
                      @change="onIntegrationSelected"
                    />
                  </div>

                  <!-- DEFECT NAME/SUMMARY (hide for custom integrations) -->
                  <div
                    v-if="actionSelected === 'Create new defect' && selectedService !== 'custom'"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ selectedService === 'github' ? $t('defect.createNewDefectDialog.nameLabel') : $t('defect.createNewDefectDialog.summary') }}
                        <span class="required-asterisk">*</span>
                      </v-label>
                    </div>
                    <v-text-field
                      v-model="defectName"
                      type="text"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="selectedService === 'github' ? $t('defect.createNewDefectDialog.enterName') : $t('defect.createNewDefectDialog.summary')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                    />
                  </div>

                  <!-- DEFECT DESCRIPTION (hide for custom integrations) -->
                  <div
                    v-if="actionSelected === 'Create new defect' && selectedService !== 'custom'"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ $t('defect.createNewDefectDialog.descriptionLabel') }} <span class="required-asterisk">*</span>
                      </v-label>
                    </div>
                    <TiptapEditor
                      v-model="defectDescription"
                      class="defect-description-editor"
                    />
                  </div>



                  <!-- DEFECT SELECTION (only for linking) - Jira/GitHub -->
                  <div
                    v-if="actionSelected === 'Link existing defect' && (selectedIntegration.service === 'jira' || selectedIntegration.service === 'github')"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{
                          $t('defect.createNewDefectDialog.libraryLabel', {
                            service: selectedIntegration.service ? selectedIntegration.service : 'Service',
                          })
                        }}
                      </v-label>
                    </div>

                    <v-select
                      v-model="selectedDefect"
                      :items="filteredDefects"
                      :loading="isLoadingDefects"
                      :disabled="isLoadingDefects"
                      item-text="name"
                      item-value="id"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.selectDefect')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                      append-icon="mdi-chevron-down"
                      :menu-props="{ offsetY: true }"
                    >
                      <template #prepend-item>
                        <v-text-field
                          v-model="searchQuery"
                          dense
                          background-color="#F9F9FB"
                          :placeholder="$t('defect.createNewDefectDialog.searchPlaceholder')"
                          class="mx-2 mt-2"
                          hide-details
                          clearable
                        >
                          <template #prepend-inner>
                            <v-icon size="18">
                              mdi-magnify
                            </v-icon>
                          </template>
                        </v-text-field>
                      </template>
                    </v-select>
                  </div>

                  <!-- CUSTOM INTEGRATION MULTI-INPUT -->
                  <div
                    v-if="actionSelected === 'Link existing defect' && selectedIntegration.service === 'custom'"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ $t('defect.createNewDefectDialog.customDefectIds') }}
                      </v-label>
                    </div>
                    <v-combobox
                      v-model="customDefectIds"
                      multiple
                      chips
                      small-chips
                      deletable-chips
                      clearable
                      hide-selected
                      hide-details
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.enterCustomDefectIds')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      @keydown.enter.native.stop
                    />
                    <p class="text-caption text-grey mt-1">
                      {{ $t('defect.createNewDefectDialog.customDefectIdsHelp') }}
                    </p>
                  </div>
                </div>

                <!-- GITHUB LABELS -->
                <div
                  v-if="selectedService === 'github' && actionSelected === 'Create new defect'"
                  class="d-flex flex-column mb-6"
                >
                  <div class="text-left">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{ $t('defect.createNewDefectDialog.tagsLabel') }}
                    </v-label>
                  </div>
                  <v-select
                    v-model="selectedIntegration.tags"
                    :items="githubTags"
                    type="text"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.selectTags')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    append-icon="mdi-chevron-down"
                    item-text="name"
                    item-value="name"
                    :menu-props="{
                      offsetY: true,
                      closeOnClick: false,
                      closeOnContentClick: false,
                    }"
                    multiple
                    chips
                    deletable-chips
                  >
                    <!-- Add Select All option -->
                    <template #prepend-item>
                      <v-list-item
                        ripple
                        @click="toggleSelectAll"
                      >
                        <v-list-item-action>
                          <v-checkbox
                            :input-value="isAllSelected"
                            :indeterminate="isIndeterminate"
                            class="field-theme"
                            :ripple="false"
                            off-icon="icon-checkbox-off"
                            on-icon="icon-checkbox-on"
                            indeterminate-icon="icon-indeterminate"
                            :hide-details="true"
                            dense
                          />
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>
                            <span class="fs-14px text-theme-label">{{ $t('selectAll') }}</span>
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                      <v-divider />
                    </template>

                    <!-- Selection template for displaying selected tags as chips -->
                    <template #selection="{ item, index }">
                      <v-chip
                        v-if="index < 3"
                        small
                        class="ma-1"
                        close
                        @click:close="removeTag(item.name)"
                      >
                        <span style="color: #3f69cc">{{ item.name }}</span>
                      </v-chip>
                      <span
                        v-if="index === 3"
                        class="text-caption grey--text text-truncate"
                      >
                        (+{{ selectedIntegration.tags.length - 3 }} {{ $t('more') }})
                      </span>
                    </template>

                    <!-- Item template for the dropdown list -->
                    <template #item="{ item, on }">
                      <v-list-item
                        v-bind="on"
                        @click.stop="toggleTag(item)"
                      >
                        <v-list-item-action>
                          <v-checkbox
                            :input-value="isTagSelected(item.name)"
                            class="field-theme"
                            :ripple="false"
                            off-icon="icon-checkbox-off"
                            on-icon="icon-checkbox-on"
                            :hide-details="true"
                            dense
                          />
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>
                            <span class="fs-14px text-theme-label">{{ item.name }}</span>
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </template>
                  </v-select>
                </div>

                <!-- INTEGRATION DEFECT COMPONENT (hide for custom integrations) -->
                <IntegrationDefectComponent
                  v-if="selectedService !== 'custom'"
                  ref="integrationDefectComponent"
                  :action-selected="actionSelected"
                  :defects="existingDefects"
                  :current-integration="currentIntegration"
                  :testfiesta-project-uid="testfiestaProjectUid"
                  :selected-service="selectedService"
                  :integration-state="integrationState"
                  @update:state="updateIntegrationState"
                />
                <div v-if="selectedService !== 'custom'">
                  <!-- ATTACHMENTS -->
                  <div class="defect-attachment-section">
                    <div class="text-left mb-4">
                      <span class="section-label">
                        {{ $t('defect.createNewDefectDialog.attachmentsLabel') }}
                      </span>
                    </div>

                    <div class="upload-container-wrapper">
                      <div
                        class="upload-container"
                        @click="openFileDialog"
                        @drop.prevent="handleDrop"
                        @dragover.prevent
                        @dragenter.prevent
                      >
                        <input
                          ref="fileInput"
                          type="file"
                          multiple
                          style="display: none"
                          @change="handleFileChange"
                        >

                        <p class="upload-text">
                          {{ $t('defect.createNewDefectDialog.uploadInstructions') }}
                        </p>

                        <div class="browse-files-btn">
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            class="arrow-icon"
                          >
                            <path
                              d="M3.33337 8H12.6667"
                              stroke="#0C2FF3"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M8.66663 4L12.6666 8L8.66663 12"
                              stroke="#0C2FF3"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          {{ $t('defect.createNewDefectDialog.browseFiles') }}
                        </div>
                      </div>
                    </div>

                    <!-- File List -->
                    <template v-if="attachments.length > 0">
                      <div class="file-list mt-4">
                        <div
                          v-for="(attachment, index) in attachments"
                          :key="index"
                          class="file-item d-flex align-center justify-space-between py-2 px-3"
                        >
                          <div class="d-flex align-center">
                            <v-icon
                              size="20"
                              class="mr-2"
                              color="grey darken-1"
                            >
                              mdi-file-outline
                            </v-icon>
                            <span class="file-name">{{ attachment.fileName }}</span>
                          </div>
                          <v-btn
                            icon
                            x-small
                            @click="removeAttachment(index)"
                          >
                            <v-icon size="16">
                              mdi-close
                            </v-icon>
                          </v-btn>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </v-form>
            </div>
          </v-card-text>

          <!-- Action Buttons -->
          <div
            v-if="!saveLoading"
            class="actions-container"
          >
            <v-btn
              width="204.5px"
              color="#F2F4F7"
              full-width
              height="40"
              :depressed="true"
              class="text-capitalize btn-theme"
              elevation="0"
              @click="handleCancel"
            >
              {{ $t('cancel') }}
            </v-btn>
            <v-btn
              width="204.5px"
              class="btn-theme text-capitalize"
              height="40"
              color="primary"
              :depressed="true"
              full-width
              elevation="0"
              @click="handleAction"
            >
              <span>{{ actionButtonText }}</span>
            </v-btn>
          </div>
        </v-card>
      </template>
    </v-dialog>
  </div>
</template>

<script>
import makeDefectService from '@/services/api/defect';
import makeIntegrationsService from '@/services/api/integrations';
import IntegrationDefectComponent from '@/components/Integration/IntegrationDefect.vue';
import makeTagService from '@/services/api/tag';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { mapActions } from 'vuex';
import UploadCloud from '@/assets/svg/upload-cloud.svg';
import TiptapEditor from '@/components/base/TipTapEditor.vue';
import convertToADF from 'html-to-adf-converter';

export default {
  name: 'CreateDefect',
  components: {
    IntegrationDefectComponent,
    UploadCloud,
    TiptapEditor,
  },
  props: {
    selectedProjectKey: {
      type: String,
      default: null,
    },
    execution: {
      type: Object,
      required: true,
    },
    resultUid: {
      type: String,
      default: null,
    },
    actionSelected: {
      type: String,
      required: true,
    },
    createDefectDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      isLoading: true,
      integrations: [],
      integrationsForSelect: [],
      currentIntegration: {},
      testfiestaProjectUid: null,
      selectedIntegration: {
        integrationUid: null,
        service: null,
        tags: [],
      },
      attachments: [],
      searchQuery: '',
      totalIntegrations: 0,
      defectName: '',
      selectedDefect: null,
      existingDefects: [],
      defectDescription: '',
      saveLoading: false,
      githubTags: [],
      isLoadingTags: false,
      fieldRules: {
        required: (v) => !!v || this.$t('defect.createNewDefectDialog.fieldRequired'),
      },
      isLoadingDefects: false,
      services: [],
      selectedService: '',
      serviceIndex: 0,

      integrationState: {
        jiraOrganizations: [],
        serviceProjects: [],
        resourceId: null,
        projectId: null,
        projectName: null,
        typeId: null,
        issueTypes: [],
        fieldData: [],
        blankIssue: {
          fields: {},
        },
        assignees: [],
        reporters: [],
        sprints: [],
        boards: [],
        selectedBoard: null,
        selectedSprint: null,
      },
      customDefectIds: [],
      rules: {
        required: (v) => !!v || this.$t('defect.createNewDefectDialog.fieldRequired'),
      },
    };
  },
  computed: {
    isJiraDefectCreation() {
      return this.selectedIntegration.service === 'jira' && this.actionSelected === 'Create new defect';
    },
    actionButtonText() {
      if (this.actionSelected === 'Create new defect') {
        return this.$t('defect.createNewDefectDialog.title');
      }
      return this.$t('defect.createNewDefectDialog.addDefect');
    },
    filteredDefects() {
      let defects = [...this.existingDefects]; // Work with a copy of the original list

      // First filter by integration if one is selected
      if (this.selectedIntegration.integrationUid) {
        defects = defects.filter(
          (defect) => Number(defect.integrationSourceUid) === Number(this.selectedIntegration.integrationUid)
        );
      }

      // Then filter by search query if one exists
      if (this.searchQuery) {
        defects = defects.filter((defect) => defect.name.toLowerCase().includes(this.searchQuery.toLowerCase()));
      }

      return defects;
    },
    isAllSelected() {
      return this.githubTags.length > 0 && this.selectedIntegration.tags.length === this.githubTags.length;
    },
    isIndeterminate() {
      return this.selectedIntegration.tags.length > 0 && this.selectedIntegration.tags.length < this.githubTags.length;
    },
    customIntegrationsForCreate() {
      return this.integrations
        .filter((integration) => integration.service === 'custom')
        .map((integration) => ({
          uid: integration.uid,
          name: integration.name,
          addUrl: integration.configuration?.addUrl || '',
        }));
    },
  },
  created() {
    this.initializeData();
  },
  methods: {
    ...mapActions({
      uploadToServer: 'attachment/uploadToServer',
    }),
    getServiceName(service) {
      switch (service) {
        case 'jira':
          return 'Jira';
        case 'github':
          return 'GitHub';
        default:
          return service;
      }
    },
    closeDrawer() {
      // Reset all selections
      this.selectedIntegration = {
        integrationUid: null,
        service: null,
        tags: [],
      };
      this.currentIntegration = {};
      this.defectName = '';
      this.defectDescription = '';
      this.$emit('closeDialog');
    },
    openCustomIntegrationUrl(integration) {
      // Find the full integration object to get the addUrl
      const fullIntegration = this.integrations.find(i => i.uid === integration.value);
      if (fullIntegration?.configuration?.addUrl) {
        window.open(fullIntegration.configuration.addUrl, '_blank');
      } else {
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.noAddUrlConfigured'),
          {},
          {}
        );
      }
    },


    async initializeData() {
      await this.fetchIntegrationData(), this.fetchExistingDefects(), this.fetchDefectTags();
    },

    async fetchIntegrationData() {
      this.isLoading = true;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const handle = this.$route.params.handle;
        const params = this.selectedProjectKey
          ? `service=jira,github,custom&projectKey=${this.selectedProjectKey}`
          : `service=jira,github,custom&projectKey=${this.$route.params.key}`;
        const response = await integrationsService.getIntegrations(handle, params);

        if (!response?.data) {
          this.totalIntegrations = 0;
          return;
        }

        this.totalIntegrations = response.data.pagination?.total || 0;

        if (this.totalIntegrations === 0) {
          return;
        }

        // Identify the testfiesta project UID from route
        let currentProjectKey = this.$route.params.key;
        if (this.selectedProjectKey) {
          currentProjectKey = this.selectedProjectKey;
        }
        const foundEntry = Object.entries(response.data.projects).find(
          ([, project]) => project.key === currentProjectKey
        );

        if (!foundEntry) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.projectNotFound'));
          return;
        }

        this.testfiestaProjectUid = foundEntry[0];

        // Filter integrations - include custom integrations regardless of projectConfigurations
        this.integrations = response.data.integrations.filter((i) => {
          if (i.service === 'custom') {
            return true; // Include all custom integrations
          }
          return i.configuration?.projectConfigurations?.length; // For other services, check projectConfigurations
        });
        this.services = response.data.services.filter((service) => service === 'github' || service === 'jira' || service === 'custom');
        this.selectedService = this.services[this.serviceIndex];
        // Prepare for the integration dropdown - include custom integrations
        this.integrationsForSelect = this.integrations
          .filter((i) => i.service === this.selectedService)
          .map((i) => ({
            title: i.name,
            value: i.uid,
            service: i.service,
          }));
        
        console.log('Selected service:', this.selectedService);
        console.log('All integrations:', this.integrations);
        console.log('Integrations for select:', this.integrationsForSelect);
        
        // Add custom integrations to the dropdown
        const customIntegrations = this.integrations.filter((i) => i.service === 'custom');
        const customIntegrationOptions = customIntegrations.map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
        this.integrationsForSelect = [...this.integrationsForSelect, ...customIntegrationOptions];
      } catch (err) {
        console.error('Integration Error:', err);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchIntegrations'),
          {},
          err?.response?.data
        );
      } finally {
        this.isLoading = false;
      }
    },
    resetIntegrationState() {
      this.integrationState = {
        jiraOrganizations: [],
        serviceProjects: [],
        resourceId: null,
        projectId: null,
        projectName: null,
        typeId: null,
        issueTypes: [],
        fieldData: [],
        blankIssue: {
          fields: {},
        },
        assignees: [],
        reporters: [],
        sprints: [],
        boards: [],
        selectedBoard: null,
        selectedSprint: null,
      };
    },
    async onIntegrationSelected() {
      const chosenUID = this.selectedIntegration.integrationUid;
      if (!chosenUID) {
        this.currentIntegration = {};
        this.selectedIntegration.service = null;
        this.resetIntegrationState();
        return;
      }

      const found = this.integrations.find((i) => i.uid === chosenUID);
      if (!found) {
        this.currentIntegration = null;
        this.selectedIntegration.service = null;
        this.resetIntegrationState();
        return;
      }

      // Handle custom create new defect case
      if (found.service === 'custom' && this.actionSelected === 'Create new defect') {
        this.openCustomIntegrationUrl({ value: chosenUID });
        this.selectedIntegration.integrationUid = null; // Reset selection
        showSuccessToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.customIntegrationOpened')
        );
        this.$emit('closeDialog');
        return;
      }

      // Reset state before setting new integration
      this.resetIntegrationState();
      this.customDefectIds = [];

      this.currentIntegration = found;
      this.selectedIntegration.service = found.service.toLowerCase();
    },
    openFileDialog() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      const files = Array.from(event.target.files);
      files.forEach((file) => {
        const attachment = {
          file,
          fileName: file.name,
          extension: file.name.split('.').pop(),
          size: Math.round(file.size / 1024),
          progress: 0,
          failed: false,
        };
        this.attachments.push(attachment);
      });
      this.$refs.fileInput.value = '';
    },
    handleDrop(event) {
      event.preventDefault();
      const files = Array.from(event.dataTransfer.files);
      files.forEach((file) => {
        const attachment = {
          file,
          fileName: file.name,
          extension: file.name.split('.').pop(),
          size: Math.round(file.size / 1024),
          progress: 0,
          failed: false,
        };
        this.attachments.push(attachment);
      });
    },
    removeAttachment(index) {
      this.attachments.splice(index, 1);
    },
    async uploadDefectAttachments(defectId) {
      if (!this.attachments.length) return;

      const handle = this.$route.params.handle;
      let projectKey = this.$route.params.key;
      if (this.selectedProjectKey) {
        projectKey = this.selectedProjectKey;
      }
      const defectService = makeDefectService(this.$api);
      const mediaType = 'attachment';

      try {
        const uploadPromises = this.attachments.map((attachment) => {
          const params = {
            handle,
            projectKey,
            defectId,
            relatedTo: 'defect',
          };

          return this.uploadToServer({
            handle,
            mediaType,
            file: attachment.file,
            apiService: defectService,
            params,
          });
        });

        return await Promise.all(uploadPromises);
      } catch (error) {
        if (error?.status == 507)
          showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
        else
          console.error('Failed to upload attachments:', error);
        throw error;
      }
    },
    async saveDefect() {
      // Create defect validation
      if (this.actionSelected === 'Create new defect') {
        if (!this.selectedIntegration.integrationUid) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.integrationSelectError'));
          return;
        }
        if (!this.defectName?.trim()) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.nameError'));
          return;
        }
        if (!this.defectDescription?.trim()) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.descriptionError'));
          return;
        }
        if (this.selectedIntegration.service === 'jira') {
          if (!this.$refs.integrationDefectComponent?.validateStep(2)) {
            return;
          }
        }
      }

      // Select defect validation
      if (this.actionSelected === 'Select defects') {
        if (!this.selectedDefect) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.defectSelectError'));
          return;
        }
      }

      // Custom integration validation for link existing defect
      if (this.actionSelected === 'Link existing defect' && this.selectedIntegration.service === 'custom') {
        if (!this.customDefectIds || this.customDefectIds.length === 0) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.customDefectIdsRequired'));
          return;
        }
      }
      const data = this.$refs.integrationDefectComponent?.makeDefectData();
      this.saveLoading = true;
      try {
        const handle = this.$route.params.handle;
        let projectKey = this.$route.params.key;
        if (this.selectedProjectKey) {
          projectKey = this.selectedProjectKey;
        }
        const defectService = makeDefectService(this.$api);

        let description = this.defectDescription.trim();
        
        // Convert HTML description to ADF for Jira
        if (this.selectedIntegration.service === 'jira' && description) {
          try {
            // Convert HTML to ADF format
            description = convertToADF(description);
          } catch (error) {
            console.error('Error converting HTML to ADF:', error);
            // Fallback to original description if conversion fails
          }
        }

        // Create the defect data without files
        const defectData = {
          name: this.defectName.trim(),
          description: description,
          tags: this.selectedIntegration.service === 'github' ? this.selectedIntegration.tags : undefined,
          executionId: this.execution?.uid,
          resultUid: this.resultUid,
          testRunUid: this.execution?.testRunUid,
          testCaseUid: this.execution?.testCaseUid,
          integrationUid: this.selectedIntegration.integrationUid,
          ...data,
        };

        // Add custom defect IDs for custom integrations
        if (this.actionSelected === 'Link existing defect' && this.selectedIntegration.service === 'custom') {
          defectData.customDefectIds = this.customDefectIds;
        }

        let response;
        if (this.actionSelected === 'Create new defect') {
          response = await defectService.createDefect(handle, projectKey, defectData);
          this.selectedDefect = response.data.uid;
          this.$emit('defectCreated');
        } else if (this.actionSelected === 'Link existing defect' && this.selectedIntegration.service === 'custom') {
          // Use the new external link API for custom integrations
          response = await defectService.linkExternalDefect(handle, projectKey, defectData);
          this.$emit('defectLinked');
        } else {
          response = await defectService.linkDefect(handle, projectKey, this.selectedDefect, defectData);
          this.$emit('defectLinked');
        }

        // If we have attachments, upload them
        if (this.attachments.length > 0) {
          try {
            await this.uploadDefectAttachments(this.selectedDefect);
          } catch (error) {
            console.error('Failed to upload attachments:', error);
            showErrorToast(
              this.$swal,
              this.$t('defect.createNewDefectDialog.failedToUploadAttachments'),
              {},
              error?.response?.data
            );
          }
        }

        showSuccessToast(
          this.$swal,
          this.actionSelected === 'Create new defect' ? this.$t('success.defectCreated') : this.$t('success.defectLinked')
        );
        this.saveLoading = false;
        this.$emit('closeDialog');
      } catch (error) {
        console.error('Failed to save defect:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.defectCreateFailed'),
          {},
          error?.response?.data
        );
        if (error.response.data.data) {
          showErrorToast(this.$swal, error.response.data.data);
        }
      } finally {
        this.saveLoading = false;
      }
    },
    async fetchExistingDefects() {
      this.isLoadingDefects = true;
      try {
        const defectService = makeDefectService(this.$api);
        const handle = this.$route.params.handle;
        let projectKey = this.$route.params.key;
        if (this.selectedProjectKey) {
          projectKey = this.selectedProjectKey;
        }
        const response = await defectService.getDefects(handle, projectKey, {
          page: 1,
          limit: 99999,
          integration: this.selectedService,
        });

        // Filter out closed defects using the same logic as the table
        this.existingDefects = response.data.data
          .filter((defect) => {
            const isClosedStatus =
              defect.customFields?.state === 'done' ||
              defect.customFields?.state === 'closed' ||
              defect.customFields?.status?.statusCategory?.key === 'done' ||
              defect.customFields?.status?.statusCategory?.key === 'closed';
            return !isClosedStatus; // Only keep open defects
          })
          .map((defect) => ({
            id: defect.uid,
            name: defect.name,
            externalId: defect.externalId,
            integrationSourceUid: defect.integrationSourceUid,
            projectScope: defect.customFields?.projectScope,
          }));
      } catch (err) {
        console.error('Failed to fetch defects:', err);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchDefects'),
          {},
          err?.response?.data
        );
      } finally {
        this.isLoadingDefects = false;
      }
    },
    async fetchDefectTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, 'defects');
        this.githubTags = response.data;
      } catch (err) {
        console.error('Error fetching defect tags:', err);
        showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.failedToFetchTags'), {}, err?.response?.data);
      }
    },
    handleCancel() {
      this.closeDrawer();
    },
    async handleAction() {
      if (this.isJiraDefectCreation) {
        if (!this.validateStep1()) {
          return;
        }
      } else {
        if (this.actionSelected === 'Create new defect' && !this.$refs.integrationDefectComponent?.validateStep(1)) {
          return;
        }
      }
      await this.saveDefect();
    },
    validateStep1() {
      if (!this.selectedIntegration.integrationUid) {
        showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.integrationSelectError'));
        return false;
      }
      if (!this.defectName) {
        const item= this.selectedService === 'github' ? 'title' : 'summary';
        showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.nameError', {item}));
        return false;
      }
      if (!this.defectDescription) {
        showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.descriptionError'));
        return false;
      }
      if (!this.$refs.integrationDefectComponent?.validateStep(1)) {
        return false;
      }
      return true;
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedIntegration.tags = [];
      } else {
        this.selectedIntegration.tags = this.githubTags.map((tag) => tag.name);
      }
    },
    removeTag(tagName) {
      const index = this.selectedIntegration.tags.indexOf(tagName);
      if (index >= 0) {
        this.selectedIntegration.tags.splice(index, 1);
      }
    },
    toggleTag(item) {
      const index = this.selectedIntegration.tags.indexOf(item.name);
      if (index === -1) {
        this.selectedIntegration.tags.push(item.name);
      } else {
        this.selectedIntegration.tags.splice(index, 1);
      }
    },
    isTagSelected(tagName) {
      return this.selectedIntegration.tags.includes(tagName);
    },
    toggleService() {
      this.serviceIndex = (this.serviceIndex + 1) % this.services.length;
      this.selectedService = this.services[this.serviceIndex];

      // Reset selections when switching services
      this.selectedIntegration = {
        integrationUid: null,
        service: null,
        tags: [],
      };
      this.resetIntegrationState();
      this.fetchExistingDefects();
      // Update integrations list for the new service
      this.integrationsForSelect = this.integrations
        .filter((i) => i.service === this.selectedService)
        .map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
    },
    updateIntegrationState({ key, value }) {
      this.integrationState[key] = value;
    },
  },
};
</script>

<style scoped>
.dialog-theme {
  overflow: hidden !important;
}

:deep(.v-dialog--fullscreen) {
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

:deep(.v-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.form-wrapper) {
  flex: 1;
  overflow-y: auto;
}

.form-wrapper {
  background-color: white !important;
}
:deep(.form-wrapper .v-form) {
  padding: 0 24px;
  width: 100%;
  background-color: white !important;
  box-sizing: border-box;
}

:deep(.actions-container) {
  position: fixed;
  bottom: 0;
  width: 100%;
  max-width: 485px;
  padding: 16px 24px;
  background-color: white;
  z-index: 1;
  border-top: 1px solid #f2f4f7;
  display: flex;
  justify-content: space-between;
  gap: 16px;
  box-sizing: border-box;
}

:deep(.actions-container .v-btn) {
  flex: 1;
  min-width: 0 !important; /* Prevent button from expanding */
}

:deep(.v-text-field.custom-prepend .v-input__prepend-inner) {
  margin-top: 10px !important;
  margin-right: 8px !important;
}

/* Add to existing styles */
.attachment-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.attachment-item {
  border-bottom: 1px solid #eaecf0;
  background-color: #f9f9fb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.upload-zone {
  background-color: #f9f9fb;
  border: 1px dashed #e4e7ec;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-zone:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-text {
  color: #667085;
  font-size: 14px;
  margin: 0;
}

.selected-files {
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  font-size: 14px;
  color: #344054;
}

.upload__attachment {
  background-color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #eaecf0;
  width: 100%;
  gap: 12px;
  margin-bottom: 8px;
}

.attachment_file-type {
  position: relative;
  display: flex;
  align-items: center;
}

.attachment_file-type .v-icon {
  z-index: 5;
  font-size: 55px !important;
}

.attachment_file-type span {
  position: absolute;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 2px;
  color: #fff;
  z-index: 6;
}

.attachment_file-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.attachment_file-details p {
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  color: #344054;
}

.attachment_file-details #fileName {
  white-space: nowrap;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment_file-details .attachment_progress {
  gap: 5px;
}

.toggle-btn {
  box-shadow: none !important;
}

.defect-attachment-section {
  margin-bottom: 32px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #344054;
  display: block;
}

.upload-container-wrapper {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px 8px;
  width: 100%;
  max-width: 421px;
}

.upload-container {
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  background-color: #f9fafb;
  min-height: 120px;
}

.upload-container:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}

.upload-text {
  color: #667085;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  margin: 0;
  max-width: 300px;
}

.browse-files-btn {
  color: #0c2ff3;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow-icon {
  transform: rotate(-45deg);
  margin-right: 4px;
}

.file-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  font-size: 14px;
  color: #344054;
}

:deep(.custom-chip-theme) {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  height: 24px !important;
}

:deep(.custom-chip-theme .v-chip__close) {
  opacity: 1;
  font-size: 16px;
}

:deep(.v-select.v-text-field input) {
  max-height: none;
  height: auto;
}

:deep(.v-select__selections) {
  padding: 0 8px;
  flex-wrap: wrap;
}

.h-100 {
  height: 100%;
}
.position-relative {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px; /* Ensures container has height */
}
.svg-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-cloud-icon {
  color: #000000; /* Makes the icon black */
}

.v-select__selections .v-chip:not(.v-chip--active) {
  background: #e6ebf6 !important;
  border: solid 2px #3f69cc80 !important;
  border-radius: 50px !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.v-select__selections .v-chip--select.v-chip--active {
  border-radius: 12px !important;
  border: solid 2px #3f69cc80 !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.required-asterisk {
  color: red;
  font-size: 14px;
  font-weight: bold;
  margin-left: 4px;
}

/* TipTap Editor Styles for Defect Description */
:deep(.defect-description-editor .tiptap-theme) {
  background-color: #F9F9FB;
  border-radius: 8px;
  border: 1px solid #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar) {
  background-color: #F9F9FB;
  border-bottom: 1px solid #E0E0E0;
  border-radius: 8px 8px 0 0;
}

:deep(.defect-description-editor .tiptap-editor) {
  background-color: #F9F9FB;
  border: none;
  border-radius: 0 0 8px 8px;
  min-height: 120px;
}

:deep(.defect-description-editor .tiptap-footer) {
  background-color: #F9F9FB;
  border-top: 1px solid #E0E0E0;
  border-radius: 0 0 8px 8px;
}

:deep(.defect-description-editor .ProseMirror) {
  background-color: #F9F9FB;
  padding: 12px;
  min-height: 100px;
  border: none;
  outline: none;
}

:deep(.defect-description-editor .ProseMirror:focus) {
  outline: none;
  border: none;
}

:deep(.defect-description-editor .tiptap-toolbar button) {
  color: #344054;
  transition: all 0.2s ease;
}

:deep(.defect-description-editor .tiptap-toolbar button:hover) {
  background-color: #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar button.is-active) {
  background-color: #1976D2;
  color: white;
}

/* Enhanced image handling for TipTap editor in Create Defect */
:deep(.defect-description-editor .ProseMirror) {
  overflow: hidden !important;
  word-wrap: break-word !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force all content to be contained */
:deep(.defect-description-editor .ProseMirror *) {
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Comprehensive image styling for create mode */
:deep(.defect-description-editor .ProseMirror img) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.defect-description-editor .ProseMirror img:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure all content is properly contained */
:deep(.defect-description-editor .ProseMirror p) {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.defect-description-editor .ProseMirror *:not(img)) {
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Container overflow control */
:deep(.defect-description-editor) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.defect-description-editor .tiptap-theme) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.defect-description-editor .tiptap-editor) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force the editor content to respect boundaries */
:deep(.defect-description-editor .tiptap-editor .ProseMirror) {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Additional constraints for dialog content */
:deep(.v-dialog__content) {
  overflow: hidden !important;
  max-width: 100% !important;
}

:deep(.v-card__text) {
  overflow: hidden !important;
  max-width: 100% !important;
}

:deep(.v-col) {
  overflow: hidden !important;
  max-width: 100% !important;
}

/* Force all content within the dialog to be contained */
:deep(.v-dialog *) {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Additional styling for better image display */
:deep(.defect-description-editor .defect-description-image) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.defect-description-editor .defect-description-image:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure proper text wrapping around images */
:deep(.defect-description-editor .ProseMirror) {
  line-height: 1.5 !important;
}

:deep(.defect-description-editor .ProseMirror h1),
:deep(.defect-description-editor .ProseMirror h2),
:deep(.defect-description-editor .ProseMirror h3),
:deep(.defect-description-editor .ProseMirror h4),
:deep(.defect-description-editor .ProseMirror h5),
:deep(.defect-description-editor .ProseMirror h6) {
  margin: 16px 0 8px 0 !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* List styling */
:deep(.defect-description-editor .ProseMirror ul),
:deep(.defect-description-editor .ProseMirror ol) {
  max-width: 100% !important;
  word-wrap: break-word !important;
  padding-left: 20px !important;
}

:deep(.defect-description-editor .ProseMirror li) {
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* Code block styling */
:deep(.defect-description-editor .ProseMirror pre) {
  max-width: 100% !important;
  overflow-x: auto !important;
  word-wrap: break-word !important;
  background: #f6f8fa !important;
  padding: 12px !important;
  border-radius: 6px !important;
  margin: 12px 0 !important;
  border: 1px solid #e1e4e8 !important;
}

:deep(.defect-description-editor .ProseMirror code) {
  background: #f6f8fa !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.9em !important;
  word-wrap: break-word !important;
}

/* Blockquote styling */
:deep(.defect-description-editor .ProseMirror blockquote) {
  border-left: 4px solid #1976D2 !important;
  padding-left: 16px !important;
  margin: 12px 0 !important;
  color: #666 !important;
  font-style: italic !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* Link styling */
:deep(.defect-description-editor .ProseMirror a) {
  color: #1976D2 !important;
  text-decoration: underline !important;
  word-break: break-word !important;
  max-width: 100% !important;
}

:deep(.defect-description-editor .ProseMirror a:hover) {
  color: #1565C0 !important;
}
</style>


