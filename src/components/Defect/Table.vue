<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table data-table-style mt-6"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      hide-default-footer
      disable-pagination
      @click:row="$emit('view', $event)"
    >
      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div 
              class="custom-attribute text-truncate cursor-pointer"
              v-bind="attrs"
              v-on="on"
            >
              <span
                v-if="integrationType == 'Jira'"
                class="font-weight-bold"
              >{{ item.name.length > 40 ? item.name.slice(0, 40) + '...' : item.name }}</span>
              <span v-else>{{ item.name.length > 20 ? item.name.slice(0, 20) + '...' : item.name }}</span>
            </div>
          </template>
          <span class="cursor-pointer">{{ item.name }}</span>
        </v-tooltip>
      </template>
      <template #[`item.priority`]="{ item }">
        <v-chip
          v-if="item.priority"
          label
          class="status-chip"
          :style="{
            color: getPriorityColor(item.priority),
            backgroundColor: `${getPriorityColor(item.priority)}10`,
          }"
        >
          <div class="px-2 font-weight-bold">
            {{ getPriorityName(item.priority) }}
          </div>
        </v-chip>
        <v-chip
          v-else
          label
          class="status-chip"
        >
          <div class="px-2 font-weight-bold">
            {{ 'None' }}
          </div>
        </v-chip>
      </template>

      <template #[`item.status`]="{ item }">
        <v-chip
          v-if="item.status"
          label
          class="status-chip"
          :style="{
            color: getStatusColor(item.status),
            backgroundColor: `${getStatusColor(item.status)}10`,
          }"
        >
          <div class="px-2 font-weight-bold">
            {{ getStatusName(item.status) }}
          </div>
        </v-chip>
        <v-chip
          v-else
          label
          class="status-chip"
          :style="{
            color: item.customFields?.state === 'open' ? '#008000' : '#f2284e',
            backgroundColor: item.customFields?.state === 'open' ? '#00800015' : '#f2284e15',
          }"
        >
          <div class="px-2 font-weight-bold">
            {{ item.customFields?.state }}
          </div>
        </v-chip>
      </template>

      <template #[`item.integration`]="{ item }">
        <span class="text-capitalize">{{ item.integration }}</span>
        <span
          v-if="item.integrationStatus === 'inactive' || item.integrationStatus === 'error'"
          class="ml-1 warning-icon"
        >
          <v-tooltip
            bottom
            persistent
            :open-on-hover="true"
            :open-on-click="false"
            content-class="tooltip-with-arrow"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                icon
                v-on="on"
              >
                <WarningIcon />
              </v-btn>
            </template>
            <div class="tooltip-content">
              {{ getErrorMessage(item.integrationStatus) }}
            </div>
          </v-tooltip>
        </span>
      </template>

      <template #[`item.linkedExecutions`]="{ item }">
        <DefectExecutionSkeleton
          :key="item.uid"
          :endpoint-func="() => getDefectExecutions(item.uid)"
          @execution-click="handleExecutionClick"
        />
      </template>

      <template #[`item.updated_at`]="{ item }">
        <span>{{ formatUpdateddAt(item.updatedAt) }}</span>
      </template>
    
      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-center">
          <v-tooltip 
            v-if="integrationType == 'Jira'"
            bottom
            :disabled="writeDefect" 
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  icon
                  color="primary"
                  :disabled="!writeDefect"
                  @click="$emit('edit', item)"
                >
                  <EditIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('noPermissionToDo', { action: $t('edit').toLowerCase(), type: $t('defect') }) }}
            </span>
          </v-tooltip>
          <v-menu
            v-else
            bottom
            left
            nudge-bottom="37px"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                icon
                v-bind="attrs"
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>

            <v-list>
              <v-list-item
                v-for="(menuItem, i) in getMenuItems"
                :key="i"
                style="cursor: pointer"
              >
                <v-list-item-icon
                  v-if="menuItem.title === 'Edit'"
                  class="mr-4"
                >
                  <EditIcon />
                </v-list-item-icon>
                <v-list-item-icon
                  v-else-if="menuItem.title === 'Close'"
                  class="mr-4"
                >
                  <CancelIcon />
                </v-list-item-icon>
                <v-list-item-icon
                  v-else-if="menuItem.title === 'Reopen'"
                  class="mr-4"
                >
                  <ReOpenIcon />
                </v-list-item-icon>
                <v-tooltip
                  v-if="!writeDefect"
                  bottom
                  content-class="tooltip-theme"
                >
                  <template #activator="{ on, attrs }">
                    <v-list-item-title
                      :class="{
                        'red--text': menuItem.title === 'Close',
                        'green--text': menuItem.title === 'Reopen'
                      }"
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ menuItem.title }}
                    </v-list-item-title>
                  </template>
                  <span>
                    {{ $t('noPermissionToDo', { action: menuItem.title.toLowerCase(), type: $t('defect') }) }}
                  </span>
                </v-tooltip>
                <v-list-item-title
                  v-else
                  :class="{
                    'red--text': menuItem.title === 'Close',
                    'green--text': menuItem.title === 'Reopen'
                  }"
                  @click="handleActions(menuItem.title, item)"
                >
                  {{ menuItem.title }}
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </template>

      <template #[`item.repository`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <span
              v-bind="attrs"
              v-on="on"
            >{{ item.repository.length > 10 ? item.repository.slice(0, 10) + '...' : item.repository }}</span>
          </template>
          <span>{{ item.repository }}</span>
        </v-tooltip>
      </template>

      <template #[`item.labels`]="{ item }">
        <div
          v-if="item.customFields?.tags?.length"
          class="d-flex align-center flex-wrap"
        >
          <!-- If 3 or fewer tags, just show them -->
          <template v-if="item.customFields.tags.length <= 4">
            <v-chip
              v-for="(tag, index) in item.customFields.tags"
              :key="index"
              class="custom-chip-theme fs-14px ma-1"
              :style="{
                backgroundColor: `${tag.color}10`,
                color: tag.color,
                border: `1px solid ${tag.color}`,
                fontWeight: 'bold',
              }"
            >
              {{ tag.name }}
            </v-chip>
          </template>

          <!-- If more than 3 tags, show first 3 and dropdown -->
          <v-menu
            v-else
            offset-y
            content-class="tags-menu"
          >
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                v-on="on"
              >
                <v-chip
                  v-for="(tag, index) in item.customFields.tags.slice(0, 4)"
                  :key="index"
                  class="custom-chip-theme ma-1"
                  :style="{
                    backgroundColor: `${tag.color}10`,
                    color: tag.color,
                    border: `1px solid ${tag.color}`,
                    fontWeight: 'bold',
                  }"
                >
                  {{ tag.name }}
                </v-chip>

                <v-tooltip
                  bottom
                  left
                  max-width="485px"
                  content-class="tooltip-theme"
                >
                  <template #activator="{ on, attrs }">
                    <span
                      class="custom-attribute text-truncate text-theme-table-text"
                      v-bind="attrs"
                      v-on="on"
                    >
                      +{{ item.customFields?.tags.length - 4 }}
                    </span>
                  </template>
                  <v-chip
                    v-for="(tag, index) in item.customFields?.tags.slice(4)"
                    :key="index"
                    class="custom-chip-theme ma-1"
                    :style="{
                      backgroundColor: `${tag.color}10`,
                      color: tag.color,
                      border: `1px solid ${tag.color}`,
                      fontWeight: 'bold',
                    }"
                  >
                    {{ tag.name }}
                  </v-chip>
                </v-tooltip>
              </div>
            </template>
          </v-menu>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <DefectTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util';
import EditIcon from '@/assets/svg/edit.svg';
import CancelIcon from '@/assets/svg/cancel.svg';
import WarningIcon from '@/assets/svg/warning.svg';
import ReOpenIcon from '@/assets/svg/reopen.svg';
import handleLoading from '@/mixins/loader.js';
import DefectTableSkeleton from '@/components/Skeletons/Defect/DefectTableSkeleton.vue';
import DefectExecutionSkeleton from '@/components/Skeletons/Defect/DefectExecutionSkeleton.vue';
import makeDefectsService from '@/services/api/defect';

let defectsService;

export default {
  components: {
    EditIcon,
    CancelIcon,
    WarningIcon,
    ReOpenIcon,
    DefectTableSkeleton,
    DefectExecutionSkeleton,
  },
  mixins: [handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    totalItems: {
      type: Number,
      default: 0,
    },
    page: {
      type: Number,
      default: 1,
    },
    itemsPerPage: {
      type: Number,
      default: 10,
    },
    priorities: {
      type: Array,
      required: true,
    },
    statuses: {
      type: Array,
      required: true,
    },
    integrationType: {
      type: String,
      required: true,
    },
    activeState: {
      type: String,
      required: true,
    },
    writeDefect: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      menuItems: [{ title: 'Edit' }, { title: 'Close' }],
    };
  },
  computed: {
    getMenuItems() {
      if (this.integrationType === 'Github' && this.activeState === 'closed') {
        return [{ title: 'Reopen' }];
      }
      else if (this.integrationType === 'Github' && this.activeState === 'active') {
        return [{ title: 'Edit' }, { title: 'Close' }];
      }
      return this.menuItems;
    }
  },
  created() {
    defectsService = makeDefectsService(this.$api);
  },
  methods: {
    async getDefectExecutions(defectUid) {
      const handle = this.$route.params.handle;
      const key = this.$route.params.key;
      return await defectsService.getDefectExecutions(handle, key, defectUid);
    },
    formatUpdateddAt(updatedAt) {
      return formatDate(updatedAt, 'MM/dd/yy');
    },
    getPriorityColor(priorityId) {
      const priority = this.priorities.find((p) => Number(p.id) === Number(priorityId));
      return priority?.color || '#0c111d';
    },
    getErrorMessage(status) {
      if (status === 'error') {
        return this.$t('integrations.error.integrationError');
      } else if (status === 'inactive') {
        return this.$t('integrations.error.inactiveIntegration');
      }
      return '';
    },
    getPriorityName(priorityId) {
      const priority = this.priorities.find((p) => Number(p.id) === Number(priorityId));
      return priority?.name || '';
    },

    getStatusColor(statusId) {
      const status = this.statuses.find((s) => Number(s.id) === Number(statusId));
      return status?.color || '#0c111d';
    },

    getStatusName(statusId) {
      const status = this.statuses.find((s) => Number(s.id) === Number(statusId));
      return status?.name || '';
    },

    getDefaultPriority() {
      return this.priorities.find((p) => p.isDefault)?.id;
    },

    getCompletedStatuses() {
      return this.statuses.filter((s) => s.isCompleted).map((s) => s.id);
    },

    getPriorityByName(name) {
      const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
      return this.priorities.find((p) => p.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
    },

    getStatusByName(name) {
      const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
      return this.statuses.find((s) => s.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
    },

    handleExecutionClick(execution) {
      window.open(execution.url, '_blank');
    },
    handleActions(actionType, item) {
      if (actionType === 'Edit') {
        this.$emit('edit', item);
      } else if (actionType === 'Close') {
        this.$emit('close', item);
      } else if (actionType === 'Reopen') {
        this.$emit('reopen', item);
      }
    },
  },
};
</script>

<style scoped>
.warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
}

.warning-icon :deep(svg) {
  color: #f2284e;
}

.label-container {
  min-height: 32px;
  padding: 4px;
  border-radius: 8px;
  background-color: #f9fafb;
  cursor: pointer;
}

.custom-chip-theme {
  height: 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.more-count {
  color: #666;
  font-size: 15px;
  font-weight: 500;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
:deep(.tags-menu) {
  background-color: #f9fafb !important;
  border-radius: 12px !important;
  overflow: hidden;
}

:deep(.tags-menu-list) {
  background-color: #f9fafb !important;
  border-radius: 12px !important;
}

.cursor-pointer {
  cursor: pointer;
}

:deep(.v-list-item__title) {
  width: 100%;
}
</style>