<template>
  <div>
    <div :class="tableContainerClass">
      <v-data-table
        v-if="hasInitiallyLoaded"
        v-model="selectedRows"
        :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
        :class="tableClass"
        :headers="filteredHeaders"
        :items="filteredItems"
        :item-key="itemKey"
        :item-class="rowClass"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        :server-items-length="totalItems"
        :fixed-header="true"
        :height="shouldHaveOverflow ? '70vh' : 'auto'"
        show-select
        :single-select="singleSelect"
        hide-default-footer
        disable-pagination
        @input="onRowClick"
        @click:row="handleRowClick"
      >
        <template #[`header.data-table-select`]="{ props, on }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              v-if="!singleSelect"
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              :input-value="props.value"
              :indeterminate="props.indeterminate"
              @change="on.input"
            />
          </div>
        </template>

        <template #[`item.data-table-select`]="{ isSelected, select }">
          <td @click.stop>
            <div class="d-flex justify-start align-center">
              <v-checkbox
                id="remember-me-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :input-value="isSelected"
                @change="select"
                @click.stop
              />
            </div>
          </td>
        </template>

        <template #[`header.actions`]="{ header }">
          <div class="d-none">
            {{ header.text }}
          </div>
        </template>
        <template #[`item.name`]="{ item }">
          <v-tooltip
            bottom
            left
            max-width="485px"
            :disabled="!isTruncated"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div
                :ref="'testPlanName_' + item.uid"
                class="custom-attribute text-truncate font-weight-bold cursor-pointer"
                v-bind="attrs"
                v-on="on"
                @mouseover="checkTruncate(item.uid, 'testPlanName')"
              >
                {{ item.name }}
              </div>
            </template>
            <span>{{ item.name }}</span>
          </v-tooltip>
        </template>
        <template #[`item.priority`]="{ item }">
          <span
            :style="{ color: getPriorityColor(item.customFields?.priority, priorities) }"
            class="font-weight-bold"
          >
            {{ getPriorityName(item.customFields?.priority, priorities) }}
          </span>
        </template>
        <template #[`item.status`]="{ item }">
          <span
            :style="{ color: getStatusColor(item.customFields?.status, statuses) }"
            class="font-weight-bold"
          >
            {{ getStatusName(item.customFields?.status, statuses) }}
          </span>
        </template>
        <template #[`item.milestones`]="{ item }">
          <v-menu 
            open-on-hover
            offset-y
            :close-on-hover="false"
            min-width="190px"
          >
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                :class="{
                  'custom-attribute font-weight-regular text-theme-table-text': true,
                  'pointer': true,
                  'disabled-action': isProjectArchived,
                }"
                v-on="{
                  ...on,
                  mouseenter: async (e) => {
                    on.mouseenter(e);
                    await getPlan(item.uid);
                  },
                }"
              >
                <div v-if="!processedPlanCache.includes(item.uid) && relationLoadingStates.milestoneCount">
                  <v-skeleton-loader
                    type="text"
                    height="16"
                    class="w-100"
                  />
                </div>
                <div v-else-if="item.testMilestones || item.testMilestones === 0">
                  {{ typeof item.testMilestones === 'number' ? item.testMilestones : 0 }} {{ $t('milestones') }}
                </div>
                <v-skeleton-loader
                  v-else-if="processedPlanCache.includes(item.uid)"
                  type="text"
                  height="16"
                  class="w-100"
                />
                <v-icon v-else-if="!relationLoadingStates.milestoneCount">
                  mdi-minus
                </v-icon>
              </div>
            </template>
            <v-card>
              <div class="d-flex flex-column align-start pa-4">
                <span class="fs-14px text-theme-label">
                  {{ $t('linkedTo') }} :
                </span>

                <v-skeleton-loader
                  v-if="isPlanMilestonesLoading"
                  type="text"
                  width="100%"
                  height="20px"
                  class="mt-3"
                />

                <div
                  v-else-if="isGetPlanResponseSuccess && selectedPlanMilestones.length > 0"
                  class="d-flex flex-column my-3"
                >
                  <div
                    v-for="(milestone, index) in selectedPlanMilestones" 
                    :key="index"
                    class="d-flex align-center gap-2 mb-3"
                  >
                    <UnlinkedIcon />
                    <span class="fs-14px text-theme-label">{{ milestone.name }}</span>
                  </div>
                </div>

                <template v-else>
                  <v-icon>mdi-minus</v-icon>
                </template>
              
                <v-btn
                  :ripple="false"
                  plain
                  class="btn-plain-theme text-capitalize pa-0"
                  color="primary"
                  :class="{
                    pointer: true,
                    'disabled-action': isProjectArchived,
                  }"
                  @click="onAddToMilestone(item)"
                >
                  <Milestone14px class="mr-2" />
                  <span class="fs-14px text-theme-label text-theme-primary ">{{ $t('testruns.linkToMilestone') }}</span>
                </v-btn>
              </div>
            </v-card>
          </v-menu>
        </template>


        <template #[`item.testruns`]="{ item }">
          <div v-if="!processedPlanCache.includes(item.uid) && relationLoadingStates.runCount">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <span
            v-else-if="item.runCount || item.runCount === 0"
            class="custom-attribute font-weight-regular text-theme-table-text"
          >
            {{ item.runCount || 0 }} {{ $t('testRuns') }}
          </span>
          <v-skeleton-loader
            v-else-if="processedPlanCache.includes(item.uid)"
            type="text"
            height="16"
            class="w-100"
          />
          <v-icon v-else-if="!relationLoadingStates.runCount">
            mdi-minus
          </v-icon>
        </template>
        <template #[`item.creationdate`]="{ item }">
          <span class="">{{ formatCreationDate(item.createdAt) }}</span>
        </template>

        <template #[`item.tags`]="{ item }">
          <div v-if="!processedPlanCache.includes(item.uid) && relationLoadingStates.tag">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else
            bottom
            left
            max-width="485px"
            :disabled="!Array.isArray(item?.tags) || item?.tags?.length < 3"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <div class="text-truncate">
                  <span v-if="Array.isArray(item?.tags) && item?.tags?.length > 0">
                    {{ item.tags.map((tag) => tag.name).join(', ') }}
                  </span>
                  <span v-else-if="typeof item.customFields?.tags === 'string' && item.customFields.tags.trim() !== ''">
                    {{ item.customFields.tags }}
                  </span>
                  <v-icon v-else>mdi-minus</v-icon>
                </div>
              </span>
            </template>
    
            <span>
              {{ Array.isArray(item?.tags) && item.tags.length > 0 
                ? item.tags.map((tag) => tag.name).join(', ') 
                : (typeof item.customFields?.tags === 'string' ? item.customFields.tags : '') 
              }}
            </span>
          </v-tooltip>
        </template>
        <template #[`item.progress`]="{ item }">
          <ProgressBar
            :executions="generateExecutionsProgress(item?.customFields?.frequency)"
            :percentage="item?.customFields?.progress"
            :case-count="getObjectCount(item?.customFields?.frequency)"
          />
        </template>

        <template #[`item.actions`]="{ item }">
          <td @click.stop>
            <div class="d-flex flex-row justify-center">
              <v-menu
                left
                offset-y
              >
                <template #activator="{ on }">
                  <v-btn
                    icon
                    v-on="on"
                  >
                    <v-icon>mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                <v-list
                  dense
                  class="text-left"
                >
                  <v-tooltip
                    v-if="item.archivedAt === null"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="handleDuplicatePlan(item)"
                        >
                          <div class="d-flex align-center">
                            <DuplicateIcon />
                          </div>
                          <v-list-item-title class="ml-2">
                            {{ $t('duplicate') }}
                          </v-list-item-title>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('duplicate').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="item.archivedAt === null"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onAddToMilestone(item)"
                        >
                          <MilestoneIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('testruns.linkToMilestone') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('testruns.linkToMilestone').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="item.archivedAt === null"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onEdit(item)"
                        >
                          <EditIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('edit') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>  
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="item.archivedAt === null"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onArchive(item)"
                        >
                          <ArchieveIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('archive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('archive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-else-if="item.archivedAt !== null"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onUnArchive(item)"
                        >
                          <ArchieveIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('unarchive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('unarchive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    bottom
                    :disabled="deleteEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!deleteEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onDelete(item)"
                        >
                          <DeleteIcon />
                          <v-list-item-content class="ml-2 error--text">
                            {{ $t('delete') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('plans.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                </v-list>
              </v-menu>
            </div>
          </td>
        </template>

        <template #[`item.users`]="{ item }">
          <td class="d-flex align-center">
            <v-row>
              <div
                v-for="(pic, imgIndex) in item.images"
                :key="imgIndex"
              >
                <v-avatar
                  class="ml-n2 custom_border"
                  size="30"
                >
                  <img
                    :src="pic"
                    alt=""
                  >
                </v-avatar>
              </div>
              <v-avatar
                v-if="item.showCount"
                class="font-weight-bold gray-ish--text ml-n2"
                color="#ebecf0"
                size="30"
              >
                +{{ item.count }}
              </v-avatar>
            </v-row>
            <div>
              <v-menu
                content-class="custom_ele elevation-0"
                nudge-bottom="35"
                left
              >
                <template #activator="{ on }">
                  <v-btn
                    icon
                    v-on="on"
                  >
                    <v-icon color="gray-ish">
                      mdi-dots-vertical
                    </v-icon>
                  </v-btn>
                </template>
                <v-list dense>
                  <v-list-item
                    :class="{
                      pointer: true,
                      'disabled-action': isProjectArchived,
                    }"
                    @click="onAddToMilestone(item)"
                  >
                    <MilestoneIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('addToMilestone') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    :class="{
                      pointer: true,
                      'disabled-action': isProjectArchived,
                    }"
                    @click="onEdit(item)"
                  >
                    <EditIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('edit') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    v-if="item.customFields?.status === 'Active'"
                    :class="{
                      pointer: true,
                      'disabled-action': isProjectArchived,
                    }"
                    @click="onArchive(item)"
                  >
                    <ArchieveIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('archive') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    v-else-if="item.customFields?.status === 'Archived'"
                    :class="{
                      pointer: true,
                      'disabled-action': isProjectArchived,
                    }"
                    @click="onUnArchive(item)"
                  >
                    <ArchieveIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('unarchive') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    :class="{
                      pointer: true,
                      'disabled-action': isProjectArchived,
                    }"
                    @click="onDelete(item)"
                  >
                    <DeleteIcon />
                    <v-list-item-content class="ml-2 error--text">
                      {{ $t('delete') }}
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </td>
        </template>
      </v-data-table>
      <template v-else>
        <RunTableSkeleton class="mt-6" />
      </template>
    </div>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import MilestoneIcon from '@/assets/svg/milestone.svg';
import ArchieveIcon from '@/assets/svg/archived.svg';
import DuplicateIcon from '@/assets/svg/duplicate.svg';
import { formattedDate } from '@/utils/util';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import projectStatus from '@/mixins/projectStatus';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ProgressBar from '@/components/base/ProgressBar';
import handleLoading from '@/mixins/loader.js';
import RunTableSkeleton from '@/components/Skeletons/TestRuns/RunTableSkeleton.vue';
import UnlinkedIcon from '@/assets/svg/unlinked.svg';
import Milestone14px from '@/assets/svg/milestone14px.svg';
import makePlanService from '@/services/api/plan';
import { usePlanIndex } from "@/composables/modules/testplan/index";
import { useRelations } from '@/composables/utils/relations';

let plansService;

export default {
  components: {
    DeleteIcon,
    EditIcon,
    MilestoneIcon,
    ArchieveIcon,
    DuplicateIcon,
    ProgressBar,
    RunTableSkeleton,
    UnlinkedIcon,
    Milestone14px
  },
  mixins: [projectStatus, colorPreferencesMixin, handleLoading],
  props: {
    filteredHeaders: Array,
    filteredItems: Array,
    itemKey: String,
    rowClass: Function,
    clearSelection: Boolean,
    singleSelect: {
      type: Boolean,
      default: false,
    },
    selectedPlans: {
      type: Array,
      default: () => [],
    },
    writeEntity: {
      type: Boolean,
      default: true,
    },
    deleteEntity: {
      type: Boolean,
      default: true,
    },
    totalItems: {
      type: Number,
      default: 0
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    },
    currentPage: {
      type: Number,
      default: 1
    },
    relationsLoading: {
      type: Boolean,
      default: false
    },
    relationLoadingStates: {
      type: Object,
      default: () => ({})
    }
  },
  setup(){
    const { formatDate } = useDateFormatter();
    const { hasInitiallyLoaded } = usePlanIndex();
    const { processedPlanCache } = useRelations();
    return { formatDate, hasInitiallyLoaded, processedPlanCache };
  },
  data() {
    return {
      selectedItems: [],
      statuses: [],
      priorities: [],
      selectedPlanMilestones: [],
      isGetPlanResponseSuccess: false,
      isPlanMilestonesLoading: false,
      isTruncated: false,
    };
  },
  computed: {
    selectedRows: {
      get() {
        return this.selectedPlans;
      },
      set(value) {
        this.selectedItems = value;
        this.$emit('select-item', this.selectedItems);
      },
    },
    routeParamsHandle() {
      return this.$route.params.handle;
    },
    routeParamsKey() {
      return this.$route.params.key;
    },
    shouldHaveOverflow() {
      // Enable overflow (horizontal scroll) if more than 4 columns
      return this.filteredHeaders && this.filteredHeaders.length > 4;
    },
    tableContainerClass() {
      return this.shouldHaveOverflow ? 'table-scroll-container' : 'table-no-scroll-container';
    },
    tableClass() {
      const baseClasses = 'table-fixed data-table-style custom-table mt-6';
      return this.shouldHaveOverflow ? `${baseClasses} table-min-width` : baseClasses;
    },
  },
  watch: {
    clearSelection(newVal) {
      if (newVal) {
        this.selectedRows = [];
      }
    },
  },
  created() {
    plansService = makePlanService(this.$api);
    this.priorities = this.getPriorities('testPlan');
    this.statuses = this.getStatuses('testPlan');
  },
  methods: {
    formattedDate,
    async getPlan(planUid) {
      try {
        this.isPlanMilestonesLoading = true;
        const response = await plansService.findTestPlan(this.routeParamsHandle, this.routeParamsKey, planUid);
        this.selectedPlanMilestones = response.data?.milestones || [];
        this.isGetPlanResponseSuccess = (response.status >= 200 && response.status < 300) || response.status === 304;
      } catch (error) {
        this.isGetPlanResponseSuccess = false;
      } finally {
         setTimeout(() => {
          this.isPlanMilestonesLoading = false
        }, 250)
      }
    },
    onRowClick(item) {
      this.selectedRows = item;
    },
    handleRowClick(item) {
      this.$emit('select-row', item);
    },
    handleDuplicatePlan(item) {
      this.$emit('duplicate-plan', item);
    },
    onEdit(item) {
      if (!this.isProjectArchived) {
        this.$emit('edit-item', item);
      }
    },
    onAddToMilestone(item) {
      if (!this.isProjectArchived) {
        this.$emit('add-to-milestone', {
          ...item,
          milestones: this.selectedPlanMilestones,
        });
      }
    },
    onArchive(item) {
      if (!this.isProjectArchived) {
        this.$emit('archive-item', item);
      }
    },
    onUnArchive(item) {
      if (!this.isProjectArchived) {
        this.$emit('unarchive-item', item);
      }
    },
    onDelete(item) {
      if (!this.isProjectArchived) {
        this.$emit('delete-item', item);
      }
    },
    formatCreationDate(createdAt) {
              return this.formatDate(createdAt);
    },
    getColor(priority) {
      switch (priority?.toLowerCase()) {
        case 'high':
        case 'failed':
          return 'font-weight-bold red--text text--lighten-1';
        case 'medium':
        case 'rerun':
          return 'font-weight-bold orange--text text--lighten-1';
        case 'low':
        case 'active':
          return 'font-weight-bold green--text text--lighten-1';
        case 'passed':
          return 'font-weight-bold deep-purple--text text--lighten-1';
        case 'in progress':
          return 'font-weight-bold grey--text text--lighten-1';
      }
    },
    updateTableOptions(options) {
      // Handle v-data-table sorting and pagination events and forward to parent
      this.$emit('update:options', {
        ...options,
        page: this.currentPage // Sync current page with parent
      });
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>

<style scoped>
.v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
  background-color: #ffffff;
}
.v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
  background-color: #f9fafb;
}
.pointer {
  cursor: pointer;
}
.custom_border {
  border: 2px solid #ffffff;
}
.v-data-table table {
  border-collapse: collapse;
}
.v-data-table th {
  border: none !important;
}
.v-data-table td {
  border: none !important;
  cursor: pointer;
}
.v-data-table tr.project-item:hover {
  border: 1px solid #d1e1ff !important;
}
.v-data-table .v-data-table__wrapper tbody tr {
  height: 80px;
}
.v-data-table .v-data-table__wrapper tbody tr td {
  height: 80px !important;
  padding-top: 0;
}
.v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: transparent !important;
}
</style>
