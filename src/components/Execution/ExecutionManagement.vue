<template>
  <v-row
    align-content="start"
    justify="start"
    dense
  >
    <div
      v-if="showFolders"
      :style="{ width: isTreeViewCollapsed ? '5%' : '15%' }"
      class="mt-4"
    >   
      <div
        class="d-flex flex-column white card rounded-lg ml-1 mr-2 sticky-on-scroll"
        :class="{ 'is-collapsed-menu': isTreeViewCollapsed }"
      >
        <TreeViewFolder
          :items="folders"
          :execution-folders="executionFolders"
          :selected-folder-uid="selectedFolderUid"
          :collapsed="isTreeViewCollapsed ? 0 : 1"
          :hide-create="showCreate"
          :disable-dynamic-loading="true"
          @folder-selected="selectFolder"
          @folder-delete="deleteFolder"
        />
        <div
          v-if="showCollapse"
          class="collapse-btn"
          @click="toggleMenu"
        >
          <v-icon
            color="#0C2FF3"
          >
            {{ isTreeViewCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
            }}
          </v-icon>
          <span
            v-if="!isTreeViewCollapsed"
            class="collapse-text"
          >{{ $t('collapse') }}</span>
        </div>
      </div> 
    </div>
    <v-col
      class=""
      :style="{ width: !showFolders ? '100%' : isTreeViewCollapsed ? '95%' : '85%' }"
    >
      <v-row
        align-content="start"
        justify="start"
        dense
      >
        <div
          :class="[isDetailViewCollapsed ? 'col-7' : 'col-12']"
          class="col mt-3"
        >
          <execution-list
            id="cases-list"
            :case-items="filterCases"
            :execution.sync="execution"
            :bread-crumbs="breadCrumbs"
            :allow-action="allowAction"
            :is-repository="!quickCreate"
            :is-detail-collapsed="isDetailViewCollapsed"
            :assignees="assignees"
            :write-entity="writeEntity"
            :delete-activity="deleteActivity"
            :total-items="totalItems"
            :current-page="currentPage"
            :items-per-page="itemsPerPage"
            :executions-loading="executionsLoading"
            :show-pagination="showPagination"
            @updateExecutions="updateExecutions"
            @expandDetail="openDetailView"
            @createCase="onCreateCase"
            @refresh="selectFolder"
            @selectedExecutions="handleExecutions"
            @updateExecution="updateExecution"
            @update-pagination="onUpdatePagination"
          />
          <slot name="control-area" />
        </div>
        <div
          v-if="isDetailViewCollapsed && execution"
          class="col-5 mt-3"
        >
          <ExecutionDetailView
            :execution="execution"
            :test-results.sync="testResults"
            :execution-defects="executionDefects"
            :write-activity="writeActivity"
            @updateExecution="updateExecution"
            @addResult="addResult"
            @updateResult="updateResult"
            @deleteResult="deleteResult"
            @moveItem="moveItem"
            @closeDetail="closeDetailView"
            @removeFile="updateResult"
            @reloadExecutionResults="reloadExecutionResults"
            @updateStepStatus="updateStepStatus"
            @updateAllStepStatus="updateAllStepStatus"
            @openDefectDialog="handleOpenDefectDialog"
            @refetchExecution="handleRefetchExecution"
          >
            <template #attachment>
              <fileInputWithSlider
                :files="execution.attachments"
                :project-key="selectedCase?.project?.key || execution?.project?.key"
                @uploadAttachments="uploadAttachments"
                @removeFile="removeFile"
              />
            </template>
          </ExecutionDetailView>
        </div>
      </v-row>
    </v-col>
  </v-row>
</template>
<script>
import ExecutionList from '@/components/Execution/List.vue';
import TreeViewFolder from '@/views/Tests/Case/Tree/Index.vue';
import ExecutionDetailView from '@/components/Execution/Index.vue';
import { showErrorToast } from '@/utils/toast';
import makeCasesService from '@/services/api/case';
import { createNamespacedHelpers } from 'vuex';
import fileInputWithSlider from "@/components/base/FileInputWithSlider.vue";

const { mapState } = createNamespacedHelpers('user');
export default {
  components: {
      ExecutionList,
      TreeViewFolder,
      ExecutionDetailView,
      fileInputWithSlider
  },
  props:{
    /*
      showCreate: Enables the creation of new folders.
      showCollapse: Hides the collapse folder option.
      selectOption: Returns selected cases only when set to true.
      allowAction: Activates the action button for selected cases.
    */
    showCreate:{
      type: Boolean,
      default: true,
    },
    showCollapse:{
      type: Boolean,
      default: true
    },
    selectOption:{
      type: Boolean,
      default: false,
    },
    allowAction:{
      type: Boolean,
      default: true
    },
    quickCreate:{
      type: Boolean,
      default: false
    },
    executions:{
      type: Array,
    },
    testResults:{
      type: Array,
    },
    executionDefects: {
      type: Array,
      default: () => []
    },
    execution:{
      type: Object,
    },
    assignees: {
      type: Array
    },
    folders:{
      type: Array,
      required: true,
    },
    writeActivity: {
      type: Boolean,
      default: false
    },
    deleteActivity: {
      type: Boolean,
      default: false,
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    showFolders:{
      type: Boolean,
      default: true
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    executionsLoading: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isTreeViewCollapsed: false,
      isDetailViewCollapsed: false,
      selectedItem : null,
      breadCrumbs: [],
      selectedCase: {},
      selectedCases: [],
      folderUid: null,
      skipFolderSelect: true,
    };
  },
  computed: {
  ...mapState(['currentAccount']),
  selectedFolderUid: {
    get() {
      // First priority: use explicitly set folderUid
      if (this.folderUid) return this.folderUid;
      
      // Second priority: use folder from route parameters
      if (this.folderUidParams) return this.folderUidParams;
      
      // Third priority: find folder with executions
      const rootFolder = this.folders[0];
      if (!rootFolder) return null;
      
      // Check if root folder has data in executions
      if (this.executions.some(element => element.parentUid === rootFolder.uid)) return rootFolder.uid;
      
      // Check children of root folder for data in executions
      const findFirstChildWithData = (children) => {
        for (const child of children) {
          if (this.executions.some(element => element.parentUid === child.uid)) {
            return child.uid;
          }
          if (child.children && child.children.length > 0) {
            const result = findFirstChildWithData(child.children);
            if (result) {
              return result;
            }
          }
        }
        return null;
      };

      return findFirstChildWithData(rootFolder.children) || null;
    },
    set(val){
      this.folderUid = val;
    }
  },
  cases(){
    if(this.executions.length)
        return this.executions.filter(element => element.parentUid == this.selectedFolderUid)
    return []
  },
  executionFolders(){
    return this.executions.map(item => item.parentUid)
  },
  filterCases(){
    if(this.selectOption)
      return this.selectedCases

    return this.cases
    },
    folderUidParams() {
      return this.$route.params.folderUid;
    }
  },
  watch: {
    folders: {
      handler() {
        if (!this.folderUid) this.folderUid = this.selectedFolderUid;
      },
      immediate: true,
      deep: true,
    },
    executions: {
      handler() {
        if (!this.folderUid) this.folderUid = this.selectedFolderUid;
      },
      immediate: true,
      deep: true,
    },
  },
  async mounted(){
    await new Promise(resolve => setTimeout(() => {
      this.buildBreadCrumbs(this.selectedFolderUid, this.folders, []);
      resolve()
    }, 1000));
 
    // Check if we're on an execution route and preserve it
    if (this.$route.name === 'TestRunCaseEditExecutions' && this.$route.params.executionUid) {
      // We're already on the correct execution route, just set the folder
      this.folderUid = this.folderUidParams;
      this.isDetailViewCollapsed = true;
      
      // Emit folder-select event to ensure parent component knows which folder is selected
      if (this.folderUidParams) {
        this.$emit('folder-select', this.folderUidParams);
        this.$nextTick(() => {
          this.$emit('folder-select', this.folderUidParams);
        });
      }
    } else if (!this.$route.path.includes('/folders/')) {
      // Only redirect if we're not on a folders route
      this.$router.replace({
        name: 'TestRunCaseEdit',
        params: {
          ...this.$route.params,
          folderUid: this.selectedFolderUid
        }
      });
    } else {
      this.folderUid = this.folderUidParams;
    }
    this.isDetailViewCollapsed = this.$route.path.includes('/executions/');
  },
  methods: {
    // For Testrun
    toggleMenu() {
      this.isTreeViewCollapsed = !this.isTreeViewCollapsed
    },
    setRootFolder(treeitems) {
      const rootFolder = treeitems.find((folder) => folder.parentUid === null);
      this.selectedFolderUid = rootFolder.uid;
    },
    changeExpansion(item) {
      this.selectedItem = item
      this.isDetailViewCollapsed = true;

    },
    handleOpenDefectDialog(execution) {
      this.$emit('openDefectDialog', execution);
    },
    reloadExecutionResults(resultUid){
      this.$emit('reloadExecutionResults', resultUid)
    },
    closeDetailView() {
      this.isDetailViewCollapsed = false;
      this.selectedCase = {};

      if (this.$route.path.includes('/executions/')) {
        this.$router.replace({
          name: 'TestRunCaseEdit',
          params: {
            ...this.$route.params,
            folderUid: this.selectedFolderUid
          }
        }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            console.warn('Router navigation error:', err);
          }
        });
      }

    },
    handleExecutions(selectedCases){
      this.selectedCases = selectedCases;
      this.$emit('updateSelectedExecution', selectedCases)
    },
    buildBreadCrumbs(searchUID, currentFolders, chain) {
      for (let idx = 0; idx < currentFolders.length; idx++) {
        let folder = currentFolders[idx];
        if (folder.uid === searchUID) {
          chain.push({ text: folder.name });
          this.breadCrumbs = chain;
          break;
        } else if (folder.children && folder.children.length > 0) {
          let newChain = [...chain, { text: folder.name }];
          this.buildBreadCrumbs(searchUID, folder.children, newChain);
        }
      }
    },
    updateStepStatus(status) {
      this.$emit('updateStepStatus', status);
    },
    updateAllStepStatus(steps) {
      this.$emit('updateAllStepStatus', steps);
    },
    async selectFolder(folderUID) {
      if (folderUID) {
        this.selectedFolderUid = folderUID;
      } else {
        folderUID = this.selectedFolderUid;
      }

      try {
        if(!this.skipFolderSelect){
          this.isDetailViewCollapsed = false;
        }
        this.selectedCase = {};
        
        this.$nextTick(() => {
          this.skipFolderSelect = false;
        });

        // Handle URL routing based on current route and execution state
        if (this.$route.path.includes('/executions/')) {
          // If we're currently viewing an execution, just remove it to go back to folder view
          if (!this.skipFolderSelect) {
            await this.$router.replace({
              name: 'TestRunCaseEdit',
              params: {
                ...this.$route.params,
                folderUid: folderUID
              }
            });
          }
        } else {
          // Update the folder in the current route
          await this.$router.replace({
            name: 'TestRunCaseEdit',
            params: {
              ...this.$route.params,
              folderUid: folderUID
            }
          });
        }

      } catch (error) {
        if (error.name !== 'NavigationDuplicated') {
          console.warn('Navigation error:', error);
        }
      }

      this.buildBreadCrumbs(folderUID, this.folders, []);
      this.$emit('folder-select', folderUID);
    },
    async onCreateCase(caseItem) {
      const caseService = makeCasesService(this.$api);
      try {
        const res = await caseService.createTestCase(
          this.$route.params.handle,
          this.$route.params.key,
          caseItem
        );
        if (res.status == 200) {
          let caseItem = res.data;
          this.cases.unshift(...caseItem);
        } else {
          showErrorToast(this.$swal, this.$t('error.caseAddFailed'));
        }
      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.caseAddFailed'), {}, error?.response?.data);
      }
    },
    updateResult(selectedResultUid, payload){
      this.$emit('updateResult',selectedResultUid, payload)
    },
    addResult(payload,isExecutionFailed){
      if(!this.writeActivity) return;
      this.$emit('addResult', payload,isExecutionFailed)
    },
    deleteResult(resultUid){
      this.$emit('deleteResult', resultUid)
    },
    updateExecution(payload){
      this.$emit('updateExecution', payload)
    },
    updateExecutions(payload){
      this.$emit('updateExecutions', payload)
    },
    uploadAttachments(files){
      this.$emit('uploadAttachments', files)
    },
    moveItem(direction) {
      this.$emit('moveItem', direction);
    },
    openDetailView(item) {
      if(this.selectedCase?.uid == item.uid)
        return;
        
      // Use the correct route name to navigate to execution view
      this.$router.replace({
        name: 'TestRunCaseEditExecutions',
        params: {
          ...this.$route.params,
          folderUid: this.selectedFolderUid,
          executionUid: item.uid
        }
      }).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          console.warn('Router navigation error:', err);
        }
      });
      
      this.selectedCase = item;
      this.isDetailViewCollapsed = true;
      this.$emit('getExecution', item.uid);
    },
    removeFile() {
      this.$emit('reloadExecution', this.execution?.uid);
    },
    deleteFolder(){
      this.cases = []
    },
    onUpdatePagination(options) {
      this.$emit('update-pagination', options);
    },
    handleRefetchExecution(updatedExecution) {
      this.$emit('refetchExecution', updatedExecution);
    }
  }
}
</script>
<style scoped>
.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  top: 12px;
  height: calc(100vh - 24px);
}
</style>
