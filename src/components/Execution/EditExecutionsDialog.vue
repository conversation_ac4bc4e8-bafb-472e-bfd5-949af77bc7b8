<template>
  <v-dialog
    v-model="showDialog"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
    @click:outside="clickOutside"
  >
    <v-card>
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6 mb-4">
          <h2
            v-if="!loaderState"
            class="black--text"
          >
            {{ $t('edit') }}
          </h2>
          <v-skeleton-loader
            v-else
            class="mr-3"
            height="24"
            width="75"
            type="text"
          />
          <v-btn
            v-if="!loaderState"
            icon
            @click="$emit('closeDialog')"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
          <v-skeleton-loader
            v-else
            height="36"
            width="36"
            rounded
            type="avatar"
          />
        </div>
        <div>
          <div>
            <div class="select-title mt-4 mb-1">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('priority') }}
              </v-label>
            </div>
            <v-select
              v-model="priority"
              type="text"
              dense
              filled
              :placeholder="$t('choosePriority')"
              :items="priorities"
              class="pt-2"
              append-icon="mdi-chevron-down"
              item-text="name"
              item-value="id"
              :full-width="true"
            >
              <template #item="{ item }">
                <span :style="{ color: getPriorityColor(item.id, priorities) }">{{ item.name }}</span>
              </template>
              <template #selection="{ item }">
                <span :style="{ color: getPriorityColor(item.id, priorities) }">{{ item.name }}</span>
              </template>
              <template #no-data>
                <v-icon>mdi-minus</v-icon>
              </template>
            </v-select>
          </div>
          <div>
            <div class="select-title mt-2 mb-1">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('status') }}
              </v-label>
            </div>
            <v-select
              v-model="status"
              type="text"
              dense
              filled
              :placeholder="$t('chooseStatus')"
              :items="statuses"
              class="pt-2"
              append-icon="mdi-chevron-down"
              item-text="name"
              item-value="id"
              :full-width="true"
            >
              <template #item="{ item }">
                <span :style="{ color: getStatusColor(item.id, statuses) }">{{ item.name }}</span>
              </template>
              <template #selection="{ item }">
                <span :style="{ color: getStatusColor(item.id, statuses) }">{{ item.name }}</span>
              </template>
              <template #no-data>
                <v-icon>mdi-minus</v-icon>
              </template>
            </v-select>
          </div>
          <div>
            <v-label class="text-left fs-14px text-theme-label font-weight-medium">
              {{ $t('milestone.create_milestone.dueDate') }}
            </v-label>
            <v-menu
              v-model="openDueDateMenu"
              max-width="290"
              offset-y
            >
              <template #activator="{ on }">
                <div
                  class="calendar-textbox-container"
                  v-on="on"
                >
                  <v-text-field
                    dense
                    filled
                    color="blue"
                    class="pt-2 custom_input rounded-lg calendar-textbox"
                    :value="dueAt"
                    :placeholder="$t('setDueDate')"
                    readonly
                  />
                  <calendarBlueIcon class="calendar-icon" />
                </div>
              </template>
              <v-date-picker v-model="dueAt" />
            </v-menu>
          </div>
          <div>
            <v-label class="text-left fs-14px text-theme-label font-weight-medium">
              {{ $t('assignedTo') }}
            </v-label>
            <v-autocomplete
              v-model="assignedTo"
              :loading="false"
              :items="userList"
              item-value="uid"
              item-text="firstName"
              :search-input.sync="search"
              :placeholder="$t('Select Assignee')"
              class="pt-2 custom_input rounded-lg field-theme"
              append-icon="mdi-chevron-down"
              cache-items
              dense
              filled
            />
          </div>
          <div>
            <div class="select-title mt-2 mb-1">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('addTags') }}
              </v-label>
            </div>
            <TagSelector
              v-if="!loaderState"
              v-model="tagsData.selectedTags"
              :items="tags"
              class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
            />
          </div>
          <section class="d-flex flex-column w-full">
            <div class="d-flex w-full justify-space-between align-center mt-4">
              <p
                v-if="!loaderState"
                class="font-weight-medium ma-0"
              >
                {{ $t('replaceExistingTags') }}
              </p>
              <v-skeleton-loader
                v-else
                class="mt-4"
                height="24"
                width="140"
                type="text"
              />
              <v-switch
                v-if="!loaderState"
                v-model="showReplaceTag"
                inset
                color="primary"
              />
              <v-skeleton-loader
                v-else
                class="mt-4"
                height="24"
                width="48"
                type="button"
              />
            </div>

            <section
              v-if="showReplaceTag"
              class="d-flex w-full flex-column"
            >
              <div class="d-flex flex-column">
                <div class="select-title mt-4 mb-1">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('replaceTag') }}
                  </v-label>
                </div>
                <TagSelector
                  v-model="tagsData.existingTagUids"
                  :items="tags"
                  class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
                />
              </div>
              <div class="d-flex flex-column mt-2">
                <div class="select-title mb-1">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('with') }}
                  </v-label>
                </div>
                <TagSelector
                  v-model="tagsData.newTagUids"
                  :items="tags"
                  class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
                />
              </div>
            </section>
          </section>
        </div>
      </v-card-text>
    </v-card>
    <div class="actions-container d-flex justify-space-between">
      <v-btn
        v-if="!loaderState"
        width="204.5px"
        color="#F2F4F7"
        full-width
        height="40"
        depressed
        class="text-capitalize btn-theme"
        elevation="0"
        @click="showDialog = false"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-skeleton-loader
        height="40px"
        width="204.5px"
        type="button"
      />
      <v-btn
        v-if="!loaderState"
        width="204.5px"
        class="btn-theme text-capitalize"
        height="40"
        color="primary"
        :depressed="true"
        full-width
        elevation="0"
        @click="updateExecutions"
      >
        {{ $t('save') }}
      </v-btn>
      <v-skeleton-loader
        v-else
        class="rounded-lg primary"
        height="40px "
        width="204.5px"
        type="button"
      />
    </div>
  </v-dialog>
</template>

<script>
import TagSelector from '@/components/base/TagSelector.vue';
import makeExecutionsService from '@/services/api/execution';
import makeUsersService from '@/services/api/user';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { createNamespacedHelpers } from 'vuex';
import { mapGetters, mapActions } from 'vuex';
import handleLoading from '@/mixins/loader.js';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import { debounce } from 'debounce';
const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'EditDialog',
  components: {
    TagSelector,
    calendarBlueIcon,
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    isOpen: Boolean,
    tags: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Boolean,
      default: false,
    },
    selectedExecutions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      priorities: [],
      priority: null,
      statuses: [],
      status: null,
      openDueDateMenu: false,
      dueAt: null,
      showReplaceTag: false,
      search: '',
      assignedTo: null,
      tagsData: {
        selectedTags: [],
        existingTagUids: [],
        newTagUids: [],
      },
      assignedProjects: [],
      loaderState: false,
      userList: [],
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },
    selectedExecutionsIds() {
      return this.selectedExecutions.map((execution) => execution.uid);
    },
  },
  watch: {
    search(newSearch) {
      this.testRunsLoading = true;
      this.debounceRunSearch(newSearch);
    },
  },
  async created() {
    if (!this.dynamicHeaders.workspace) {
      this.initializeHeaders({ type: 'workspace' });
    }
    this.priorities = this.getPriorities('testCase');
    this.statuses = this.getStatuses('testCase');
  },
  async mounted() {
    this.resetForm();
    await this.getUserList('');
  },
  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    clickOutside() {
      this.showDialog = false;
    },
    resetForm() {
      this.tagsData = {
        selectedTags: [],
        existingTagUids: [],
        newTagUids: [],
      };
      this.showReplaceTag = false;
    },
    async getUserList(search = '') {
      const userService = makeUsersService(this.$api);
      try {
        const response = await userService.searchUsers(`%${search}%`);
        this.userList = response.data;
      } catch (err) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchUser'), {}, err?.response?.data);
      }
    },
    async updateExecutions() {
      this.loaderState = true;
      const payload = {
        executionUids: this.selectedExecutionsIds,
        priority: this.priority || undefined,
        status: this.status || undefined,
        dueAt: this.dueAt || undefined,
        assignedTo: this.assignedTo || undefined,
        tagUids: this.tagsData.selectedTags.length ? this.tagsData.selectedTags.map((tag) => tag.uid) : undefined,
        tagReplacements: this.showReplaceTag
          ? [
              {
                existingTagUids: this.tagsData.existingTagUids.length
                  ? this.tagsData.existingTagUids.map((tag) => tag.uid)
                  : undefined,
                newTagUids: this.tagsData.newTagUids.length
                  ? this.tagsData.newTagUids.map((tag) => tag.uid)
                  : undefined,
              },
            ]
          : undefined,
      };
      const executionsService = makeExecutionsService(this.$api);
      try {
        const response = await executionsService.updateBulkExecutions(this.$route.params.handle, payload);
        showSuccessToast(this.$swal, this.$t('success.executionsUpdated', { count: `${response?.data?.length}` }));
        this.$emit('refetchExecution');
      } catch (err) {
        showErrorToast(
          this.$swal,
          this.$t('error.executionUpdateFailed', { item: 'Execution' }),
          {},
          err?.response?.data
        );
      } finally {
        this.loaderState = false;
        this.showDialog = false;
        this.resetForm();
      }
    },
    debounceRunSearch: debounce(async function (search) {
      this.getUserList(search ?? '');
    }, 500),
  },
};
</script>

<style scoped>
.select-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
}
input {
  height: 38px !important;
}
.custom-prepend {
  padding: 0px !important;
}
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 9px;
}
.box-shadow-none {
  box-shadow: none;
}

.calendar-textbox-container {
  position: relative;
}
</style>
