<template>
  <!-- header section -->
  <div
    class="card bg-white rounded-lg ml-1 pa-6 app-height-global"
  >
    <DeleteResultDialog
      :value="showDeleteConfirmDialog"
      @input="showDeleteConfirmDialog = !showDeleteConfirmDialog"
      @respond="handleDeleteResponse"
    />
    <v-dialog
      v-model="testDialog"
      max-width="594"
      persistent
    >
      <v-card>
        <v-card-title class="d-flex justify-between">
          <h4>{{ selectedResultUid ? $t('editResult') : $t('addResult') }}</h4>
          <v-btn 
            text 
            small 
            depressed 
            @click="closeTestDialog"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn> 
        </v-card-title>
        <v-card-text>
          <v-form 
            ref="form"
            class="text-left"
          >
            <v-label class="text-left fs-14px text-theme-label font-weight-medium">
              {{ $t('status') }}<strong class="danger--text">*</strong>
            </v-label>
            <v-select 
              v-model="resultState" 
              placeholder="Select status" 
              :items="activeStatuses" 
              append-icon="mdi-chevron-down"
              class="mt-0 pt-1 rounded-lg field-theme custom-prepend mb-4"
              height="38px"
              item-text="name"
              item-value="id"
              background-color="#F9F9FB"  
              :menu-props="{'offset-y': true}"
              :rules="requiredRules"
            />

            <!-- Add tag selector -->
            <v-label class="text-left fs-14px text-theme-label font-weight-medium mb-1">
              {{ $t('tags') }}
            </v-label>
            <TagSelector
              v-model="resultTags"
              :items="availableResultsTags"
              class="mb-4 w-full"
            />

            <label class="text-weight-black result-form-label">{{ $t('addComment') }} </label>
            <div class="item-area align-center">
              <tiptap-editor
                v-model="resultComment"
                class="mb-0 w-full"
                @files-added="updateFiles"
              />
              
              <v-btn
                v-if="canAssist"
                icon
                :fab="false"
                @click="openAssist"
              >
                <v-icon>mdi-shimmer</v-icon>
              </v-btn>
            </div>
            <label class="text-weight-black result-form-label">{{ $t('addAttachment') }}</label>
            <fileInput
              ref="fileInput"
              :files.sync="files"
              class="w-full mb-4"
            />
            <div class="test-result-actions d-flex justify-between">
              <v-btn
                depressed
                class="btn-theme text-capitalize rounded-lg"
                color="gray-100"
                :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
                height="40px"
                @click="closeTestDialog"
              >
                {{ $t('cancel') }}
              </v-btn>
              <v-btn
                depressed
                class="f-color-white btn-theme text-capitalize rounded-lg"
                color="primary"
                :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
                height="40px"
                @click="selectedResultUid ? updateResult() : addResult() "
              >
                {{ selectedResultUid ? $t('editResult') : $t('addResult') }} 
              </v-btn>
            </div>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  
  
    <div
      class="w-100"
    >
      <div
        v-if="showActions"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="d-flex align-center">
          <v-tooltip 
            bottom 
            :disabled="writeActivity"
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  :disabled="!writeActivity"
                  class="text-capitalize bg-white f-color-blue px-0 toggle-btn btn-plain-theme"
                  :class="{
                    'disabled-action': isProjectArchived
                  }"
                  plain
                  :ripple="false"
                  depressed
                  @click="testDialog = true"
                >
                  <div class="d-flex align-center">
                    <PlusBlueIcon />
                    <span class="ml-2 fw-semibold">{{ $t('addResult') }}</span>
                  </div>
                </v-btn>
              </div> 
            </template>
            <span>
              {{ $t('noPermissionToDo', { action: $t('addResult').toLowerCase(), type: $t('workspace') }) }}
            </span>
          </v-tooltip>
          <v-btn
            v-if="false"
            :class="{
              'text-capitalize ml-3 px-1 btn-custom bg-white f-color-blue px-0 toggle-btn mx-1': true,
              'disabled-action': isProjectArchived
            }"
            depressed
            @click="!isProjectArchived && handleEdit()"
          >
            <div class="d-flex align-center">
              <RerunIcon />
              <span class="ml-2 fw-semibold">{{ $t('rerun') }}</span>
            </div>
          </v-btn>
        </div>
        <div class="d-flex align-center">
          <div class="d-flex align-center mr-5">
            <v-btn 
              icon
              class="text-capitalize px-1 btn-custom bg-white f-color-blue px-0 toggle-btn mx-1"
              depressed 
              :disabled="isSelectedExecutionFirstIndex"
              :class="{ 'disabled-action': isSelectedExecutionFirstIndex }"
              @click="viewPreviousExecution"
            >
              <span class="cursor-pointer d-flex align-center">
                <ArrowLeftIcon />
              </span>
            </v-btn>

            <v-btn 
              depressed
              icon
              class="text-capitalize px-1 btn-custom bg-white f-color-blue px-0 toggle-btn mx-1"
              :disabled="isSelectedExecutionLastIndex"
              :class="{ 'disabled-action': isSelectedExecutionLastIndex }" 
              @click="viewNextExecution"
            >
              <span
                class="mx-2 cursor-pointer d-flex align-center"    
              >
                <ArrowRightIcon />
              </span>
            </v-btn>
          </div>
          <span
            class="close-btn"
            @click="$emit('closeDetail')"
          ><v-icon>mdi-close</v-icon></span>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <h2
        v-if="showName"
      >
        {{ execution?.name || execution?.testCase?.name }} 
      </h2>
      
      <div
        class="d-flex flex-flow-wrap align-center"
        :class="{
          'gap-2': isTagsNotEmpty
        }"
      >
        <div
          v-for="(item, index) in execution?.tags"
          :key="index"
        >
          <v-chip
            :ripple="false"
            class="chip-theme"
          >
            {{ item.name }}
          </v-chip>
        </div>
        <v-menu
          v-model="tagMenu"
          bottom
          right
          offset-y
          class="rounded-lg"
          :close-on-content-click="false"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              depressed
              plain
              :ripple="false"
              v-bind="attrs"
              class="f-color-white btn-theme text-capitalize rounded-lg btn-plain-theme px-0"
              color="primary"
              height="40px"
              style="order: 1;"
              v-on="on"
              @click="openTagMenu"
            >
              <div class="d-flex align-center">
                <PlusBlueIcon />
                <span class="ml-2 fw-semibold fs-14px">{{ $t('addTags') }}</span>
              </div>
            </v-btn>
          </template>

          <v-list class="pa-0">
            <v-list-item
              class="px-4 py-2"
            >
              <v-text-field
                v-model="tagSearch"
                dense
                hide-details
                prepend-inner-icon="mdi-magnify"
                placeholder="Search"
                class="tag-search rounded-lg"
                background-color="#F9F9FB"
                filled
                rounded
                @click.stop
              />
            </v-list-item>
            <template v-if="filteredTags.length > 0">
              <v-list-item
                v-for="tag in filteredTags"
                :key="tag.uid"
              >
                <v-list-item-action class="custom-checkbox-container">
                  <v-checkbox
                    :input-value="isTagSelected(tag.uid)"
                    hide-details
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    @change="toggleTag(tag)"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ tag.name }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
            <template v-else>
              <div class="px-4 pb-4">
                <span class="fs-14px">{{ $t('noMatchesFound') }} <b
                  class="text-theme-primary cursor-pointer"
                  @click="onCreateTagClick"
                >{{ $t('createItemTag', { tagName: tagSearch }) }}</b></span>
              </div>
            </template>
          </v-list>
        </v-menu>
      </div>
    </div>
    <h4>{{ $t('defect.overview') }}</h4>
    <div
      id="execution-detail"
      class="execution-detail my-4"
    >
      <div class="d-flex justify-space-between">
        <div class="block rounded-lg w-48 mh-56px bg-gray-theme d-flex align-center">
          <v-select
            id="execution_status"
            v-model="executionStatus"
            :menu-props="{'offset-y': true}"
            :class="{'disabled-action': isProjectArchived }"
            :items="statuses"
            label="Status"
            item-text="name"
            item-value="id"
            dense
            filled
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            flat
            item-color="grey"
            :hide-details="true"
            @change="$emit('updateExecution', {property: 'status', value: executionStatus})"
          >
            <template #selection="{ item }">
              <span
                class="fs-14px fw-semibold"
                :style="{ color: item.color }"
              >
                {{ item.name }}
              </span>
            </template>
            <template #item="{ item, on, attrs }">
              <v-list-item 
                class="mh-36px cursor-pointer"
                v-bind="attrs" 
                @click="on.click" 
              >
                <v-list-item-content>
                  <v-list-item-title
                    class="fs-14px fw-semibold"
                    :style="{ color: item.color }"
                  >
                    {{ item.name }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-select>
        </div>
        <div class="block rounded-lg w-48 mh-56px bg-gray-theme d-flex align-center">
          <v-select
            v-model="executionPriority"
            filled
            :menu-props="{'offset-y': true}"
            label="Priority"
            :items="activePriorities"
            :class="{'disabled-action': isProjectArchived }"
            background-color="#F9F9FB"
            item-text="name"
            item-value="id"
            append-icon="mdi-chevron-down"
            dense
            :hide-details="true"
            @change="$emit('updateExecution', {property: 'priority', value: executionPriority})"
          >
            <template #selection="{ item }">
              <span
                class="fs-14px fw-semibold"
                :style="{ color: item.color }"
              >
                {{ item.name }}
              </span>
            </template>
            <template #item="{ item, on, attrs }">
              <v-list-item 
                class="mh-36px cursor-pointer"
                v-bind="attrs" 
                @click="on.click" 
              >
                <v-list-item-content>
                  <v-list-item-title
                    class="fs-14px fw-semibold"
                    :style="{ color: item.color }"
                  >
                    {{ item.name }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-select>
        </div>
      </div>
      
      <v-expansion-panels
        v-model="overviewCollapsed"
        flat
      >
        <v-expansion-panel>
          <v-expansion-panel-header class="mx-0 px-0 no-expansion-header-icon">
            <h4
              class="f-color-blue toggle-btn"
              @click="overviewCollapsed = overviewCollapsed"
            >
              {{ overviewCollapsed === 0 ? $t('defect.showLess') : $t('defect.showMore') }}
            </h4>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="panel-content-theme">
            <div class="d-flex justify-space-between mb-2">
              <div class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme">
                <div class="">
                  <h5 class="align-left">
                    {{ $t('id') }}
                  </h5>
                  <div class="align-left contents fw-semibold fs-14px">
                    {{ execution.testCaseRef ? `${$route.params.key || getProjectKeyByUid(execution.projectUid)} - ${execution.testCaseRef}` : '-' }}
                  </div>
                </div>
              </div>
              <div class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme">
                <div class="">
                  <h5 class="align-left">
                    {{ $t('lastUpdate') }}
                  </h5>
                  <div class="align-left contents fw-semibold fs-14px">
                    {{ lastUpdated }}
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex justify-space-between py-1">
              <div class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme">
                <v-hover
                  v-slot="{ hover }"
                >
                  <div>
                    <h5 class="align-left">
                      {{ $t('duedate') }}
                    </h5>
                    <div
                      class="align-left contents fw-semibold fs-14px d-flex"
                    >
                      <v-menu
                        v-model="openDueDate"
                        :close-on-content-click="false"
                        max-width="290"
                      >
                        <template
                          #activator="{ on }"
                        >
                          <div class="calendar-textbox-container">
                            <v-text-field
                              dense
                              class="due-input mt-0"
                              style="min-height: 21px; height: 20px"
                              color="transparent"
                              :value="dueAt"
                              hide-details="true"
                              placeholder="MM/DD/YY"
                              readonly
                              v-on="on"
                            />
                          </div>
                        </template>
                        <v-date-picker
                          v-model="dueDate"
                          @change="setDueDate"
                        />
                      </v-menu>
                      <v-btn
                        icon
                        depressed
                        plain
                        :ripple="false"
                        height="16px"
                        class="btn-hide"
                        :class="{'btn-show': hover}"
                        @click="openDueDate = !openDueDate"
                      >
                        <PencilIcon />
                      </v-btn>
                    </div>
                  </div>
                </v-hover>
              </div>
              <div class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme">
                <div class="block rounded-lg mh-56px bg-gray-theme w-full">
                  <h5 class="align-left">
                    {{ $t('assignedTo') }}
                  </h5>
                  <AssigneeSelector
                    :value="assignedToValue"
                    :execution="execution"
                    :project-list="projectList"
                    style="height: 35px"
                    @refetchExecution="$emit('refetchExecution', $event)"
                  />
                </div>
              </div>
            </div>
            <div class="d-flex justify-space-between py-1" />
            
            <div v-if="execution.caseFields?.templateFields?.length || execution?.templateFields?.length">
              <h4 class="custom_field_heading">
                {{ $t('customFields') }}
              </h4>
              <div class="d-flex flex-wrap justify-space-between">
                <div 
                  v-for="(field, index) in (execution?.templateFields || execution?.caseFields?.templateFields)"
                  :key="field.id || index" 
                  class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme mb-2"
                >
                  <div>
                    <h5 class="align-left">
                      {{ field.name }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ field.value || $t('none') }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- References Section -->
            <div v-if="execution.references && execution.references.length > 0">
              <h4 class="custom_field_heading">
                {{ $t('references') }}
              </h4>
              <div class="d-flex flex-wrap gap-2">
                <v-tooltip
                  v-for="(reference, index) in execution.references"
                  :key="index"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                      style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                      v-on="on"
                      @click="window.open(reference.externalLink, '_blank')"
                    >
                      <div
                        class="d-flex align-center"
                        style="min-width: 0; flex: 1;"
                      >
                        <span
                          class="fs-12px text-theme-label mr-1 text-truncate"
                          style="min-width: 0; flex: 1; font-weight: 500;"
                        >{{ reference.name }}</span>
                      </div>
                      <a
                        :href="reference.externalLink"
                        target="_blank"
                        class="reference-link"
                        style="text-decoration: none; color: inherit;"
                        @click.stop
                      >
                        <v-icon
                          size="12"
                          class="text-theme-secondary"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </a>
                    </div>
                  </template>
                  <span>{{ reference.name }}</span>
                </v-tooltip>
              </div>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
      <slot name="attachment" />
    </div>
    <v-expansion-panels
      v-model="descriptionPanel"
      flat
      class="panel-expansion"
      multiple
    >
      <v-expansion-panel>
        <v-expansion-panel-header class="mx-0 px-0 panel-title">
          <div class="d-flex align-center justify-start">
            <span>{{ $t('description') }}</span> 
            <v-btn
              :ripple="false"
              plain
              class="btn-plain-theme"
              :disabled="executionItems.length == 0 || isProjectArchived"
              :class="{
                'disabled-action': executionItems.length == 0 || isProjectArchived
              }"
              @click.stop="onSetAllStepsPassed"
            >
              <GreenCheckIcon class="mr-2" />
              <span class="text-status-passed fs-14px fw-semibold text-capitalize">{{ $t('setAllStepsPassed') }}</span>
            </v-btn>
          </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <!-- description contents -->
          <v-timeline
            v-if="(execution?.steps?.length || execution?.customFields?.steps?.length || execution?.testCase?.steps?.length)"
            dense
            class="timeline-theme"
          >
            <v-timeline-item
              v-for="step in executionItems"
              :key="step.key"
              right
            >
              <ExecutionStepItem 
                :step-item="step" 
                :statuses="statuses"
                show-statuses
                @updateStatus="updateStepStatus"
              />
            </v-timeline-item>
            <v-timeline-item v-if="execution?.expectedResult">
              <div class="px-0">
                <h4 class="text-theme-secondary fs-14px fw-semibold mb-3">
                  {{ $t('sharedStepPage.expectedResult') }}
                </h4>
                <div 
                  class="text-theme-label font-weight-regular"
                  v-html="formatContent(execution?.expectedResult)"
                />
              </div>
            </v-timeline-item>
          </v-timeline>
          <template v-else>
            <div class="font-weight-medium text-theme-secondary fs-14px text-center">
              {{ $t("noDescriptionAvailableYet") }}
            </div>
          </template>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
    <v-expansion-panels
      v-model="resultHistoryPanel"
      flat
      class="panel-expansion result-expansion"
    >
      <v-expansion-panel>
        <v-expansion-panel-header class="mx-0 px-0 panel-title">
          {{ $t('resultHistory') }}
        </v-expansion-panel-header>
        <v-expansion-panel-content v-if="filterResults && filterResults.length">
          <!-- description contents -->
          <v-timeline
            dense
            class="timeline-history"
          >
            <div 
              v-for="(result, index) in filterResults"
              :key="index"
              class="result-content"
            >
              <v-timeline-item
                right
                medium
                class="timeline-item"
                color="grey lighten-2"
              >
                <template #icon>
                  <span class="grey--text text--darken-1 caption font-weight-medium">{{ index + 1 }}</span>
                </template>

                <div 
                  v-if="getResultReporter(result)"
                  class="d-flex justify-space-between mb-3"
                >
                  <div class="d-flex align-center">
                    <Avatar
                      :size="32"
                      :avatar="getResultReporter(result)?.avatar"
                      class="mr-2"
                    />
                    <div class="lh-normal">
                      <p class="mb-0 text-theme-label fs-14px font-weight-regular">
                        {{ getResultReporterDisplayName(result) }}
                      </p>
                    </div>
                  </div>
                  <div class="d-flex align-center">
                    <span class="fs-12px text-theme-secondary">{{ formatDate(result.resultCreatedAt) }}</span>
                  </div>
                </div>
          
                <div class="step-item">
                  <div class="d-flex justify-space-between mb-3">
                    <p class="mb-0 text-theme-label fs-14px fw-semibold">
                      {{ result.title }}
                    </p>
                    <div class="step-item-status">
                      <!-- TODO: Remove hard-coded values. -->
                      <h5
                        class="fw-semibold text-capitalize"
                        :style="{color: getStatusColor(result.status, statuses)}"
                      >
                        {{ getStatusName(result.status, statuses) }}
                      </h5>
                    </div>
                  </div>

                  <div class="step-item-comment">
                    <div v-html="result.comment" />
                  </div>

                  <SliderGroup
                    :files="result.attachments"
                    @remove-file="removeResultFile"
                    @download-file="downloadResultFile"
                  />

                  <div class="d-flex flex-flow-wrap align-center gap-2 mt-2">
                    <div
                      v-for="(item, index) in result.tags"
                      :key="index"
                    >
                      <v-chip
                        :ripple="false"
                        class="chip-theme"
                      >
                        {{ item.name }}
                      </v-chip>
                    </div>
                  </div>
                  
                  <!-- Defects for this result -->
                  <div
                    v-if="getDefectsForResult(result.resultUid).length > 0"
                    class="mt-3"
                  >
                    <div class="d-flex align-center mb-2">
                      <span class="fs-12px text-theme-secondary">{{ $t('defects') }}</span>
                    </div>
                    <div class="d-flex flex-wrap gap-2">
                      <div
                        v-for="defect in getDefectsForResult(result.resultUid)"
                        :key="defect.externalLink"
                        class="defect-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg"
                        style="background: #F2F4F7; border: 1px solid #E4E7EC;"
                      >
                        <div
                          class="d-flex align-center"
                          style="min-width: 0; flex: 1;"
                        >
                          <DefectIcon class="mr-1 flex-shrink-0" />
                          <span
                            class="fs-12px text-theme-label mr-1 text-truncate"
                            style="min-width: 0; flex: 1; font-weight: 500;"
                          >{{ defect.name }}</span>
                        </div>
                        <a
                          :href="defect.externalLink"
                          target="_blank"
                          class="defect-link"
                        >
                          <v-icon
                            size="14"
                            class="text-theme-secondary"
                          >mdi-arrow-top-right</v-icon>
                        </a>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="step-item-action">
                    <v-btn
                      class="text-capitalize px-1 btn-custom  f-color-blue px-2 toggle-btn mx-1"
                      depressed
                      @click="resultDialog(result.resultUid, result.status, result.comment)"
                    >
                      <v-icon size="16">
                        mdi-pencil
                      </v-icon>
                      {{ $t('edit') }}
                    </v-btn>
                    <v-btn
                      class="text-capitalize px-1 btn-custom f-color-red px-2 toggle-btn mx-1"
                      depressed
                      @click="showResultDialog(result.resultUid)"
                    >
                      <v-icon size="16">
                        mdi-delete
                      </v-icon>
                      {{ $t('delete') }}
                    </v-btn>
                  </div> -->
                </div>
              </v-timeline-item>
            </div>
          </v-timeline>
        </v-expansion-panel-content>
        <v-expansion-panel-content v-else>
          <div class="font-weight-medium text-theme-secondary fs-14px text-center">
            {{ $t('noResultFound') }} 
          </div>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
    <v-expansion-panels
      flat
      class="panel-expansion"
    >
      <v-expansion-panel>
        <v-expansion-panel-header class="mx-0 px-0 panel-title">
          <div class="d-flex justify-space-between align-center w-full">
            <div>{{ $t('defects') }}</div>
            <div
              class="d-flex justify-end"
              @click.stop="openDefectDialog"
            >
              <PlusBlueIcon />
              <span class="ml-2 fw-semibold fs-14px f-color-blue">{{ $t('addDefects') }}</span>
            </div>
          </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content v-if="executionDefects && executionDefects.length">
          <v-list class="pa-0">
            <v-list-item 
              v-for="defect in executionDefects" 
              :key="defect.externalLink" 
              class="mb-2 rounded-lg px-0"
              style="background: transparent;"
            >
              <v-list-item-avatar
                class="mr-3"
                style="position: relative; top: 5px;"
              >
                <v-avatar
                  class="defect-avatar"
                  size="32"
                >
                  <DefectIcon />
                </v-avatar>
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>
                  <v-tooltip
                    bottom
                    left
                    max-width="485px"
                    content-class="tooltip-theme"
                    :disabled="defect.name.length < 30"
                  >
                    <template #activator="{ on, attrs }">
                      <div 
                        class="custom-attribute text-truncate fs-14px fw-semibold cursor-pointer"
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ defect.name.length > 30 ? defect.name.slice(0, 30) + '...' : defect.name }}
                      </div>
                    </template>
                    <span class="cursor-pointer">{{ defect.name }}</span>
                  </v-tooltip>
                </v-list-item-title>
              </v-list-item-content>

              <v-list-item-action>
                <a
                  :href="defect.externalLink"
                  target="_blank"
                  class="issue-link"
                >
                  <v-icon
                    class="hover-icon"
                    size="20"
                  >mdi-arrow-top-right</v-icon>
                </a>
              </v-list-item-action>
            </v-list-item>
          </v-list>
        </v-expansion-panel-content>
        <v-expansion-panel-content v-else>
          <div class="font-weight-medium text-theme-secondary fs-14px text-center">
            {{ $t('noDefectFound') }} 
          </div>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
    <CreateUpdateTagDialog 
      v-model="showCreateTagDialog"
      :initial-value="initialValue"
      @create-new-tag="createTag"
      @close-dialog="onCloseTagDialog"
    />
    <RunDiscardDialog
      v-if="!isWorkspace"
      v-model="showConfirmDialog"
      :title="$t('setAllStepsAsPassed')"
      :content="$t('allStepsAsPassed')"
      :btn_label="$t('confirm')"
      :color="'primary'"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmClick"
    />
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { formatDate } from '@/utils/util';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import PlusBlueIcon from '@/assets/svg/plus-blue.svg';
import GreenCheckIcon from '@/assets/svg/green-check-icon.svg';
import RerunIcon from '@/assets/svg/rerun.svg';
import ArrowRightIcon from '@/assets/svg/arrow-right.svg';
import ArrowLeftIcon from '@/assets/svg/arrow-left.svg';
import DeleteResultDialog from '@/components/Execution/DeleteResultDialog.vue'
import AssigneeSelector from '@/components/Execution/AssigneeSelector.vue';
import ExecutionStepItem from '@/views/Tests/Case/Components/StepItem.vue';
import TiptapEditor from '@/components/base/TipTapEditor.vue';
import fileInput from "@/components/base/FileInput.vue";
import SliderGroup from "@/components/base/SliderGroup.vue";
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import projectStatus from '@/mixins/projectStatus';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { uuid } from 'vue-uuid';
import makeResultsService from '@/services/api/result'
import dayjs from 'dayjs';
import makeTagService from '@/services/api/tag';
import TagSelector from '@/components/base/TagSelector.vue';
import PencilIcon from '@/assets/svg/pencil.svg';
import jiraIcon from '@/assets/png/jira.png';
import githubIcon from '@/assets/png/github.png';
import testrailIcon from '@/assets/png/testrail.png';
import DefectIcon from '@/assets/svg/left-menu/defect.svg';
import CreateUpdateTagDialog from "@/components/Admin/Tag/CreateUpdateTagDialog.vue";
import makeAttachmentService from '@/services/api/attachment'
import axios from 'axios'
import { getExtension, generateDateUid } from "@/utils/util";
import { formatContent } from '@/utils/markdown';

let tagService;
let attachmentService;

export default {
  name: 'DetailView',
  components: {
    PlusBlueIcon,
    RerunIcon,
    ArrowRightIcon,
    ArrowLeftIcon,
    DeleteResultDialog,
    ExecutionStepItem,
    TiptapEditor,
    fileInput,
    SliderGroup,
    TagSelector,
    PencilIcon,
    CreateUpdateTagDialog,
    GreenCheckIcon,
    AssigneeSelector,
    DefectIcon
  },
  mixins: [projectStatus, colorPreferencesMixin],
  props: {
    isSelectedExecutionFirstIndex: Boolean,
    isSelectedExecutionLastIndex: Boolean,
    projectList: Array,
    userList: Array,
    execution: Object,
    testResults: Array,
    executionDefects: {
      type: Array,
      default: () => []
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showName: {
      type: Boolean,
      default: true
    },
    canAssist: {
      type: Boolean,
      default: () => true,
    },
    writeActivity: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      customToolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        ['link']
      ],
       integrationImages: {
        'jira.png': jiraIcon,
        'github.png': githubIcon,
        'testrail.png': testrailIcon,

      },
      showConfirmDialog: false,
      openDueDate: false,
      dueDate: null,
      requiredRules: [(value) => !!value || this.$t('error.requiredField')],
      files: [],
      tagMenu: false,
      resultState: null,
      overviewCollapsed: false,
      descriptionPanel:[],
      resultHistoryPanel: [],
      runsPanel: null,
      runs: [],
      statuses: [],
      priorities: [],
      collapseStep: [],
      collapseResult: [],
      testDialog: false,
      selectedResultUid: null,
      selectedResultAttachments: [],
      showDeleteConfirmDialog: false,
      handleDeletedResultUid: null,
      field: {id: uuid.v4(), dataType: "text", name: this.$t('resultComment')},
      availableResultsTags: [],
      availableExecutionTags: [],
      tagSearch: '',
      resultTags: [],
      showCreateTagDialog: false,
      initialValue: {
        name: '',
      },
      originalTags: [], // Store original tags when menu opens
      hasChanges: false, // Track if there are actual changes
      pendingIsAddTags: false,
    }
  },
  computed: {
    ...mapGetters(['isTextAssistOpen', 'textAssistField']),

    resultComment: {
      get() {
        return this.textAssistField.value || this.field.value;
      },
      set(value) {
        this.field.value = value;
        this.updateTextAssistValue(value);
      }
    },
    displayAssignee: {
      get() {
      if (this.execution?.assignedTo) {
        return `${  this.execution?.assignedTo?.firstName || '' } ${ this.execution?.assignedTo?.lastName || '' }`;
      } else {
        return this.$t('noAssigned')
      }
      },
      set(value) {
      this.$emit('updateExecution', {property: 'assignedTo', value: value})
      }
    },
    executionItems() {
      const candidates = [
        this.execution?.steps,
        this.execution?.customFields?.steps,
        this.execution?.testCase?.steps,
      ];
      const items = candidates.find(arr => Array.isArray(arr) && arr.length > 0) || [];
      return [...items].sort((a, b) => a.position - b.position);
    },
    isWorkspace(){
      return this.$route.name == 'Workspace'
    },
    lastUpdated() {
      return formatDate(this.execution.updatedAt, 'dd-MM-yyyy');
    },
    isExecutionFailed(){
      return this.getStatusName(this.resultState, this.activeStatuses).toLowerCase() == 'failed';
    }, 
    dueAt(){
      return this.execution?.dueAt?.split('T')[0]
    },
    executionStatus:{
      get(){
        return this.execution?.status
      },
      set(value){
        // TODO .. Rely on the completed statuses of test cases rather than hard-coding
        if(this.getEntityCompletedStatuses('testCase').includes(value))
          this.execution.toWait = 1
        this.execution.status = value
      }
    },
    isTagsNotEmpty(){
      return this.execution?.tags?.length > 0
    },
    executionPriority:{
      get(){
        return this.execution.priority
      },
      set(value){
        this.execution.priority = value
      }
    },
    filterResults() {
      return this.testResults;
    },
    activeStatuses(){
      return this.statuses.filter(element => !element.archived)
    },
    activePriorities(){
      return this.priorities.filter(element => !element.archived)
    },
    filteredTags() {
      if (!this.tagSearch) return this.availableExecutionTags;
      const search = this.tagSearch.toLowerCase();
      return this.availableExecutionTags.filter(tag => 
        tag.name.toLowerCase().includes(search)
      );
    },
    isCompletedId(){
      const passedText = this.$t("statuses.passed").toLowerCase();
      const step = this.statuses.find(i =>
        i.isCompleted ||
        i.name.toLowerCase().includes(passedText)
      );
      return step?.id;
    },
    assignedToValue() {
      return this.execution?.assignedTo?.uid || this.execution?.assignedTo;
    },
  },

  watch: {
    execution: {
      handler(newExecution) {
        if (newExecution?.dueAt) {
          this.dueDate = newExecution.dueAt.split('T')[0];
        } else {
          this.dueDate = null;
        }
        if (newExecution) {
          this.resultState = newExecution.status;
        }
      },
      immediate: true
    },
    tagMenu(newValue, oldValue) {
      if (newValue) {
        // When menu opens, store the original tags
        this.originalTags = [...(this.execution?.tags || [])];
        this.hasChanges = false;
      } else if (oldValue && this.hasChanges) {
        // When menu closes and there are changes, emit the update
        this.$emit('updateExecution', { 
          property: 'tags', 
          value: this.execution?.tags, 
          isAddTags: this.pendingIsAddTags 
        });
      }
    }
  },

  created() {
    this.priorities = this.getPriorities("testCase");
    this.statuses = this.getStatuses("testCase");
    this.fetchResultsTags();
    this.fetchExecutionTags();
    tagService = makeTagService(this.$api);
    attachmentService = makeAttachmentService(this.$api);
  },
 
  methods: {
  ...mapMutations(['toggleTextAssist', 'setTextAssistData']),
  formatContent,
  stripHtml(html) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  },
  getProjectKeyByUid(projectUid) {
    const project = this.projectList.find(p => p.uid === projectUid);
    return project ? project.key : null;
  },
  setDueDate() {
    this.openDueDate = null;
    if(this.dueDate) {
      const payload = {
        property: 'dueAt',
        value: dayjs(this.dueDate).format('YYYY-MM-DD')
      }
      this.$emit('updateExecution', payload);

      this.execution.dueAt = this.dueDate;
    }
  },
  async removeResultFile(file) {
    await this.deleteResultAttachment(file.uid)
  },
  async downloadResultFile(file) {
      try {
        const params = {
          download: true
        }
        const response = await attachmentService.getAttachmentUrl(this.$route.params.handle, "results", file.uid, params);
        const blob = await axios.get(response.data, { responseType: 'arraybuffer' }).then(res => res.data);
        const uid = generateDateUid();
        const filename = file.name || `${uid}.${getExtension(file.type)}`;
        const url = URL.createObjectURL(new Blob([blob]));
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (err) {
        showErrorToast(this.$swal, this.$t('toast.downloadError', { item: 'Attachment' }), {}, err?.response?.data)
      }
    },  
  openDefectDialog() {
    this.$emit('openDefectDialog', this.execution);
  },
  async openAssist() {
    if (this.isTextAssistOpen) return;

    if (this.resultComment?.length > 16) {
      this.toggleTextAssist();
      this.setTextAssistData({ field: {...this.field, value: this.stripHtml(this.resultComment)}, callback: this.closeAssist });
    } else {
      showErrorToast(this.$swal, this.$t('error.fieldTooShort'));
    }
  },
  updateTextAssistValue(value) {
    const updatedField = { ...this.textAssistField, value };
    this.setTextAssistData({ field: updatedField, callback: this.textAssistField.callback });
  },
  updateFiles(files) {
    this.resultComment = files;
  },
  inited (viewer) {
      this.$viewer = viewer
    },
    show () {
      this.$viewer.show()
    },
    handleEdit() {
      this.$router.push({
        name: 'EditTestCases',
        params: { uid: this.caseItem.test_case_ref },
      });
    },
    deleteAttachment(index){
      this.files.splice(index,1);
    },
    showResultDialog(uid){
      this.showDeleteConfirmDialog = true;
      this.handleDeletedResultUid = uid;
    },
    handleDeleteResponse(value){
      if(value && this.handleDeletedResultUid)
        this.deleteResult(this.handleDeletedResultUid)

      this.showDeleteConfirmDialog = false;
      this.handleDeletedResultUid = null;
    },
    async deleteResultAttachment(uid){
      const resultService = makeResultsService(this.$api);
      const params = { 
        projectKey: this.$route.params.key || this.execution?.project?.key,
        handle: this.$route.params.handle
      };
      const resultUid = this.execution.uid;
      await resultService.deleteAttachments({id: uid, params}).then(() => {
        this.$emit('removeFile');
        this.$emit('reloadExecutionResults', resultUid)
        showSuccessToast(this.$swal, this.$t('success.deleteAttachment'))
      }).catch((err) => {
        showErrorToast(this.$swal, this.$t('error.failedToDeleteAttachment'), {}, err?.response?.data)
      })

    },
    addResult(){
      const isValidForm = this.$refs.form.validate();
      if(isValidForm) {
        const payload = {
          files: this.files,
          status: this.resultState,
          comment: this.resultComment,
          tagUids: this.resultTags.map(tag => tag.uid)
        } 
        this.$emit('addResult', payload, this.isExecutionFailed)
        this.closeTestDialog();
      }
    },
    updateResult(){
      const isValidForm = this.$refs.form.validate();
      if(isValidForm) {
        const payload = {
          files: this.files,
          status: this.resultState,
          comment: this.resultComment,
          tags: this.resultTags
        }

        this.$emit('updateResult', this.selectedResultUid, payload)
        this.closeTestDialog();
      }
    },
    resultDialog(resultUid, status, comment){
      this.resultComment = comment;
      this.resultState = status[0].toUpperCase() + status.slice(1)
      this.selectedResultUid = resultUid;
      this.testDialog = true;
      const selectedResult = this.testResults.filter((element) => {
        if(element.resultUid == resultUid)
          return element.attachments
      });

      if(selectedResult.length)
        this.selectedResultAttachments = selectedResult[0].attachments
      
    },
    deleteResult(resultUid){
      this.$emit('deleteResult', resultUid)
    },
    openTagMenu(){
      this.tagMenu = !this.tagMenu;
      this.tagSearch = '';
    },
    closeTestDialog(){
      this.$refs.fileInput.resetFileInput();
      this.files = [];
      this.selectedResultAttachments = [];
      this.resultComment = "";
      this.resultState = null;
      this.resultTags = [];
      this.selectedResultUid = null;
      this.testDialog = false;
    },
    viewPreviousExecution(){
      if(!this.isSelectedExecutionFirstIndex){
        this.$emit('moveItem', 'previous')
      }
    },
    viewNextExecution(){
      if(!this.isSelectedExecutionLastIndex){
        this.$emit('moveItem', 'next')
      }
    },
    async fetchTagsByType(tagType) {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, tagType);
        return response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: `${tagType} tags` }, error?.response?.data);
        return [];
      }
    },
    async fetchResultsTags() {
      this.availableResultsTags = await this.fetchTagsByType('results');
    },
    async fetchExecutionTags() {
      this.availableExecutionTags = await this.fetchTagsByType('executions');
    },
    isTagSelected(tagUid) {
      return this.execution?.tags?.some(tag => tag.uid === tagUid);
    },
    toggleTag(tag) {   
      const index = this.execution?.tags?.findIndex(t => t.uid === tag.uid);
      let isAddTags = false;
      if(index === undefined){
        isAddTags = true;
        this.execution.tags = [tag];
      } else if (index === -1) {
        isAddTags = true;
        this.execution.tags.push(tag);
      } else {
        this.execution?.tags.splice(index, 1);
      }
      
      // Store the isAddTags for when we emit
      this.pendingIsAddTags = isAddTags;
      
      // Check if there are actual changes compared to original
      const originalUids = this.originalTags.map(t => t.uid).sort();
      const currentUids = (this.execution?.tags || []).map(t => t.uid).sort();
      this.hasChanges = JSON.stringify(originalUids) !== JSON.stringify(currentUids);
    },
    onCreateTagClick() {
      this.showCreateTagDialog = true;
      this.tagMenu = false;
      this.initialValue = {
        name: this.tagSearch,
      }
    },
    onCloseTagDialog() {
      this.showCreateTagDialog = false;
      this.tagSearch = '';
    },
    onSetAllStepsPassed() {
      this.showConfirmDialog = true;
      this.$emit('setAllStepsPassed', {
        steps: this.executionItems,
        completedId: this.isCompletedId,
        status: true
      });
    },
    handleCloseClick() {
      this.showConfirmDialog = false;
    },
    handleConfirmClick() {
      this.showConfirmDialog = false;
      this.$emit("updateAllStepStatus", {
        steps: this.executionItems,
        completedId: this.isCompletedId
      });
    },
    updateStepStatus(step) {
      this.$emit('updateStepStatus', step);
    },
    async createTag(tag) {
      
      this.showCreateTagDialog = false;
    
      try {
        const payload = {
          name: tag.name,
          description: tag.description,
          entityTypes: tag.entityTypes,
        }

        const response = await tagService.createTag(this.$route.params.handle, payload);
        if(response.status === 200){
          this.fetchResultsTags();
          this.fetchExecutionTags();
          showSuccessToast(this.$swal, "createSuccess", { item: "Tag" });
        }
        
      } catch (err) {
        showErrorToast(this.$swal, "createError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.showCreateTagDialog = false;
        this.tagSearch = '';
      }

    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        showSuccessToast(this.$swal, this.$t('toast.copySuccess'));
      }).catch(() => {
        showErrorToast(this.$swal, this.$t('toast.copyError'));
      });
    },
    getDefectId(externalLink) {
      if (!externalLink) return '';
      
      // Extract the last part of the URL as ID
      const urlParts = externalLink.split('/');
      return urlParts[urlParts.length - 1] || '';
    },
    getDefectIcon() {
      return DefectIcon;
    },
    getDefectsForResult(resultUid) {
      return this.executionDefects.filter(defect => defect.resultUid === resultUid) || [];
    },
    getResultReporter(result) {
      // Check if reporter exists in the main field, otherwise check customFields
      if (result.reporter) {
        return result.reporter;
      } else if (result.customFields?.reporter) {
        return result.customFields.reporter;
      }
      return null;
    },
    getResultReporterDisplayName(result) {
      const reporter = this.getResultReporter(result);
      if (!reporter) return '';
      
      // If it's a regular reporter object with firstName/lastName
      if (reporter.firstName || reporter.lastName) {
        return `${reporter.firstName || ''} ${reporter.lastName || ''}`.trim();
      }
      
      // If it's from customFields with name field
      if (reporter.name) {
        return reporter.name;
      }
      
      return '';
    },
  },
}
</script>
<style scoped>
.btn-hide{
  display: none;
}
.btn-show{
  display: block !important;
}
.result-form-label{
  font-size: 14px !important;
  color: #000 !important;
  font-weight: 500 !important;
  margin-bottom: 6px !important;
  display: block !important;
}
.tags{
  gap: 8px;
}
#input-file-browser{
  display: block;
  height: 120px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #D0D5DD;
  background-color: #F9FAFB;
  color: #0C2FF3;
  font-weight: bold;
  cursor: pointer;
}
#input-file-browser:hover{
  background-color: #eeeeee;
  transition: all 0.5s;
}
</style>
<style scoped>
#execution-detail .calendar-textbox-container .v-input .v-input__control, 
#execution-detail .calendar-textbox-container .v-input .v-input__slot
{
  min-height: 21px !important;
  height: 21px !important;
}
#execution-detail .calendar-textbox-container .v-input .v-input__slot{
  padding: 0px !important;
}
</style>

<style scoped>
/* Customize Editor */
.quillWrapper{
  background-color: #f0f0f0;
  border-radius: 16px;
}
.quillWrapper .ql-toolbar{
  border: 0px !important;
}
.quillWrapper #quill-container{
  border: 0px !important;
  height: 120px;
}
.quillWrapper .ql-toolbar .ql-formats svg{
  width: 16px !important;
}
.quillWrapper .ql-toolbar .ql-formats button{
  width: 20px;
}
.ql-editor{
  min-height: auto !important;
}
/* Result buttons */
.test-result-actions{
  gap: 16px;
}
.test-result-actions button{
  width: calc(50% - 16px) !important;
  border-radius: 6px !important;
}
.images-preview{
  margin: 15px 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.image-preview img{
  width: 60px;
  object-fit: cover;
  height: 60px;
  border: 1px dashed blue;
}
.image-preview button{
  position: absolute;
}
.execution-detail .v-text-field--filled.v-input--dense .v-label--active, .v-text-field--full-width.v-input--dense .v-label--active{
  color: #667085 !important
}

h2, h3, h4{
  text-align: left;
}
h5{
  color:#667085;
  font-family: Inter;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: left;
}
.collapse-btn{
  color:#0C2FF3;
}
p{
  text-align: left;
}
.flex{
  display: flex;
}
.justify-between{
  justify-content: space-between;
}
.justify-start{
  justify-content: flex-start;
}
.align-center {
  align-items: center;
}
.gap-2{
  gap: 8px;
}
.bg-white{
  background-color: white !important;
}
.close-btn:hover{
  cursor: pointer;
}
.f-color-blue{
  color: #0C2FF3 !important;
}
.w-48 {
  width: 48.5%;
}
.align-left{
  text-align: left;
}
.align-left .contents{
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  color: #0C111D;
}
.toggle-btn{
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  cursor: pointer;
}
.panel-title{
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
}
/* Customize Expansion */
/* .panel-expansion{
  overflow: hidden;
} */
.v-timeline-item__divider{
  min-width: auto !important;
  margin: 0px -8px;
}
.v-timeline--dense:not(.v-timeline--reverse):before, .v-application--is-rtl .v-timeline--reverse.v-timeline--dense:before{
  left: 11px !important;
}
.v-timeline--dense .v-timeline-item{
  justify-content: flex-end;
  gap: 8px;
}
.timeline-item h4{
  color: #0C111D;
  font-size: 14px;
}
.v-timeline--dense .v-timeline-item__body{
  max-width: max-content;
  min-width: calc(100% - 24px) !important;
  background-color: #F9FAFB;
  border-radius: 8px;
  padding: 16px;
}
.v-timeline--dense .v-timeline-item__body button{
  padding: 0px !important;
}
.step-item{
  gap: 8px
}
.step-item .step-item-status h5{
  font-size: 12px;
  margin-top: 10px;
}
.step-item > div{
  width: calc(50% - 4px);
  flex-shrink: 0;
}
.step-item > div h5{
  color: #667085;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}
.step-item > div p{
  font-size: 14px;
  color: #0C111D;
}
.result-expansion .step-item > div{
  width: 100%;
}
.btn-custom:hover::before{
  display: none;
}
/* Test Results */
.result-expansion .v-timeline-item__dot{
  box-shadow: none !important;
}
.result-expansion .expansion-header h4.result-name{
  font-weight: 500 !important;
  color: #0C111D;
  font-size: 15px;
  font-family: 'Inter', sans-serif;
}
.result-expansion .expansion-header h4.result-date{
  color: #667085;
  font-weight: 400;
  font-size: 13px;
}
.result-expansion .step-item-attachments .viewer img{
  width: 150px;
  height: 100px;
  margin: 8px;
  border: 3px solid #888;
  cursor: pointer;
}
.result-expansion .step-item-attachments .images-attachment .item-attachment{
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.result-expansion .step-item-attachments .images-attachment .item-attachment a{
  border: 1px solid #D0D5DD;
  overflow: hidden;
  width: 120px;
  height: 80px;
  border-radius: 4px;
}
.result-expansion .step-item-attachments .images-attachment .item-attachment a img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.item-attachment span{
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;

}
.result-expansion .step-item-action{
  gap: 8px;
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}

.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.item-area {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 14px;
}

.chip-theme {
  background-color: #F2F4F7 !important;
  color: #344054 !important;
  height: 24px !important;
  border-radius: 8px !important;
}

.custom-checkbox-container {
  margin: 0 !important;
}

.field-theme {
  font-family: Inter;
  font-size: 14px;
}

.tag-search :deep(.v-input__slot) {
  min-height: 36px !important;
  box-shadow: none !important;
  border-radius: 8px !important;
}

.v-list-item:hover {
  background-color: transparent !important;
}

.tag-search :deep(.v-input__prepend-inner) {
  margin-top: 6px !important;
}

.tag-search :deep(input) {
  font-size: 14px;
}

.defect-chip {
  transition: all 0.2s ease;
}

.defect-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.defect-link {
  text-decoration: none;
  color: inherit;
}

.defect-link:hover {
  opacity: 0.8;
}
.text-wrap {
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%; /* Ensures the text stays within the column */
}

.issue-link {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color 0.3s ease;
}

.hover-icon {
  transition: color 0.3s ease, transform 0.2s ease;
}

.issue-link:hover .hover-icon {
  color: #0C2FF3;; 
  transform: scale(1.2);
}
.bg-gray-theme{
  background-color: #F9FAFB;
}
.number-color{
  color: #667085;
}

.w-full {
  width: 100%;
}


.panel-expansion .v-expansion-panel-content {
  border-top: none;
}

.defect-avatar {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  

}


</style>
