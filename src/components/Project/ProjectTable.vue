<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table data-table-style mt-6"
      :headers="filteredHeaders"
      :items="filteredItems"
      :item-key="itemKey"
      :item-class="rowClass"
      hide-default-footer
      disable-pagination
      @click:row="onRowClick"
    >
      <template #[`header.key`]="{ header }">
        <span class="header_text text-capitalize">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.name`]="{ header }">
        <span class="header_text ml-12">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.test`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.cases`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.lastchanges`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.defects`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.projadmin`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.users`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`item.test`]="{ item }">
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectTestRuns(handle, item.key)"
          :count-for="$t('testRuns')"
        />
      </template>
      <template #[`item.cases`]="{ item }">
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectTestCases(handle, item.key)"
          :count-for="$t('testCases')"
        />
      </template>
      <template #[`item.lastchanges`]="{ item }">
        <span class="text-theme-table-text fs-14px">
          {{ formattedDate(item.updatedAt) }}
        </span>
      </template>
      <template #[`item.defects`]="{ item }">
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectDefects(handle, item.key)"
          :count-for="$t('defects')"
        />
      </template>
      <template #[`item.createdAt`]="{ item }">
        <span class="text-theme-table-text fs-14px">
          {{ formattedDate(item.createdAt) }}
        </span>
      </template>
      <template #[`item.projadmin`]="{ item }">
        <span class="text-theme-table-text fs-14px">
          {{ item.projadmin }}
        </span>
      </template>
      <template #[`item.name`]="{ item }">
        <div class="d-flex align-center">
          <v-btn
            icon
            class="mr-1"
            @click="toggleStar(item)"
          >
            <StarFillIcon v-if="item.customFields?.star" />
            <StarIcon v-else />
          </v-btn>
          <Avatar 
            :size="40"
            :avatar-src="item.avatarUrl || require('@/assets/png/project.png')"
            class="mr-2 cursor-pointer"
          />
          <div class="text-start cursor-pointer">
            <div class="fw-semibold fs-14px">
              {{ item.name }}
            </div>
            <div class="text-theme-secondary fs-12px text-truncate mw-100px">
              {{ item.customFields?.description }}
            </div>
          </div>
        </div>
      </template>
      <template #[`item.users`]="{ item }">
        <td class="d-flex align-center justify-space-between w-100">
          <AvatarGroupSkeleton
            :endpoint-func="() => getProjectMembers(handle, item.key)"
            :size="40"
          />
          <div>
            <v-menu
              content-class="custom_ele elevation-0"
              nudge-bottom="35"
              left
            >
              <template #activator="{ on }">
                <v-btn
                  icon
                  v-on="on"
                >
                  <v-icon color="gray-ish">
                    mdi-dots-vertical
                  </v-icon>
                </v-btn>
              </template>
              <v-list dense>
                <v-tooltip
                  :disabled="writeProject"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div 
                      v-bind="attrs" 
                      v-on="on"
                    >
                      <v-list-item
                        v-if="!item.archivedAt"
                        :disabled="!writeProject"
                        class="pointer"
                        @click="onEdit(item)"
                      >
                        <EditIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('edit') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('orgLevelActionDisabled') }}
                  </span>
                </v-tooltip>
                <v-tooltip 
                  v-if="!item.archivedAt"
                  :disabled="writeProject"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div 
                      v-bind="attrs" 
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!writeProject"
                        class="pointer"
                        @click="onArchive(item)"
                      >
                        <ArchieveIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('archive') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('orgLevelActionDisabled') }}
                  </span>
                </v-tooltip>
                <v-tooltip
                  v-else-if="item.archivedAt"
                  :disabled="writeProject"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div 
                      v-bind="attrs" 
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!writeProject"
                        class="pointer"
                        @click="onUnArchive(item)"
                      >
                        <ArchieveIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('unarchive') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('orgLevelActionDisabled') }}
                  </span>
                </v-tooltip>
                <v-tooltip 
                  :disabled="deleteProject"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div 
                      v-bind="attrs" 
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!deleteProject"
                        class="pointer"
                        @click="onDelete(item)"
                      >
                        <DeleteIcon />
                        <v-list-item-content class="ml-2 error--text">
                          {{ $t('delete') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('orgLevelActionDisabled') }}
                  </span>
                </v-tooltip>
              </v-list>
            </v-menu>
          </div>
        </td>
      </template>
    </v-data-table>
    <template v-else>
      <ProjectTableSkeleton />
    </template>

    <Pagination
      v-if="!skeletonLoaderState && totalItems > 0"
      :page="currentPage"
      :total-pages="totalPages"
      :items-per-page="itemsPerPage"
      :total-items="totalItems"
      @update:pagination="onUpdatePagination"
    />
  </div>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import ArchieveIcon from '@/assets/svg/archived.svg';
import StarFillIcon from '@/assets/svg/star-fill.svg';
import StarIcon from '@/assets/svg/star.svg';
import { formattedDate } from '@/utils/util';
import handleLoading from '@/mixins/loader.js';
import ProjectTableSkeleton from '@/components/Skeletons/Projects/ProjectTableSkeleton.vue';
import CountsValueSkeleton from '@/components/Skeletons/CountsValue/CountsValueSkeleton.vue';
import AvatarGroupSkeleton from '@/components/Skeletons/AvatarGroup/AvatarGroupSkeleton.vue';
import makeRunService from "@/services/api/run";
import makeCasesService from '@/services/api/case';
import makeDefectService from '@/services/api/defect';
import Avatar from "@/components/base/Avatar.vue"
import makeProjectsService from '@/services/api/project';
import Pagination from '@/components/base/Pagination.vue';

let runsService;
let casesService;
let defectsService;
let projectsService;

export default {
  components: {
    DeleteIcon,
    EditIcon,
    ArchieveIcon,
    StarFillIcon,
    StarIcon,
    ProjectTableSkeleton,
    CountsValueSkeleton,
    Avatar,
    AvatarGroupSkeleton,
    Pagination
  },
  mixins: [handleLoading],
  props: {
    filteredHeaders: Array,
    filteredItems: Array,
    itemKey: String,
    rowClass: Function,
    writeProject:{
      type: Boolean,
      default: false,
    },
    deleteProject:{
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      debounce: false,
    };
  },
  computed: {
    handle() {
      return this.$route.params.handle;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },
  },
  created() {
    runsService = makeRunService(this.$api);
    casesService = makeCasesService(this.$api);
    defectsService = makeDefectService(this.$api);
    projectsService = makeProjectsService(this.$api);
  },
  methods: {
    formattedDate,
    onRowClick(item) {
      if (!this.debounce) {
        this.$emit('select-item', item);
      } else {
        this.debounce = false;
      }
    },
    onEdit(item) {
      this.$emit('edit-item', item);
    },
    onArchive(item) {
      this.$emit('archive-item', item);
    },
    onUnArchive(item) {
      this.$emit('unarchive-item', item);
    },
    onDelete(item) {
      this.$emit('delete-item', item);
    },
    toggleStar(item) {
      this.debounce = true;
      this.$emit('toggle-star', item);
    },
    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.itemsPerPage) {
        this.$emit('update-pagination', { page: newPage, itemsPerPage: newItemsPerPage });
      }
    },
    async countProjectTestRuns(handle, key) {
      return await runsService.getTestRunsCount(handle, key);
    },
    async countProjectTestCases(handle, key) {
      return await casesService.getTestCasesCount(handle, key);
    },
    async countProjectDefects(handle, key) {
      return await defectsService.getDefectsCount(handle, key);
    },
    async getProjectMembers(handle, key) {
      return await projectsService.getProjectMembers(handle, key);
    },
  },
};
</script>

