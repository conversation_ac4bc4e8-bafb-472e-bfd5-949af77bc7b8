<template>
  <v-sheet
    class="project-item"
    rounded="lg"
    p="4"
    color="#f9fafb"
    @click="cardClick"
  >
    <div class="d-flex align-center justify-space-between py-4 px-2">
      <div class="d-flex gap-4">
        <v-btn
          icon
          class="mr-2"
          @click="toggleStar(item, $event)"
        >
          <StarFillIcon v-if="item.customFields?.star" />
          <StarIcon v-else />
        </v-btn>
        <Avatar 
          :size="40"
          :avatar-src="item.avatarUrl || require('@/assets/png/project.png')"
          class="mr-2"
        />
      </div>
      <div
        v-if="isColumnChecked('name')"
        class="text-start"
      >
        <div
          class="fw-semibold fs-12px"
        >
          {{ item.name }}
        </div>
        <div class="text-theme-secondary fs-12px text-truncate mw-100px">
          {{ item.customFields?.description }}
        </div>
        <div
          v-if="isColumnChecked('key') && isColumnChecked('name')"
          class="text-theme-secondary fs-12px mt-1"
        >
          {{ item.key }}
        </div>
      </div>
      <div
        v-else-if="isColumnChecked('key')"
        class="text-start"
      >
        <div class="text-theme-secondary fs-12px mt-1">
          {{ item.key }}
        </div>
      </div>
    </div>
    <div class="d-flex align-center">
      <div
        class="mr-2 text-start"
      >
        <div class="text-theme-secondary fs-12px font-weight-medium">
          {{ $t('lastChanges') }}:
        </div>
        <div class="fw-semibold fs-14px text-end">
          {{ formattedDate(item.updatedAt) }}
        </div>
      </div>
      <v-menu
        v-if="_writeProject || _deleteProject"
        content-class="custom_ele elevation-0"
        nudge-bottom="35"
        left
      >
        <template #activator="{ on }">
          <v-btn
            icon
            v-on="on"
          >
            <v-icon color="gray-ish">
              mdi-dots-vertical
            </v-icon>
          </v-btn>
        </template>
        <v-list dense>
          <v-list-item
            v-if="!isArchived && _writeProject"
            class="pointer"
            @click="onEdit(item)"
          >
            <EditIcon />
            <v-list-item-content class="ml-2">
              {{ $t('edit') }}
            </v-list-item-content>
          </v-list-item>
          <v-list-item
            v-if="!isArchived && _writeProject"
            class="pointer"
            @click="onArchive(item)"
          >
            <ArchieveIcon />
            <v-list-item-content class="ml-2">
              {{ $t('archive') }}
            </v-list-item-content>
          </v-list-item>
          <v-list-item
            v-else-if="isArchived && _writeProject"
            class="pointer"
            @click="onUnArchive(item)"
          >
            <ArchieveIcon />
            <v-list-item-content class="ml-2">
              {{ $t('unarchive') }}
            </v-list-item-content>
          </v-list-item>
          <v-list-item
            v-if="_deleteProject"
            class="pointer"
            @click="onDelete(item)"
          >
            <DeleteIcon />
            <v-list-item-content class="ml-2 error--text">
              {{ $t('delete') }}
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>
    <v-sheet
      v-if="isColumnChecked('test') || isColumnChecked('cases') || isColumnChecked('defects')"
      color="#f3f4f7"
      rounded="lg"
      class="py-2 px-5 mx-4 text-start align-center d-flex justify-space-between"
    >
      <div
        v-if="isColumnChecked('test')"
      >
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectTestRuns(handle, item.key)"
          :count-for="$t('testRuns')"
        >
          <template #value="{ count }">
            <span class="fw-semibold fs-14px">{{ count }}</span> <span class="text-theme-secondary fs-12px font-weight-medium">{{ $t('testRuns') }}</span>
          </template>
        </CountsValueSkeleton>
      </div>
      <div v-if="isColumnChecked('cases')">
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectTestCases(handle, item.key)"
          :count-for="$t('testCases')"
        >
          <template #value="{ count }">
            <span class="fw-semibold fs-14px">{{ count }}</span> <span class="text-theme-secondary fs-12px font-weight-medium">{{ $t('testCases') }}</span>
          </template>
        </CountsValueSkeleton>
      </div>
      <div v-if="isColumnChecked('defects')">
        <CountsValueSkeleton
          :key="item.uid"
          :endpoint-func="() => countProjectDefects(handle, item.key)"
          :count-for="$t('defects')"
        >
          <template #value="{ count }">
            <span class="fw-semibold fs-14px">{{ count }}</span> <span class="text-theme-secondary fs-12px font-weight-medium">{{ $t('defects') }}</span>
          </template>
        </CountsValueSkeleton>
      </div>
    </v-sheet>
    <div class="d-flex align-center justify-space-between px-5 w-full py-5">
      <div
        v-if="isColumnChecked('users')"
        class="user-avatars-container"
      >
        <AvatarGroupSkeleton
          :endpoint-func="() => getProjectMembers(handle, item.key)"
          :size="24"
        />
      </div>
      <div v-if="isColumnChecked('createdAt')">
        <div class="text-theme-secondary fs-12px font-weight-medium">
          {{ $t('creationDate') }}:
        </div>
        <div class="fw-semibold fs-14px">
          {{ formattedDate(item.createdAt) }}
        </div>
      </div>
    </div>
  </v-sheet>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import ArchieveIcon from '@/assets/svg/archived.svg';
import { formattedDate } from '@/utils/util';
import StarFillIcon from '@/assets/svg/star-fill.svg';
import StarIcon from '@/assets/svg/star.svg';
import CountsValueSkeleton from '@/components/Skeletons/CountsValue/CountsValueSkeleton.vue';
import AvatarGroupSkeleton from '@/components/Skeletons/AvatarGroup/AvatarGroupSkeleton.vue';
import makeRunService from "@/services/api/run";
import makeCasesService from '@/services/api/case';
import makeDefectService from '@/services/api/defect';
import makeProjectsService from '@/services/api/project';
import Avatar from '@/components//base/Avatar.vue';
let runsService;
let casesService;
let defectsService;
let projectsService;

export default {
  components: {
    DeleteIcon,
    EditIcon,
    ArchieveIcon,
    StarFillIcon,
    StarIcon,
    CountsValueSkeleton,
    AvatarGroupSkeleton,
    Avatar
  },
  props: {
    item: Object,
    filterItems: Array
  },
  computed: {
    isArchived() {
      return this.item.archivedAt != null;
    },
    _writeProject(){
      return this.authorityTo('write_project')
    },
    _deleteProject(){
      return this.authorityTo('delete_project')
    },
    limitedUserImages() {
      return this.item.members ? this.item.members.slice(0, 3) : [];
    },
    extraUsersCount() {
      return this.item.members && this.item.members.length > 3 ? this.item.members.length - 3 : 0;
    },
    showExtraUsersCount() {
      return this.extraUsersCount > 0;
    },
    handle() {
      return this.$route.params.handle;
    }
  },
   created() {
    runsService = makeRunService(this.$api);
    casesService = makeCasesService(this.$api);
    defectsService = makeDefectService(this.$api);
    projectsService = makeProjectsService(this.$api);
  },
  methods: {
    formattedDate,
    cardClick() {
      this.$emit('select-item', this.item);
    },
    toggleStar(item, event) {
      event.stopPropagation();
      this.$emit('toggle-star', item);
    },
    onEdit(item) {
      this.$emit('edit-item', item);
    },
    onArchive(item) {
      this.$emit('archive-item', item);
    },
    onUnArchive(item) {
      this.$emit('unarchive-item', item);
    },
    onDelete(item) {
      this.$emit('delete-item', item);
    },
    isColumnChecked(columnName) {
      if(this.filterItems && this.filterItems.length > 0){
        const item = this.filterItems.find((item) => item.value == columnName);
        return item ? item.checked : false;
      }
      return false;
    },
    getUserInitials(member) {
      if (!member) return '';
      
      const firstInitial = member.firstName ? member.firstName.charAt(0) : '';
      const lastInitial = member.lastName ? member.lastName.charAt(0) : '';
      
      return (firstInitial + lastInitial).toUpperCase();
    },
    async countProjectTestRuns(handle, key) {
      return await runsService.getTestRunsCount(handle, key);
    },
    async countProjectTestCases(handle, key) {
      return await casesService.getTestCasesCount(handle, key);
    },
    async countProjectDefects(handle, key) {
      return await defectsService.getDefectsCount(handle, key);
    },
    async getProjectMembers(handle, key) {
      return await projectsService.getProjectMembers(handle, key);
    },
  },
};
</script>

<style scoped>
  .project-item:hover {
    cursor: pointer;
  }
  
  .user-avatars-container {
    display: flex;
    align-items: center;
  }
  
  .avatar-group {
    position: relative;
  }
  
  .avatar-overlap {
    margin-left: -8px !important;
    border: 2px solid #f9fafb;
  }
  
  .avatar-overlap:first-child {
    margin-left: 0;
  }
  
  .avatar-count {
    background-color: #ebecf0;
    color: #44546f;
    font-weight: bold;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .user-initial-avatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #DFE1E6;
    color: #44546F;
    font-size: 12px;
    font-weight: bold;
  }
</style>