<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ getDialogTitle() }}
          </h2>
          <v-btn
            icon
            @click="handleCancel"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <p class="text-body-1 text-left font-weight-light mt-3 d-flex justify-space-between">
          <span>{{ getCurrentStatusLabel() }}</span>
          <span :class="getStatusTextColor()">{{ getCurrentStatusText() }}</span>
        </p>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="gray-100"
              width="100%"
              class="text-capitalize"
              elevation="0"
              @click="handleCancel"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              color="primary"
              width="100%"
              elevation="0"
              class="white--text text-capitalize"
              @click="handleConfirm"
            >
              {{ $t('confirm') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'ConfirmationDialog',

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    item: {
      type: Object,
      default: () => ({})
    },
    switchType: {
      type: String,
      default: 'success'
    }
  },

  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
  },

  methods: {
   getDialogTitle() {
     switch(this.switchType) {
      case 'success':
        return this.$t('confirmSuccessStatus');
      case 'failure':
        return this.$t('confirmFailureStatus');
      case 'completed':
        return this.$t('confirmCompletedStatus');
      case 'default':
        return this.$t('setDefaultStatus');
      default:
        return this.$t('confirmSuccessStatus');
    }
   },
    
   getCurrentStatusLabel() {
      switch(this.switchType) {
      case 'success':
        return this.$t('willMarkAsSuccess');
      case 'failure':
        return this.$t('willMarkAsFailure');
      case 'completed':
        return this.$t('willMarkAsCompleted');
      case 'default':
        return this.$t('currentDefaultWillBeReassigned');
      default:
        return this.$t('willMarkAsSuccess');
    }
   },
    
   getCurrentStatusText() {
     switch(this.switchType) {
      case 'success':
        return this.$t('successLabel');
      case 'failure':
        return this.$t('failureLabel');
      case 'completed':
        return this.$t('completedLabel');
      case 'default':
        return this.item.name || this.$t('defaultLabel');
      default:
        return this.item.name || '';
     }
   },

    
   getStatusTextColor() {
     switch(this.switchType) {
      case 'success':
        return 'success--text'; 
      case 'failure':
        return 'error--text'; 
      case 'completed':
        return 'info--text'; 
      case 'default':
        return 'primary--text'; 
      default:
        return 'black--text';
    }
   },

    handleCancel() {
      this.$emit('cancel')
    },

    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>