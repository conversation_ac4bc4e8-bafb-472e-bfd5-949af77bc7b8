<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="table-fixed data-table-style mt-6 font-inter"
      :headers="filteredHeaders"
      :items="filteredItems"
      :item-key="itemKey"
      :item-class="rowClass"
      :options.sync="options"
      hide-default-footer
    >
      <template #[`item.date`]="{ item }">
        <div class="text-truncate">
          <span v-if="formatExpirationDate(item.createdAt)">
            {{ formatExpirationDate(item.createdAt) }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.projectName`]="{ item }">
        <div class="text-truncate">
          <span v-if="item?.project?.name">
            {{ item.project.name }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.entityId`]="{ item }">
        <div class="text-truncate">
          <span v-if="item?.entityId">
            {{ item.entityId }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.entityName`]="{ item }">
        <div class="text-truncate">
          <span v-if="item?.tablename">
            {{ item.tablename }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.action`]="{ item }">
        <div class="text-truncate">
          <span v-if="item.action">
            {{ item.action }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.actor`]="{ item }">
        <div class="text-truncate">
          <span v-if="item.actor?.fullName">
            {{ item.actor.fullName }}
          </span>
          <v-icon v-else>
            mdi-minus
          </v-icon>
        </div>
      </template>
      <template #[`item.actions`]="{ item }">
        <v-btn
          v-if="(item.action === 'CREATE' || item.action === 'SOFT_DELETE') && !item.revertedAt"
          icon
          color="primary"
          @click.stop="refreshAuditLogItem(item)"
        >
          <Revert class="custom_color" />
        </v-btn>
      </template>
    </v-data-table>
    <template v-else>
      <AuditTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import handleLoading from '@/mixins/loader.js';
import AuditTableSkeleton from '@/components/Skeletons/Settings/AuditLog/AuditLogTableSkeleton.vue';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import Revert from '@/assets/svg/revert.svg';

export default {
  components: {
    AuditTableSkeleton,
    Revert
  },
  mixins: [handleLoading],
  props: {
    filteredHeaders: Array,
    filteredItems: Array,
    itemKey: String,
    rowClass: Function,
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
  },
  setup() {
    const { formatDateTime } = useDateFormatter();
    return { formatDateTime };
  },
  data() {
    return {
      options: {
        page: this.currentPage,
        itemsPerPage: this.itemsPerPage
      }
    };
  },

  watch: {
    options: {
      handler(newOptions, oldOptions) {
        if (oldOptions && 
            (newOptions.page !== oldOptions.page || 
             newOptions.itemsPerPage !== oldOptions.itemsPerPage)) {
          this.$emit('update-pagination', newOptions);
        }
      },
      deep: true
    }
  },

  methods: {
    refreshAuditLogItem(item) {
      this.$emit('open-revert-modal', item);
    },
    formatExpirationDate(date) {
      if (!date) return '';
      return this.formatDateTime(date);
    },
  }
};
</script>

<style scoped>
.v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
  background-color: #ffffff;
}

.v-data-table-header__icon {
  opacity: 1 !important;
}

.v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
  background-color: #F9FAFB;
}

.pointer {
  cursor: pointer;
}

.header_text {
  color: #475467;
  font-weight: 700;
}

.custom_color {
  color: #667085;
}

.custom_border {
  border: 2px solid #ffffff;
}
.my-table table {
  table-layout: fixed;
}
.v-data-table table {
  border-collapse: collapse;
}

.v-data-table th {
  border: none !important;
}

.v-data-table td {
  border: none !important;
  cursor: pointer;
}

.v-data-table tr.project-item:hover {
  border: 1px solid #d1e1ff !important;
}

.v-data-table .v-data-table__wrapper tbody tr {
  height: 80px;
}

.v-data-table .v-data-table__wrapper tbody tr td {
  height: 80px !important;
  padding-top: 0;
}

.v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: transparent !important;
}
</style>