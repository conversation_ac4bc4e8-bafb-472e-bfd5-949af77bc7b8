<template>
  <v-container
    class="pa-6 white align-start card rounded-lg"
    fluid
  >
    <v-row
      v-if="!isBlankTable"
      justify="start"
      align="start"
    >
      <v-col
        cols="7"
        sm="4"
        class="search-bar-style"
      >
        <v-text-field
          v-if="!skeletonLoaderState"
          v-model="searchTerm"
          class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
          :placeholder="$t('placeHolder.searchByColumn')"
          height="40"
          clearable
          clear-icon="mdi-close-circle"
          background-color="#F9F9FB"
          hide-details
          @input="onSearchInput"
          @click:clear="onSearchClear"
        >
          <template #prepend-inner>
            <SearchIcon />
          </template>
        </v-text-field>
        <v-skeleton-loader
          v-else
          class="rounded-lg"
          height="40"
          width="325"
          type="button"
        />
        <TestCasesFilter
          :current-filters="filters"
          @filters="applyFilters"
        />
      </v-col>
      <v-col
        cols="5"
        sm="8"
        class="search-bar-style setting-btn-style"
      >
        <div class="btn-selector">
          <template>
            <div class="text-center">
              <SettingsMenu 
                table-type="case"
              />
            </div>
          </template>
        </div>
      </v-col>

      <!-- Filter Chips Row -->
      <v-col
        v-if="hasActiveFilters"
        cols="12"
        sm="12"
        class="mt-2 mb-2"
      >
        <TestCaseFilterChips
          :filters="filters"
          :results-count="displayTableData.length"
          :priorities="priorities"
          @update-filters="updateFilters"
          @clear-filters="clearFilters"
        />
      </v-col>

      <v-col
        cols="12"
        sm="12"
        class="breadcrumb-container mt-4"
      >
        <v-breadcrumbs 
          v-if="!skeletonLoaderState"
          :items="breadCrumbs"
        >
          <template #item="{ item }">
            <v-breadcrumbs-item v-if="isLastItem(item)">
              <b>{{ item.text }}</b>
            </v-breadcrumbs-item>
            <v-breadcrumbs-item v-else>
              <span 
                style="color: #667085; cursor: pointer;"
                class="hover-underline"
                @click="handleBreadcrumbClick(item)"
              >{{ item.text }}</span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
        <v-skeleton-loader
          v-else
          class="rounded-lg pl-3"
          height="24"
          width="250"
          type="text"
        />
      </v-col>
      <v-col
        cols="12"
        sm="12"
      >
        <TestCasesListSkeleton v-if="casesLoading" />
        
        <div
          v-else-if="!skeletonLoaderState"
          :class="tableContainerClass"
        >
          <v-data-table
            v-if="tableItems.length"
            id="case-table"
            v-model="selectedRows"
            :headers="filteredHeaders"
            :items="tableItems"
            item-key="testCaseRef"
            show-select
            :class="tableClass"
            :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
            :item-class="getItemClass"
            :sort-by.sync="sortBy"
            :sort-desc.sync="sortDesc"
            :fixed-header="true"
            :height="shouldHaveOverflow ? '70vh' : 'auto'"
            :hide-default-footer="!isImport"
            :disable-pagination="!isImport"
            @input="handleSelectTestCases"
            @click:row="handleClick"
          >
            <template #[`header.data-table-select`]="{ props }">
              <div class="d-flex justify-start align-center">
                <iconDots />
                <v-checkbox
                  id="select-all-checkbox"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  indeterminate-icon="icon-indeterminate"
                  :input-value="props.value"
                  :indeterminate="props.indeterminate"
                  @change="handleSelectAll"
                />
              </div>
            </template>

            <template #[`item.data-table-select`]="{ isSelected, select }">
              <div class="d-flex justify-start align-center">
                <span
                  v-if="isImport && isSelected"
                  class="mr-1"
                  style="margin-top: 6.5px; cursor: grab;"
                >
                  <iconDots /> 
                </span>
            
                <v-checkbox
                  id="remember-me-checkbox"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :input-value="isSelected"
                  @change="select"
                  @click.stop
                /> 
              </div>
            </template>

            <template #[`header.actions`]="{ header }">
              <div class="d-none">
                {{ header.text }} 
              </div>
            </template>
            <template #[`item.id`]="{ item }">
              <span
                v-if="!isImport"
                class="custom-attribute font-weight-regular text-theme-table-text cursor-pointer"
              >{{ $route.params.key }}-{{ item.testCaseRef }}</span>
              <span
                v-else
                class="custom-attribute font-weight-regular text-theme-table-text"
              >{{ item.externalId }}</span>
            </template>
            <template #[`item.name`]="{ item }">
              <v-tooltip
                bottom
                left
                max-width="485px"
                :disabled="!isTruncated"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div 
                    :ref="'caseName_' + item.uid"
                    class="custom-attribute text-truncate fw-semibold text-theme-table-text cursor-pointer"
                    v-bind="attrs"
                    v-on="on"
                    @mouseover="checkTruncate(item.uid, 'caseName')"
                  >
                    {{ item.name }}
                  </div>
                </template>
                <span>{{ item.name }}</span>
              </v-tooltip>
            </template>
            <template #[`item.priority`]="{ item }">
              <span
                v-if="!isImport"
                :style="{ color: getPriorityColor(item.priority, priorities) }" 
                class="font-weight-bold"
              >{{ getPriorityName(item.priority, priorities) }}</span>

              <span
                v-else
                :style="{ color: getPriorityColor(item.priorityText, priorities) }" 
                class="font-weight-bold"
              >{{ item.priorityText }}</span>
            </template>
            <template #[`item.tags`]="{ item }">
              <div v-if="!isImport">
                <div v-if="relationsLoading">
                  <v-skeleton-loader
                    type="text"
                    height="16"
                    class="w-100"
                  />
                </div>
                <TagEditor
                  v-else-if="item.tags"
                  :key="`${item.testCaseRef}-${item.tags.map(t => t.uid).join('-')}`"
                  :is-project-archived="isProjectArchived"
                  :items="tags"
                  :value="item.tags"
                  @updateTag="updateTag($event, item)"
                  @refreshTags="fetchTags"
                />
                <span v-else>-</span>
              </div>
              <div v-else>
                <span>{{ item.tags.join(', ') }}</span>
              </div>
            </template>
            <template #[`item.actions`]="{ item }">
              <div
                v-if="!isImport"
                class="d-flex flex-row justify-center"
              >
                <v-menu 
                  :ref="`mainMenu_${item.uid}`"
                  offset-y
                  :close-on-content-click="true"
                  :close-on-click="true"
                  :close-on-click-outside="true"
                >
                  <template #activator="{ on }">
                    <v-btn
                      icon
                      v-on="on"
                    >
                      <v-icon>mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list
                    dense
                    flat
                    class="text-left"
                  >
                    <v-tooltip
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item 
                            :disabled="!writeEntity" 
                            :class="{ 'disabled-action': isProjectArchived }"
                            @click="!isProjectArchived && handleRowEdit(item.testCaseRef, item.uid)" 
                          >
                            <EditIcon />
                            <v-list-item-content class="ml-2 fs-14px text-theme-label">
                              {{ $t('edit') }}
                            </v-list-item-content>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{ $t('noPermissionToDo', { action: $t('edit').toLowerCase(), type: $t('testCase').toLowerCase() }) }}
                      </span>
                    </v-tooltip>
                    
                    <v-list-item 
                      :class="{ 'disabled-action': isProjectArchived }"
                      @click="handleDuplicateToProjectSidebar(item)"
                    >
                      <DuplicateIcon />
                      <v-list-item-content class="ml-2 fs-14px text-theme-label">
                        {{ $t('duplicateToProject') }}
                      </v-list-item-content>
                    </v-list-item>
                    
                    <v-tooltip
                      bottom
                      :disabled="deleteEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item 
                            :disabled="!deleteEntity"
                            :class="{ 'disabled-action': isProjectArchived }"
                            @click="!isProjectArchived && deleteTestCase(item.uid)" 
                          >
                            <DeleteIcon />
                            <v-list-item-content class="ml-2 fs-14px text-theme-label">
                              {{ $t('delete') }}
                            </v-list-item-content>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{ $t('noPermissionToDo', { action: $t('delete').toLowerCase(), type: $t('testCase').toLowerCase() }) }}
                      </span>
                    </v-tooltip>
                  </v-list>
                </v-menu>
              </div>
            </template>
          </v-data-table>
          <v-col
            v-else
            cols="12"
            sm="12"
            class="h-100 flex"
          >
            <div class="block">
              <img
                :src="require('@/assets/png/blank-case-background.png')"
                style="margin-left: auto; margin-right: auto; display: block"
              >
              <h2 class="blank-title">
                {{ $t('manageTestCaseEasily') }}
              </h2>
              <p class="blank-description">
                {{ $t('startCreateTestCase') }}
              </p>
              <div class="w-100 flex">
                <v-btn
                  color="#0C2FF3"
                  dark
                  class="text-capitalize px-6"
                  depressed
                  :to="{ name: 'CreateTestCases' }"
                >
                  {{ $t('createTestCase') }}
                  <v-icon
                    class="ml-1"
                    size="xs"
                  >
                    mdi-plus
                  </v-icon>
                </v-btn>
              </div>
            </div>
          </v-col>
        </div>
        <!-- Use Pagination component -->
        <Pagination
          v-if="showPagination && pagination && pagination.total > 0"
          :page="localCurrentPage"
          :items-per-page="localItemsPerPage"
          :total-pages="pagination.totalPages"
          :total-items="pagination.total"
          @update:pagination="onPaginationChange"
        />
        <v-col
          v-if="isImport"
          cols="12"
          sm="12"
          class="d-flex justify-end pa-0 ma-0 mt-5"
        >
          <v-btn
            depressed
            background-color="#F2F4F7"
            class="font-inter text-capitalize black--text mr-4 "
            height="40"
            min-width="140px"
            @click="$emit('back')" 
          >
            {{ $t('back') }}
          </v-btn>
    
          <v-btn
            class="text-capitalize btn-theme px-6 py-2"
            color="primary"
            depressed
            height="40"
            min-width="140px"
            @click="$emit('gotoRepository')" 
          >
            {{ $t('gotoRepository') }}
          </v-btn>
        </v-col>
      </v-col>
      <v-col
        v-if="!isRepository"
        cols="12"
        sm="12"
        class="create-btn w-full gap-3"
      >
        <v-text-field
          v-if="!skeletonLoaderState"
          v-model="testName"
          class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
          :placeholder="$t('placeHolder.createQuickTestCase')"
          height="38"
          background-color="#F9F9FB"
          single-line
        >
          <template #prepend-inner>
            <iconAttach class="mt-1" />
          </template>
        </v-text-field>
        <v-skeleton-loader
          v-else
          class="rounded-lg w-45"
          height="40"
          type="button"
        />
        
        <v-select
          v-if="!skeletonLoaderState"
          v-model="testTemplate"
          height="38px"
          :items="testTemplates"
          item-text="name"
          dense
          item-value="uid"
          class="text-field bottom-input-style rounded-lg field-theme custom-prepend"
          placeholder="Choose the template"
          append-icon="mdi-chevron-down"
          background-color="#F9F9FB"
        />
        <v-skeleton-loader
          v-else
          class="rounded-lg w-45"
          height="40"
          type="button"
        />
        <v-tooltip
          v-if="!skeletonLoaderState"
          bottom
          :disabled="writeEntity"
        >
          <template #activator="{ on, attrs }">
            <div
              v-bind="attrs"
              v-on="on"
            >
              <v-btn
                color="blue"
                class="text-capitalize mt-0"
                :class="{ 'disabled-action': !testName || !testTemplate || isProjectArchived }"
                depressed
                :disabled="!writeEntity"
                height="40"
                @click="quickCreate"
              >
                {{ $t('create') }}
              </v-btn>
            </div>
          </template>
          <span>
            {{ $t('noPermissionToDo', { action: $t('create').toLowerCase(), type: $t('testCase').toLowerCase() }) }}
          </span>
        </v-tooltip>
        <v-skeleton-loader
          v-else
          class="rounded-lg primary w-10"
          height="40"
          type="button"
        />
      </v-col>
      <v-col
        v-if="!isRepository"
        cols="12"
        sm="12"
        class="breadcrumb-container mt-4"
      />
      <v-col
        v-if="!isRepository"
        cols="12"
        sm="12"
        class="round-8 action-btn-wrapper pl-6 py-4"
      >
        <v-btn
          v-if="allowAction && isSelected"
          width="141px"
          color="#F2F4F7"
          full-width
          height="40"
          depressed
          class="text-capitalize btn-theme mr-3"
          elevation="0"
          @click="onCancelAction"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-menu
          v-if="isSelected"
          v-model="menuOpen"
          offset-y
          top
          right
          :close-on-content-click="false"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              color="primary"
              class="text-capitalize btn-theme"
              height="40"
              width="141px"
              v-bind="attrs"
              :depressed="true"
              v-on="on"
            >
              {{ $t('actions') }}
              <v-icon size="20">
                {{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
              </v-icon>
            </v-btn>
          </template>
          <v-list class="actions-list font-inter text-left">
            <v-list-item
              :key="1"
              :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
              @click="handleAddToTestRuns"
            >
              <div class="d-flex align-center">
                <AddIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('testruns.addToTestRuns') }}
              </v-list-item-title>
            </v-list-item>
            <v-list-item
              :key="2"
              :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
              @click="handleCreateTestRunDialogue"
            >
              <div class="d-flex align-center">
                <PlusIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('testruns.createTestRun') }}
              </v-list-item-title>
            </v-list-item>
            <v-tooltip
              bottom
              :disabled="writeEntity"
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-list-item
                    :key="1"
                    :disabled="!writeEntity"
                    :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
                    @click="handleEditClick"
                  >
                    <div class="d-flex align-center">
                      <EditIcon />
                    </div>
                    <v-list-item-title class="pl-3">
                      {{ $t('edit') }}
                    </v-list-item-title>
                  </v-list-item>
                </div>
              </template>
              <span v-if="!writeEntity">
                {{ $t('noPermissionToDo', { action: $t('edit').toLowerCase(), type: $t('testCase').toLowerCase() }) }}
              </span>
            </v-tooltip>
            <v-list-item 
              :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
              @click="handleBulkDuplicateToProjectSidebar"
            >
              <div class="d-flex align-center">
                <DuplicateIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('duplicateToProject') }}
              </v-list-item-title>
            </v-list-item>
            <v-list-item
              :key="4"
              :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
              @click="handleExportClick"
            >
              <div class="d-flex align-center">
                <ExportIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('export') }}
              </v-list-item-title>
            </v-list-item>
            <v-tooltip
              bottom
              :disabled="deleteEntity"
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-list-item
                    :key="5"
                    :disabled="!deleteEntity"
                    :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived}"
                    @click="handleBulkRemove"
                  >
                    <div class="d-flex align-center">
                      <DeleteIcon />
                    </div>
                    <v-list-item-title class="pl-3 error--text">
                      {{ $t('remove') }}
                    </v-list-item-title>
                  </v-list-item>
                </div>
              </template>
              <span v-if="!deleteEntity">
                {{ $t('noPermissionToDo', { action: $t('remove').toLowerCase(), type: $t('testCase').toLowerCase() }) }}
              </span>
            </v-tooltip>
          </v-list>
        </v-menu>
      </v-col>
    </v-row>
    <v-row
      v-else
      class="h-100"
      justify="start"
      align="start"
    >
      <v-col
        cols="12"
        sm="12"
        class="h-100 flex"
      >
        <div class="block">
          <img
            :src="require('@/assets/png/blank-case-background.png')"
            style="margin-left: auto; margin-right: auto; display: block"
          >
          <h2 class="blank-title">
            {{ $t('manageTestCaseEasily') }}
          </h2>
          <p class="blank-description">
            {{ $t('startCreateTestCase') }}
          </p>
          <div class="w-100 flex">
            <v-btn
              color="#0C2FF3"
              dark
              class="text-capitalize px-6"
              depressed
              :to="{ name: 'CreateTestCases' }"
            >
              {{ $t('createTestCase') }}
              <v-icon
                class="ml-1"
                size="xs"
              >
                mdi-plus
              </v-icon>
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
    <CaseAddTestCasesToTestRuns
      :is-open="isOpenAddToTestRunsDialog"
      :selected-rows="selectedRows"
      @closeDialog="handleCloseAddToTestRunsDialog"
    />
    <CaseEditDialog
      :is-open="isOpenEditDialog"
      :priorities="priorities"
      :tags="tags"
      @closeDialog="handleCloseEditDialog"
      @clickSave="updateSelectedCases"
    />
    <CaseCreateTestRunAddTestCases
      :is-open="isOpenCreateTestRunDialog"
      :selected-rows="selectedRows"
      @closeDialog="handleCloseCreateTestRunDialog"
    />
    <CaseExportDialog
      :is-open="isOpenExportDialog"
      :selected-rows="selectedRows"
      @closeDialog="handleCloseExportDialog"
    />
    <CaseConfirmDialog
      :is-open="isOpenConfirmDialog"
      @closeDialog="handleCloseConfirmDialog"
      @confirm="handleConfirm"
    />
    <CaseConfirmBulkDeleteDialog
      :title="$t('testruns.test_case.bulk_remove.title', { count: selectedRows?.length })"
      :is-open="isOpenConfirmBulkDeleteDialog"
      @closeDialog="handleCloseConfirmBulkDeleteDialog"
      @confirm="confirmBulkRemove"
    />
    <v-snackbar
      v-model="selectionInProgress"
      :timeout="-1"
      class="alert w-full"
      bottom
      right
    >
      <div class="d-flex align-start gap-4 w-full">
        <SwalAlertIcon />
        <div class="d-flex flex-column w-full">
          <span class="mb-2">Selecting items: {{ selectionProgress }}%</span>
          <v-progress-linear
            :value="selectionProgress"
            height="5"
            color="primary"
            class="mb-0 w-full"
            rounded
          />
        </div>
      </div>
    </v-snackbar>
    
    <!-- Duplicate to Project Sidebar -->
    <DuplicateToProjectSidebar
      v-model="showDuplicateSidebar"
      :selected-cases="selectedCasesForDuplicate"
      :current-handle="$route.params.handle"
      :current-project-key="$route.params.key"
      @duplicate-success="handleDuplicateSuccess"
      @input="handleCloseDuplicateSidebar"
    />
  </v-container>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue';
import { debounce } from 'lodash';
import Pagination from '@/components/base/Pagination.vue';
import { useTestCasesList } from '@/composables/modules/cases/list';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import TestCasesFilter from '@/views/Tests/Case/Components/Filter.vue';
import TestCaseFilterChips from '@/components/Cases/CaseFilterChips.vue';
import CaseEditDialog from '@/views/Tests/Case/Components/EditDialog.vue';
import CaseAddTestCasesToTestRuns from '@/views/Tests/Case/Components/AddTestCasesToTestRuns.vue';
import CaseCreateTestRunAddTestCases from '@/views/Tests/Case/Components/CreateTestRunAddTestCases.vue';
import CaseExportDialog from '@/views/Tests/Case/Components/ExportDialog.vue';
import CaseConfirmDialog from '@/views/Tests/Case/Components/ConfirmDialog.vue';
import CaseConfirmBulkDeleteDialog from '@/views/Tests/Case/Components/ConfirmBulkDeleteDialog.vue';
import iconAttach from '@/assets/svg/outline-file-attach16x16-blue.svg';
import SearchIcon from '@/assets/svg/search-icon.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import ExportIcon from '@/assets/svg/export.svg';
import AddIcon from '@/assets/svg/add.svg';
import PlusIcon from '@/assets/svg/plus24px.svg';
import DuplicateIcon from '@/assets/svg/duplicate.svg';
import TagEditor from '@/components/base/TagEditor.vue';
import SwalAlertIcon from '@/assets/svg/swal_alert.svg';
import iconDots from '@/assets/svg/dots-20x20-gray.svg';
import TestCasesListSkeleton from '@/components/Skeletons/TestCases/List.vue';
import DuplicateToProjectSidebar from '@/components/Cases/DuplicateToProjectSidebar.vue';

export default {
  components: {
    TestCasesFilter,
    TestCaseFilterChips,
    CaseAddTestCasesToTestRuns,
    CaseEditDialog,
    CaseExportDialog,
    CaseConfirmDialog,
    iconAttach,
    SettingsMenu,
    CaseConfirmBulkDeleteDialog,
    SearchIcon,
    DeleteIcon,
    EditIcon,
    ExportIcon,
    AddIcon,
    PlusIcon,
    DuplicateIcon,
    TagEditor,
    SwalAlertIcon,
    iconDots,
    Pagination,
    TestCasesListSkeleton,
    CaseCreateTestRunAddTestCases,
    DuplicateToProjectSidebar,
  },
  props: {
    isRepository: Boolean,
    fromRun: Boolean,
    caseItems: Array,
    initialSelected: {
      type: Array,
      default: () => {
        return [];
      },
    },
    breadCrumbs: {
      type: Array,
      default: () => []
    },
    tableFilter: {
      type: Boolean,
      default: false
    },
    selectedCase: Object,
    allowAction: {
      type: Boolean,
      default: true,
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    deleteEntity: {
      type: Boolean,
      default: false
    },
    isImport: {
      type: Boolean,
      default: false
    },
    pagination: Object,
    currentPage: {
      type: Number, 
      default: 1
    },
    itemsPerPage: Number,
    updatePagination: Function,
    relationsLoading: {
      type: Boolean,
      default: false
    },
    casesLoading: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    },
    showPagination: {
      type: Boolean,
      default: true
    },
  },
  setup(props, context) {
    const {
      // Data refs
      isBlankTable,
      isOpenAddToTestRunsDialog,
      isOpenEditDialog,
      isOpenExportDialog,
      isOpenConfirmDialog,
      headers,
      isOpenConfirmBulkDeleteDialog,
      isSelectedData,
      isAddedTable,
      openFilterDrawer,
      toggleSelect,
      testName,
      testTemplate,
      tableSelectedCases,
      menuOpen,
      searchTerm,
      filters,
      actionBtnShow,
      selectedData,
      states,
      tags,
      testTemplates,
      localCaseItems,
      itemToDelete,
      statuses,
      priorities,
      skeletonLoaderState,
      tableFilterData,
      tableItems,
      isOpenCreateTestRunDialog,
      // Computed properties
      activeRow,
      selectedRows,
      isSelected,
      selectedItemsTitles,
      filteredHeaders,
      displayTableData,
      isProjectArchived,
      shouldHaveOverflow,
      tableContainerClass,
      tableClass,
      
      // Methods
      handleCloseConfirmBulkDeleteDialog,
      handleCloseConfirmDialog,
      handleConfirm,
      handleCloseAddToTestRunsDialog,
      handleEditClick,
      handleCloseEditDialog,
      handleExportClick,
      handleCloseExportDialog,
      getItemClass,
      isLastItem,
      onCancelAction,
      applyFilters,
      applySearch,
      clearFilters,
      handleSelectTestCases,
      handleClick,
      addSelectedCases,
      removeSelectedCases,
      quickCreate,
      getCaseHistoryData,
      deleteTestCase,
      handleBulkRemove,
      confirmBulkRemove,
      handleAddToTestRuns,
      updateSelectedCases,
      updateTag,
      handleRowEdit,
      initializeDraggable,
      handleBreadcrumbClick,
      getPriorityColor,
      getPriorityName,
      fetchTags,
      reloadCases,
      addGlobalDragListeners,
      handleCloseCreateTestRunDialog,
      handleCreateTestRunDialogue,
    } = useTestCasesList(props, context);
    addGlobalDragListeners();
    // Add ref for selection in progress
    const selectionInProgress = ref(false);
    const selectionProgress = ref(0);
    
    // Local state for pagination controls
    const localCurrentPage = ref(props.currentPage || 1);
    const localItemsPerPage = ref(props.itemsPerPage || 10);
    const isTruncated = ref(false);
    
    // New sidebar functionality
    const showDuplicateSidebar = ref(false);
    const selectedCasesForDuplicate = ref([]);

    // Watch for prop changes and sync local state
    watch(() => props.currentPage, (val) => { localCurrentPage.value = val; });
    watch(() => props.itemsPerPage, (val) => { localItemsPerPage.value = val; });

    // --- Fix: Watch for caseItems prop changes and update localCaseItems ---
    watch(() => props.caseItems, (newVal) => {
      localCaseItems.value = Array.isArray(newVal) ? [...newVal] : [];
    }, { immediate: true });
    // --- End fix ---

    // When user changes page or items per page
    function onPaginationChange(obj) {
      // obj: { page, itemsPerPage }
      props.updatePagination(obj);
    }

    const updateFilters = (updatedFilters) => {
      applyFilters(updatedFilters);
    };
    
    const handleClearFilters = () => {
     clearFilters();
    };

    // New sidebar methods
    const handleDuplicateToProjectSidebar = (caseItem) => {
      selectedCasesForDuplicate.value = [caseItem];
      showDuplicateSidebar.value = true;
    };

    const handleBulkDuplicateToProjectSidebar = () => {
      selectedCasesForDuplicate.value = [...selectedRows.value];
      showDuplicateSidebar.value = true;
    };

    const handleDuplicateSuccess = () => {
      // Clear selection after successful duplication
      selectedRows.value = [];
      reloadCases();
    };

    const handleCloseDuplicateSidebar = (value) => {
      showDuplicateSidebar.value = value;
      if (!value) {
        selectedCasesForDuplicate.value = [];
      }
    };

    const debouncedApplySearch = debounce(applySearch, 500);
    
    const onSearchInput = (value) => {
      debouncedApplySearch(value);
    };

    const onSearchClear = () => {
      debouncedApplySearch.cancel(); 
    applySearch('');
    };

    const checkTruncate = (uid, columnName) => {
      nextTick(() => {
        if (context.refs && context.refs[`${columnName}_${uid}`]) {
          const el = context.refs[`${columnName}_${uid}`];
          isTruncated.value = el?.scrollWidth > el?.clientWidth;
        }
      });
    };
    
    const hasActiveFilters = computed(() => {
      return (
        (filters.value.priorities && filters.value.priorities.length > 0) ||
        (filters.value.tagObjects && filters.value.tagObjects.length > 0) ||
        (filters.value.tags && filters.value.tags.length > 0)
      );
    });

    const handleSelectAll = (selected) => {
      // Reset the progress indicator state
      selectionInProgress.value = false;
      selectionProgress.value = 0;
      // For deselection, we can simply clear the array immediately - this is fast
      if (!selected) {
        selectedRows.value = [];
        handleSelectTestCases(selectedRows.value);
        return;
      }
      // For selection of all items, we need to preserve existing selections from other pages
      const allData = displayTableData.value;
      const totalItems = allData.length;
      // Get existing selections that are not on the current page
      const existingSelections = selectedRows.value.filter(item =>
        !allData.some(currentPageItem => currentPageItem.testCaseRef === item.testCaseRef)
      );
      // If we have a small dataset, just do it synchronously
      if (totalItems < 1000) {
        // Combine existing selections with current page selections, avoiding duplicates
        const newSelections = [...existingSelections, ...allData];
        selectedRows.value = newSelections;
        handleSelectTestCases(selectedRows.value);
        return;
      }
      // Show selection progress indicator
      selectionInProgress.value = true;
      selectionProgress.value = 0;
      // For large datasets, process in chunks to avoid UI freezing
      const chunkSize = 1000;
      const totalChunks = Math.ceil(totalItems / chunkSize);
      let currentChunk = 0;
      // Start with existing selections
      selectedRows.value = [...existingSelections];
      // Process chunks using setTimeout to yield to the main thread
      function processNextChunk() {
        if (currentChunk >= totalChunks) {
          // All chunks processed, call the handler
          handleSelectTestCases(selectedRows.value);
          selectionInProgress.value = false;
          return;
        }
        const startIdx = currentChunk * chunkSize;
        const endIdx = Math.min(startIdx + chunkSize, totalItems);
        // Process this chunk
        const chunkData = allData.slice(startIdx, endIdx);
        // Append to existing selections, avoiding duplicates
        const chunkToAdd = chunkData.filter(item =>
          !selectedRows.value.some(sel => sel.testCaseRef === item.testCaseRef)
        );
        selectedRows.value = [...selectedRows.value, ...chunkToAdd];
        // Update progress
        currentChunk++;
        selectionProgress.value = Math.round((currentChunk / totalChunks) * 100);
        // Yield to the main thread before processing the next chunk
        setTimeout(processNextChunk, 0);
      }
      // Start processing
      processNextChunk();
    };

    return {
      // Data refs
      isBlankTable,
      isOpenAddToTestRunsDialog,
      isOpenEditDialog,
      isOpenExportDialog,
      isOpenConfirmDialog,
      headers,
      isOpenConfirmBulkDeleteDialog,
      isSelectedData,
      isAddedTable,
      openFilterDrawer,
      toggleSelect,
      testName,
      testTemplate,
      tableSelectedCases,
      tableItems,
      menuOpen,
      searchTerm,
      filters,
      actionBtnShow,
      selectedData,
      states,
      tags,
      testTemplates,
      localCaseItems,
      itemToDelete,
      statuses,
      priorities,
      skeletonLoaderState,
      tableFilterData,
      isOpenCreateTestRunDialog,
      // Computed properties
      activeRow,
      selectedRows,
      isSelected,
      selectedItemsTitles,
      filteredHeaders,
      displayTableData,
      isProjectArchived,
      hasActiveFilters,
      shouldHaveOverflow,
      tableContainerClass,
      tableClass,
      
      // Methods
      handleCloseConfirmBulkDeleteDialog,
      handleCloseConfirmDialog,
      handleConfirm,
      handleCloseAddToTestRunsDialog,
      handleEditClick,
      handleCloseEditDialog,
      handleExportClick,
      handleCloseExportDialog,
      getItemClass,
      isLastItem,
      onCancelAction,
      applyFilters,
      updateFilters,
      clearFilters,
      handleSelectTestCases,
      handleClick,
      addSelectedCases,
      removeSelectedCases,
      quickCreate,
      getCaseHistoryData,
      deleteTestCase,
      handleBulkRemove,
      confirmBulkRemove,
      handleAddToTestRuns,
      updateSelectedCases,
      updateTag,
      handleRowEdit,
      initializeDraggable,
      handleBreadcrumbClick,
      getPriorityColor,
      getPriorityName,
      handleSelectAll,
      selectionInProgress,
      selectionProgress,
      fetchTags,
      reloadCases,
      localCurrentPage,
      localItemsPerPage,
      onPaginationChange,
      handleCloseCreateTestRunDialog,
      isTruncated,
      checkTruncate,
      handleCreateTestRunDialogue,
      onSearchInput,
      onSearchClear,
      handleClearFilters,
      updateTableOptions,
      // Duplicate to project functionality
      showDuplicateSidebar,
      selectedCasesForDuplicate,
      handleDuplicateToProjectSidebar,
      handleBulkDuplicateToProjectSidebar,
      handleDuplicateSuccess,
      handleCloseDuplicateSidebar,
    };

    function updateTableOptions(options) {
      // Handle v-data-table sorting and pagination events and forward to parent
      context.emit('update:options', {
        ...options,
        page: localCurrentPage.value
      });
    }
  },
};
</script>


<style>
 .v-snack__wrapper, .v-snack__content{
  background: white !important;
  color: black !important;
  border-radius: 8px !important;
  box-shadow: 0px -5px 6px -2px #10182808 !important;

}
 .v-snack__wrapper{
  border: 1px solid var(--Colors-Border-border-primary, #D0D5DD) !important;
 }
</style>

<style scoped>

.color-red {
  color: #f2284e !important;
}

.f-color-red {
  color: #f2284e !important;
}

.round-8 {
  border-radius: 8px;
}

.round-6 {
  border-radius: 6px;
}

.h-40 {
  height: 40px !important;
}

.btn-selector {
  position: relative;
}

.modal-main-area {
  height: 100%;
  padding: 32px 32px 32px 32px;
}

.dialog-title {
  font-weight: 900 !important;
}

.filter-dialog {
  padding-top: 15px;
}

.dialog-action {
  width: 90%;
  display: flex;
  position: absolute;
  bottom: 25px;
}

.btn-selector .selector-wrapper {
  position: relative;
}

.selector-style {
  position: absolute;
  right: 0;
  left: unset;
  top: 30px;
  min-width: 240px;
}

.modal-btn {
  width: 45%;
}

.f-color-white {
  color: white !important;
}

.text-red {
  color: #ef5350;
}

.text-green {
  color: #66bb6a;
}

.text-yellow {
  color: #ffa726;
}

.align-start {
  align-items: start !important;
  font-family: Inter !important;
}
.gap-4{
  gap: 8px !important;
}

.search-box-style {
  padding-top: 0;
  border-radius: 8px;
}

.search-bar-style {
  display: flex;
  padding-bottom: 0;
  justify-content: space-between;
}

.setting-btn-style {
  display: flex;
  justify-content: flex-end;
}

.setting-btn {
  position: absolute;
  right: 10px;
  width: 40px !important;
  min-width: 40px !important;
}

.breadcrumb-container {
  padding: 0;
}

.breadcrumb-container ul {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 15px;
}

.create-btn {
  display: flex;
  justify-content: space-between;
  padding-top: 0;
  padding-bottom: 0;
}

.create-btn button {
  color: white !important;
  margin-top: 10px;
}

.bottom-input-style {
  margin-top: 0;
  border-radius: 5px;
  margin-right: 10px;
}

.bottom-input-style .v-text-field__slot {
  padding-left: 10px;
}

.bottom-input-style .v-select__selections {
  padding-left: 10px;
}

.data-table-style {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
}

.data-table-style tbody tr {
  height: 50px !important;
  min-height: 50px;
  cursor: pointer;
}

.v-input__prepend-inner {
  padding-left: 10px;
}

.v-list-item__content {
  text-align: start;
}

.v-breadcrumbs__item .normal-font-color {
  color: rgb(93, 101, 121) !important;
  color: red;
}

.search-field .v-input__slot {
  display: flex;
  align-items: center !important;
}

.search-field .v-input__prepend-inner {
  align-self: center;
  margin-top: 0 !important;
  padding-left: 0px;
  padding-right: 8px !important;
}

.text-field .v-input__slot {
  background-color: #f9f9fb !important;
}

.btn-restore {
  width: 100%;
  font-family: Inter;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  text-align: left;
  cursor: pointer;
}

.menu-header {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
}

.absolute {
  position: absolute;
}

.bottom-right {
  display: flex;
  justify-content: right;
  right: 24px;
  bottom: 16px;
}

.f-color-blue {
  color: #0c2ff3;
}

.action-btn .v-list-item__title {
  display: flex;
  justify-content: flex-start;
}

.h-100 {
  height: 100%;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.block {
  display: block;
}

h2.blank-title {
  text-align: center;
}

p.blank-description {
  max-width: 500px;
  text-align: center;
}

.none {
  display: none;
}

.custom-attribute {
  white-space: nowrap;
}
.v-data-table .v-data-table__wrapper tbody tr {
  height: auto !important;
}

.v-data-table .v-data-table__wrapper tbody tr td {
  height: auto !important;
  padding-top: 0;
}
.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}

.v-data-table tbody tr.dragging {
  opacity: 0.5;
  cursor: move;
}

.v-data-table tbody tr {
  cursor: move;
}

.hover-underline:hover {
  text-decoration: underline;
}



</style>

<style>
#case-table > div.v-data-table__wrapper > table > tbody > tr.v-data-table__selected{
background-color: #E5E7EB !important;
}

#case-table > div.v-data-table__wrapper > table > tbody > tr.v-data-table__selected * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}
</style>
