<template>
  <v-dialog
    v-model="showDialog"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
  >
    <v-card class="d-flex flex-column justify-space-between overflow-hidden">
      <v-card-text class="black--text pt-4 pb-2">
        <div class="d-flex align-center justify-space-between">
          <h2 class="black--text">
            {{ isEditMode ? $t('templatesPage.editTemplate') : $t('templatesPage.createTemplate') }}
          </h2>
          <v-btn
            icon
            @click="showDialog = false"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <v-form
          ref="form"
          v-model="validForm"
          lazy-validation
          class="d-flex flex-column mt-4"
        >
          <div class="d-flex flex-column" v-if="!isEditMode">
            <v-radio-group
              v-model="customTemplate.entityType"
              hide-details
            >
              <v-radio
                v-for="(entityType, index) in entityTypes"
                :key="index"
                class="mb-4"
                :value="entityType"
              >
                <template #label>
                  <span class="fs-14px text-theme-label">
                    {{ entityTypeLabels[entityType] }}
                  </span>
                </template>
              </v-radio>
            </v-radio-group>
          </div>

          <div>
            <div class="text-left mb-1">
              <span class="font-weight-medium">
                {{ $t('storage.name') }} <strong class="danger--text">*</strong>
              </span>
            </div>
            <v-text-field
              v-model="customTemplate.name"
              type="text"
              class="rounded-lg field-theme"
              background-color="#F9F9FB"
              dense
              height="40px"
              :placeholder="$t('name')"
              :rules="requiredRules"
            />
          </div>

          <div class="d-flex align-center justify-space-between">
            <span class="font-weight-medium">{{ $t('templatesPage.assign_as_default') }}</span>
            <v-switch
              v-model="customTemplate.isDefault"
              inset
              :ripple="false"
              color="primary"
              hide-details
              class="custom-switch ma-0 pa-0"
            />
          </div>
          <span
            v-if="customTemplate.isDefault && hasTemplateDefaultData"
            class="text-theme-danger"
          >{{ $t('templatesPage.defaultTemplateDescription', { name: templateDefaultData?.name }) }}</span>

          <v-divider class="my-6 divider-theme divider-theme-width-2px" />
          <template v-if="isTestFormat">

          <div class="d-flex flex-flow-wrap align-center gap-2">
            <v-chip
              v-for="(value, key) in testFormatGroupNames"
              :key="key"
              :ripple="false"
              class="fs-14px radius-6px text-capitalize height-28px user-select-none position-relative chip-with-actions"
              :class="{
                'blue--text': testFormatSelectedGroup === key,
                'text-theme-secondary': testFormatSelectedGroup !== key,
                'fw-semibold': testFormatSelectedGroup === key,
                'font-weight-medium': testFormatSelectedGroup !== key
              }"
              :color="testFormatSelectedGroup === key ? '#e6ecff' : '#f9fafb'"
              label
              @click="onChangeTestFormatGroup(key)"
            >
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ value }}
                  </span>
                </template>
                <span>{{ testFormatGroupTooltips[key] }}</span>
              </v-tooltip>
            </v-chip>
          </div>
          
          <CustomFieldsManager
            ref="customFieldsManager"
            v-model="currentGroupFields"
            :custom-fields="testCustomFields"
          />
            
          </template>
          <template v-else>
            <TemplateRulesManager
              v-model="resultRules"
              :custom-fields-result="resultCustomFields"
            />
            <div class="d-flex justify-end">
              <v-menu
                offset-y
                left
                :nudge-bottom="4"
              >
                <template #activator="{ attrs, on }">
                  <v-btn
                    v-bind="attrs"
                    color="primary"
                    height="40"
                    depressed
                    width="137px"
                    class="text-capitalize white--text btn-theme rounded-lg"
                    v-on="on"
                  >
                    <div class="d-flex align-center gap-2">
                      <span>
                        {{ $t('templatesPage.add_field') }}
                      </span>
                      <v-icon
                        color="white"
                        size="20"
                      >
                        mdi-plus
                      </v-icon>
                    </div>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    :key="1"
                    @click="onAddExistingField"
                  >
                    {{ $t('templatesPage.add_exist_fields') }}
                  </v-list-item>
                  <v-list-item
                    :key="2"
                    @click="onAddCustomField"
                  >
                    {{ $t('templatesPage.add_new_custom_field') }}
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <div>
              <div
                v-for="(field, index) in resultFields"
                :key="index"
                class="px-0 w-full"
              >
                <CustomFieldForm
                  ref="customFieldForms"
                  :custom-fields="resultCustomFields"
                  :field-index="index"
                  :field-item="field"
                  @add-option="onAddOption"
                  @remove-option="onRemoveOption"
                  @remove-field="onRemoveField"
                />
              </div>
            </div>

          </template>
        </v-form>
      </v-card-text>
    </v-card>
    <div class="actions-container d-flex justify-space-between">
      <v-btn
        width="204.5px"
        color="#F2F4F7"
        class="text-capitalize btn-theme"
        :depressed="true"
        full-width
        elevation="0"
        height="40"
        @click="onCancel()"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-btn
        width="204.5px"
        class="btn-theme"
        height="40"
        color="primary"
        :depressed="true"
        full-width
        elevation="0"
        :loading="localCreateBtnLoading"
        @click="onCreate()"
      >
        {{ isEditMode ? $t('save') : $t('create') }}
      </v-btn>
    </div>
  </v-dialog>
</template>

<script>
import { uuid } from 'vue-uuid';
import makeCustomFieldService from '@/services/api/customField';
import { showErrorToast } from '@/utils/toast';
import { entityTypes, entityTypeLabels, entityTypeNames, testFormatGroupNames } from '@/constants/templates';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import TemplateRulesManager from './TemplateRulesManager.vue';
import CustomFieldsManager from './CustomFieldsManager.vue';

let customFieldService;

export default {
  name: 'CreateUpdateCustomField',

  components: {
    TemplateRulesManager,
    CustomFieldsManager,
  },
  mixins: [colorPreferencesMixin],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    defaultFilteredTemplate: {
      type: Object
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
    entityType: {
      type: String,
    },
  },
  data() {
    return {
      requiredRules: [(value) => !!value || this.$t('error.requiredField')],
      validForm: false,
      fields: {
        generalInfo: [],
        details: [],
      },
      resultRules: [],
      resultFields: [],
      customTemplate: {
        uid: '',
        name: '',
        templateFields: [],
        isDefault: false,
        entityType: this.entityType || '',
      },
      customFields: [],

      testFormatGroup: {
        generalInfo: [],
        details: [],
      },

      testFormatSelectedGroup: 'generalInfo',
      testFormatGroupTooltips: {
        generalInfo: this.$t('templatesPage.generalInfoTooltip'),
        details: this.$t('templatesPage.detailsTooltip')
      },

      entityTypes: entityTypes,
      entityTypeLabels: entityTypeLabels,
      entityTypeNames: entityTypeNames,
      testFormatGroupNames: testFormatGroupNames,
    };
  },
  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },
    localCreateBtnLoading: {
      get() {
        return this.loading;
      },
      set(value) {
        this.$emit('update:loading', value);
      }
    },
    defaultFilteredTemplateByEntityType() {
      return this.defaultFilteredTemplate[this.customTemplate.entityType] ? 
        this.defaultFilteredTemplate[this.customTemplate.entityType] :
        [];
    },
    templateDefaultData() {
      return this.defaultFilteredTemplateByEntityType.find((template) => template.isDefault) || {};
    },
    hasTemplateDefaultData() {
      return Object.keys(this.templateDefaultData).length > 0;
    },
    testCustomFields() {
      return this.customFields.filter(field => field.entityTypes.includes(this.customTemplate.entityType));
    },
    resultCustomFields() {
      return this.customFields.filter(field => field.entityTypes.includes(this.customTemplate.entityType));
    },
    isEditMode() {
      return !!this.customTemplate.uid;
    },
    currentGroupFields: {
      get() {
        return this.fields[this.testFormatSelectedGroup] || [];
      },
      set(value) {
        this.$set(this.fields, this.testFormatSelectedGroup, value);
      }
    },
    allFields() {
      return [
        ...this.fields.generalInfo,
        ...this.fields.details
      ];
    },
    isTestFormat() {
      return this.customTemplate.entityType.toLowerCase() === this.entityTypeNames.testCase.toLowerCase();
    },
    rules() {
      return Array.isArray(this.resultRules) ? this.resultRules : [];
    },
    isRulesNotEmpty() {
      return this.rules.length > 0;
    },
    currentRule() {
      return this.rules[this.activeRuleIndex] || {};
    },
    statusUsageMap() {
      const usageMap = new Map();
      this.resultRules.forEach((rule, ruleIndex) => {
        rule.appliesToStatus?.forEach(statusId => {
          if (!usageMap.has(statusId)) {
            usageMap.set(statusId, []);
          }
          usageMap.get(statusId).push(ruleIndex);
        });
      });
      return usageMap;
    },
  },

  watch: {
    showDialog(value) {
      if (!value) {
        return;
      }

      this.customTemplate = {
        uid: this.data.uid || '',
        name: this.data.name || '',
        templateFields: this.data.customFields?.templateFields || [],
        templateFieldsResult: this.data.customFields?.templateFieldsResult || [],
        isDefault: this.data.isDefault || false,
        entityType: this.entityType || '',
      };

      const existingFields = this.data.customFields?.templateFields || [];
      this.resultFields = this.data?.customFields?.templateFields || [];
       if (Array.isArray(this.data?.rules) && this.data.rules.length > 0) {
        this.resultRules = this.data.rules;
      }

      this.fields = {
        generalInfo: existingFields.filter(field => field.group === 'generalInfo'),
        details: existingFields.filter(field => field.group === 'details'),
      };

      if (this.fields.generalInfo.length === 0 && this.fields.details.length === 0 && existingFields.length > 0) {
        this.fields.generalInfo = existingFields.map(field => ({
          ...field,
          group: 'generalInfo'
        }));
      }
      this.testFormatSelectedGroup = 'generalInfo';

      if(!this.isEditMode) {
        this.resultRules = [];
        this.resultFields = [];
      }

    },
  },
  created() {
    customFieldService = makeCustomFieldService(this.$api);
  },
  mounted() {
    this.init(this.$route.params.handle);
  },
  methods: {
    async getCustomFields(handle) {
      try {
        const searchParams = new URLSearchParams();
        this.entityTypes.forEach(entityType => {
          searchParams.append('entityTypes', entityType);
        });
        const response = await customFieldService.getCustomFields(handle, this.$route.params.key, searchParams.toString());
        const result = response?.data || [];
        this.customFields = result;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'Custom Fields' }, error?.response?.data);
      }
    },
    async init(handle) {
      try {

        await this.getCustomFields(handle);

      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'custom fields' }, error?.response?.data);
      } 
    },
    onChangeTestFormatGroup(value) {
      this.testFormatSelectedGroup = value;
    },
    handleAddCustomFieldClick() {
      const newItem = {
        id: uuid.v4(),
        name: '',
        dataType: 'text',
        defaultValue: '',
        options: [],
        required: false,
        default_date: new Date().toISOString().substr(0, 10),
      };

      this.fields.push(newItem);
    },
    handleAddExistingFieldClick() {
      const newItem = {
        required: false,
        name: '',
        dataType: '',
        importFrom: 'existingField',
        defaultValue: [],
        default_date: new Date().toISOString().substr(0, 10),
      };

      this.fields.push(newItem);
    },

    async onCreate() {
      const isValidForm = this.$refs.form.validate();
      let customFieldsValid = true;
      let rulesValid = true;

      if (this.isTestFormat) {
        // Validate CustomFieldsManager for test format
        if (this.$refs.customFieldsManager) {
          customFieldsValid = await this.$refs.customFieldsManager.validate();
        }
      } else {
        // Validate TemplateRulesManager for result format
        if (this.$refs.templateRulesManager) {
          rulesValid = await this.$refs.templateRulesManager.validate();
        }
        
        // Validate individual CustomFieldForm components for result format
        if (this.$refs.customFieldForms && this.$refs.customFieldForms.length > 0) {
          for (let form of this.$refs.customFieldForms) {
            const formValid = await form.validate();
            if (!formValid) {
              customFieldsValid = false;
              break;
            }
          }
        }
      }
      
      if (customFieldsValid && rulesValid && isValidForm) {
        if (this.isTestFormat) {
          const fieldsWithGroup = this.allFields.map(field => ({
            ...field,
            group: field.group || this.getFieldGroup(field),
            templateType: field.group || this.getFieldGroup(field)
          }));
          
          this.customTemplate.templateFields = fieldsWithGroup;
          this.customTemplate.rules = [];
        } else {
          this.customTemplate.rules = this.resultRules;
          this.customTemplate.templateFields = this.resultFields;
        }

        this.$emit(this.isEditMode ? 'update-template' : 'create-template', this.customTemplate);
      }
    },
    getFieldGroup(field) {
      // Helper method to determine which group a field belongs to
      if (this.fields.generalInfo.includes(field)) {
        return 'generalInfo';
      } else if (this.fields.details.includes(field)) {
        return 'details';
      }
      return 'generalInfo'; // default group
    },
    onCancel() {
      this.$emit('close-dialog');
    },
    onAddExistingField() {
      const newItem = {
        required: false,
        name: '',
        dataType: '',
        importFrom: 'existingField',
        defaultValue: [],
        default_date: new Date().toISOString().substr(0, 10),
      };
      this.resultFields.push(newItem);
    },

    onAddCustomField() {
      const newItem = {
        id: uuid.v4(),
        name: '',
        dataType: 'text',
        defaultValue: '',
        options: [],
        required: false,
        default_date: new Date().toISOString().substr(0, 10),
      };
      this.resultFields.push(newItem);
    },

    onAddOption(field) {
      const index = this.resultFields.indexOf(field);
      const options = [...field.options, ''];
      
      const newField = {
        ...field,
        options,
      };

      this.resultFields.splice(index, 1, newField);
    },

    onRemoveOption(field, removingIndex) {
      const index = this.resultFields.indexOf(field);
      const options = [...field.options];
      options.splice(removingIndex, 1);
      
      const newField = {
        ...field,
        options,
      };

      this.resultFields.splice(index, 1, newField);
    },

    onRemoveField(selectedItem) {
      this.resultFields = this.resultFields.filter((item) => item !== selectedItem);
    },
  },
};
</script>

<style lang="scss" scoped>
.font-16 {
  font-size: 16px;
}

.radius-6px.v-chip--label {
  border-radius: 6px !important;
}

.bg-color-grey {
  background-color: #f9fafb;
}

.custom-switch {
  ::v-deep .v-input--switch__thumb {
    width: 24px;
    height: 24px;
    top: 0;
    right: 2px;
  }

  ::v-deep .primary--text {
    background-color: #ffffff !important;
    /* Custom track color when switch is on */
  }

  ::v-deep .primary--text.v-input--switch__track {
    background-color: #0000ff !important;
    /* Custom thumb color */
    opacity: 1;
  }
}

.bg-f9f9fb {
  background-color: #f9fafb;
}

.swal-back {
  width: 440px;
}

.height-28px {
  height: 28px;
}

.inline-edit-field {
  max-width: 100px;
}

.edit-actions {
  .hover-icon {
    cursor: pointer;
    transition: transform 0.1s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.rule-chip-container {
  display: inline-block;

  .chip-with-actions {
    width: 100%;
    min-width: fit-content;

    .chip-content {
      min-height: 28px;
    }

    .action-icons-container {
      width: 44px;
      justify-content: flex-end;
      flex-shrink: 0;
    }
  }
}
</style>
