<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      v-model="selectedItems"
      hide-default-footer
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="data-table-style table-fixed mt-6"
      show-select
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      disable-pagination
      @click:row="handleClick"
      @input="handleSeleted"
    >
      <template #[`header.data-table-select`]="{ props, on }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            indeterminate-icon="icon-indeterminate"
            :input-value="props.value"
            :indeterminate="props.indeterminate"
            @change="on.input"
          />
        </div>
      </template>

      <template #[`item.data-table-select`]="{ isSelected, select }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            :input-value="isSelected"
            @change="select"
            @click.stop
          />
        </div>
      </template>

      <template #[`headers.name`]="{ header }">
        <span class="header_text">{{ header.text }}</span>
      </template>
      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div 
              :ref="'sharedStepName_' + item.uid"
              class="custom-attribute text-truncate font-weight-bold cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'sharedStepName')"
            >
              {{ item.name }}
            </div>
          </template>
          <span class="cursor-pointer">{{ item.name }}</span>
        </v-tooltip>
      </template>
      <template #[`item.steps_number`]="{ item }">
        <span class="custom-attribute">{{ item.steps.length }} &nbsp; steps</span>
      </template>
      <template #[`item.referencedBy`]="{ item }">
        <span class="custom-attribute"> {{ item.referencedBy }} test cases</span>
      </template>

      <template #[`item.actions`]="{ item }">
        <div class="d-flex flex-row justify-end">
          <v-menu
            left
            offset-y
            class="font-inter"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left font-inter"
            >
              <v-tooltip
                bottom
                :disabled="writeStep"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      v-if="item.archivedAt == null"
                      :disabled="!writeStep"
                      :class="{ 'pointer d-flex align-center': true, 'disabled-action': isProjectArchived }"
                    >
                      <EditIcon />
                      <v-list-item-content
                        class="ml-2"
                        @click="!isProjectArchived && $emit('edit', item)"
                      >
                        {{ $t('edit') }}
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>
                  {{
                    $t('sharedStepPage.noPermissionToDo', { action: $t('edit').toLowerCase() })
                  }}
                </span>
              </v-tooltip>
              <v-tooltip 
                bottom 
                :disabled="writeStep"
              >
                <template #activator="{ on, attrs }">
                  <div 
                    v-bind="attrs" 
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!writeStep"
                      :class="{ 'pointer': true, 'disabled-action': isProjectArchived }"
                      @click="!isProjectArchived && $emit(item.archivedAt == null ? 'archive' : 'unarchive', item)"
                    >
                      <ArchiveIcon v-if="item.archivedAt == null" />
                      <UnArchiveIcon v-else />
                      <v-list-item-content class="ml-2">
                        {{
                          item.archivedAt == null ? $t('archive') : $t('unarchive')
                        }}
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>
                  {{
                    $t('sharedStepPage.noPermissionToDo', { action: $t('archive').toLowerCase() })
                  }}
                </span>
              </v-tooltip>
              <v-tooltip 
                bottom 
                :disabled="deleteStep"
              >
                <template #activator="{ on, attrs }">
                  <div 
                    v-bind="attrs" 
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!deleteStep"
                      :class="{ 'pointer': true, 'disabled-action': isProjectArchived }"
                      @click="!isProjectArchived && $emit('delete', item)"
                    >
                      <DeleteIcon />
                      <v-list-item-content class="ml-2 error--text">
                        {{ $t('delete') }}
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>
                  {{
                    $t('sharedStepPage.noPermissionToDo', { action: $t('delete').toLowerCase() })
                  }}
                </span>
              </v-tooltip>
            </v-list>
          </v-menu>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <SharedStepSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex';

import EditIcon from '@/assets/svg/edit.svg';
import ArchiveIcon from '@/assets/svg/archived.svg';
import UnArchiveIcon from '@/assets/svg/unarchive.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js';
import SharedStepSkeleton from '@/components/Skeletons/Admin/SharedStep/SharedStepSkeleton.vue';


const { mapState } = createNamespacedHelpers('user');

export default {
  
  components: {
    EditIcon,
    ArchiveIcon,
    DeleteIcon,
    UnArchiveIcon,
    SharedStepSkeleton,
  },
  mixins: [projectStatus, handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    tab: String,
    writeStep: {
      type: Boolean,
      default: false
    },
    deleteStep: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      tags: this.items,
      selectedItems: [],
      isTruncated: false,
    };
  },
  computed: {
    ...mapState(['currentAccount']),

    isAbleToManage() {
      return this.currentAccount.roleName === 'owner' || this.currentAccount.roleName === 'admin';
    },
  },
  watch: {
    tab() {
      this.selectedItems = [];
    }
  },
  methods: {
    handleClick(item, index) {
      this.$emit('row-click', item, index?.index);
    },
    handleSeleted() {
      this.$emit('input', this.selectedItems);
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
  
};
</script>
