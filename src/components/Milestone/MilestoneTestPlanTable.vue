<template>
  <div>
    <div class="table-scroll-container">
      <v-data-table
        v-if="!skeletonLoaderState"
        v-model="selectedItems"
        :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
        class="table-fixed data-table-style table-min-width mt-6"
        :headers="headers"
        :items="itemsPerView"
        :item-key="itemKey"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        :server-items-length="totalItems"
        show-select
        hide-default-footer
        :disable-pagination="true"
        @click:row="onRowClick"
        @update:options="updateTableOptions"
      >
        <template #[`header.data-table-select`]="{ props, on }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              :input-value="props.value"
              :indeterminate="props.indeterminate"
              @change="on.input"
            />
          </div>
        </template>

        <template #[`item.data-table-select`]="{ isSelected, select }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              :input-value="isSelected"
              @change="select"
              @click.stop
            />
          </div>
        </template>
        <template #[`item.name`]="{ item }">
          <v-tooltip
            bottom
            left
            max-width="485px"
            :disabled="!isTruncated"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div 
                :ref="'milestoneTestPlanName_' + item.uid"
                class="text-subtitle-2 text-truncate font-weight-bold cursor-pointer"
                v-bind="attrs"
                v-on="on"
                @mouseover="checkTruncate(item.uid, 'milestoneTestPlanName')"
              >
                {{ item.name }}
              </div>
            </template>
            <span>{{ item.name }}</span>
          </v-tooltip>
        </template>
        <template />
        <template #[`item.testruns`]="{ item }">
          <span class="custom-attribute font-weight-regular text-theme-table-text">
            {{ item.runCount || 0 }}
          </span>
        </template>
        <template #[`item.milestones`]="{ item }">
          <span class="">{{ item?.testMilestoneCount }} {{ $t('milestones') }}</span>
        </template>
        <template #[`item.progress`]="{item}">
          <ProgressBar
            :executions="item?.executionsProgress"
            :percentage="item?.percentage"
            :case-count="countTestCases(item.runs)"
          />
        </template>

        <template #[`item.priority`]="{item}">
          <span
            :style="{ color: getPriorityColor(item.customFields?.priority, priorities) }"
            class="text-capitalize fw-semibold"
          >{{ getPriorityName(item.customFields?.priority, priorities) }}</span>
        </template>
        <template #[`item.status`]="{item}">
          <span
            :style="{ color: getStatusColor(item.customFields?.status, statuses) }"
            class="text-capitalize fw-semibold"
          >{{ getStatusName(item.customFields?.status, statuses) }}</span>
        </template>
        <template #[`item.configurations`]="{item}">
          <span>{{ item.customFields?.configurations }}</span>
        </template>
        <template #[`item.creationdate`]="{item}">
          <span class="">{{ dateFormatter.formatCreatedAt(item.createdAt) }}</span>
        </template>
        <template #[`item.tags`]="{item}">
          <div v-if="relationLoadingStates.tag">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else
            bottom
            left
            max-width="485px"
            :disabled="!Array.isArray(item?.tags) || item.tags?.length < 2"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <template>
                  <div class="text-truncate">
                    <template v-if="Array.isArray(item?.tags)">
                      <span v-if="item.tags.length > 0">
                        {{ item.tags?.map(tag => tag.name).join(', ') }}
                      </span>
                      <v-icon v-else>mdi-minus</v-icon>
                    </template>
                    <v-icon v-else>mdi-minus</v-icon>
                  </div>
                </template>
              </span>
            </template>
            <span>
              {{
                Array.isArray(item?.tags)
                  ? item.tags?.map(tag => `${tag.name}`).join(', ')
                  : ''
              }}
            </span>
          </v-tooltip>
        </template>
      </v-data-table>
      <template v-else>
        <RunTableSkeleton class="mt-6" />
      </template>
    </div>
    <!-- Custom Pagination Component - only show for 'all' filter when we have server-side pagination -->
    <Pagination
      v-if="!skeletonLoaderState && totalItems > 0 && tableFilter === 'all'"
      :page="currentPage"
      :items-per-page="itemsPerPage"
      :total-pages="totalPages"
      :total-items="totalItems"
      @update:pagination="onUpdatePagination"
    />
  </div>
</template>
  
<script>
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ProgressBar from '@/components/base/ProgressBar'
import handleLoading from '@/mixins/loader.js';
import RunTableSkeleton from '@/components/Skeletons/TestRuns/RunTableSkeleton.vue';
import Pagination from '@/components/base/Pagination.vue';


export default {
  components: {
    ProgressBar,
    RunTableSkeleton,
    Pagination
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    tableFilter: {
      type: String,
      default: 'all',
    },
    plansData: {
      type: Array,
    },
    filteredHeaders: {
      type: Array,
    },
    filteredItems: {
      type:Array,
    },
    rowClass: {
      type:Function,
    },
    value: {
      type: Array,
    },
    relationsLoading: {
      type: Boolean,
      default: false
    },
    relationLoadingStates: {
      type: Object,
      default: () => ({
        tag: false
      })
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    const dateFormatter = useDateFormatter();
    
    return {
      dateFormatter
    };
  },
  data() {
    return {
      plans: this.plansData,
      itemKey: 'uid',
      isAllPlanChecked: false,
      isSelectedPlanChecked: false,
      statuses: [],
      priorities: [],
      isTruncated: false,
    };
  },
  computed: {
    itemsPerView() {
      if (this.tableFilter === 'all') {
        return this.filteredItems;
      } else {
        return this.selectedItems;
      }
    },
    headers() {
      return this.filteredHeaders.filter(header => header.value !== 'milestones');
    },
    selectedItems:{
      get(){
        return this.value;
      },
      set(selectedItems){
        this.$emit('input', selectedItems)
      }
    },
    routeParamsHandle() {
      return this.$route.params.handle;
    },
    routeParamsKey() {
      return this.$route.params.key;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },
  },
  created() {
    this.priorities = this.getPriorities("testPlan");
    this.statuses = this.getStatuses("testPlan");
  },
  methods: {
    getProgress(progress) {
    const numericProgress = parseFloat(progress);
      return isNaN(numericProgress) ? 0 : numericProgress;
    },
    countTestCases(runs) {
      return Array.isArray(runs) ? runs.reduce((total, run) => total + (Array.isArray(run.testExecutions) ? run.testExecutions.length : 0), 0) : 0;
    },
    onUpdatePagination(options) {
      // Emit pagination change to parent component
      this.$emit('update-pagination', options);
    },
    updateTableOptions(options) {
      // Handle v-data-table sorting events and forward to parent
      const itemsPerPage = options.itemsPerPage === -1 ? 10000 : options.itemsPerPage;
      this.$emit('update-pagination', { ...options, itemsPerPage });
    },
    onRowClick(item) {
      this.$emit('row-click', item);
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>
