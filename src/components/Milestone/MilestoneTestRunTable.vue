<template>
  <div>
    <div class="table-scroll-container">
      <v-data-table
        v-if="!skeletonLoaderState"
        v-model="selectedItems"
        :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
        class="table-fixed data-table-style table-min-width mt-6"
        :headers="filteredHeaders"
        :items="itemsPerView"
        :item-key="itemKey"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        :server-items-length="totalItems"
        show-select
        hide-default-footer
        :disable-pagination="true"
        @update:options="updateTableOptions"
        @click:row="onRowClick"
      >
        <template #[`header.data-table-select`]="{ props, on }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              :input-value="props.value"
              :indeterminate="props.indeterminate"
              @change="on.input"
            />
          </div>
        </template>
        <template #[`item.data-table-select`]="{ isSelected, select }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              :input-value="isSelected"
              @change="select"
              @click.stop
            />
          </div>
        </template>
        <template #[`item.name`]="{ item }">
          <td class="d-flex align-center">
            <v-tooltip
              bottom
              left
              max-width="485px"
              :disabled="!isTruncated"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <div 
                  :ref="'milestoneTestRunName_' + item.uid"
                  class="text-subtitle-2 font-weight-bold text-truncate cursor-pointer"
                  v-bind="attrs"
                  v-on="on"
                  @mouseover="checkTruncate(item.uid, 'milestoneTestRunName')"
                >
                  {{ item.name }}
                </div>
              </template>
              <span>{{ item.name }}</span>
            </v-tooltip>
          </td>
        </template>
        <template #[`item.priority`]="{item}">
          <span
            :style="{ color: getPriorityColor(item?.customFields?.priority, priorities) }"
            class="text-capitalize fw-semibold"
          >{{ getPriorityName(item?.customFields?.priority, priorities) }}</span>
        </template>
        <template #[`item.status`]="{item}">
          <span
            :style="{ color: getStatusColor(item?.customFields?.status, statuses) }"
            class="text-capitalize fw-semibold"
          >{{ getStatusName(item?.customFields?.status, statuses) }}</span>
        </template>
        <template />
        <template #[`item.config`]="{item}">
          <div v-if="relationLoadingStates.config">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <template v-else-if="item?.configs?.length > 0">
            <v-tooltip
              bottom
              left
              max-width="485px"
              :disabled="!Array.isArray(item.configs) || item.configs?.length < 2"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="custom-attribute font-weight-regular text-theme-table-text cursor-pointer"
                  v-bind="attrs"
                  v-on="on"
                >
                  <template>
                    <div class="text-truncate">
                      <span v-if="Array.isArray(item?.configs) && item?.configs?.length > 0">
                        {{ item?.configs.map((config) => `${config.option}`).join(', ') }}
                      </span>
                      <v-icon v-else>mdi-minus</v-icon>
                    </div>
                  </template>
                </span>
              </template>
              <span>
                {{ Array.isArray(item.configs) ? item?.configs.map((config) => `${config.option}`).join(', ') : '' }}
              </span>
            </v-tooltip>
          </template>
          <template v-else>
            <v-icon>mdi-minus</v-icon>
          </template>
        </template>
        <template #[`item.testcases`]="{ item }">
          <span class="custom-attribute font-weight-regular text-theme-table-text">
            {{ item.customFields?.caseCount || 0 }}
          </span>
        </template>
        <template #[`item.milestone`]="{item}">
          <div v-if="relationLoadingStates.milestone">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else
            top
            left
            max-width="485px"
            :disabled="!Array.isArray(item.testMilestones) || item.testMilestones.length < 2"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <template>
                  <div class="text-truncate">
                    <span v-if="Array.isArray(item.testMilestones) && item.testMilestones.length > 0">
                      {{ item.testMilestones.map(milestone => `${milestone.name}`).join(', ') }}
                    </span>
                    <span v-else-if="typeof item.testMilestones === 'string' && item.testMilestones.trim() !== ''">
                      {{ item.testMilestones }}
                    </span>
                    <v-icon v-else>mdi-minus</v-icon>
                  </div>
                </template>
              </span>
            </template>
            <span>
              {{
                Array.isArray(item.testMilestones)
                  ? item.testMilestones.map(milestone => `${milestone.name}`).join(', ')
                  : ''
              }}
            </span>
          </v-tooltip>
        </template>
        <template #[`item.tags`]="{item}">
          <div v-if="relationLoadingStates.tag">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else
            bottom
            left
            max-width="485px"
            :disabled="!Array.isArray(item?.tags) || item?.tags?.length < 2"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <template>
                  <div class="text-truncate">
                    <template v-if="Array.isArray(item?.tags)">
                      <span v-if="item?.tags?.length > 0">
                        {{ item.tags?.map(tag => tag.name).join(', ') }}
                      </span>
                      <v-icon v-else>mdi-minus</v-icon>
                    </template>
                    <v-icon v-else>mdi-minus</v-icon>
                  </div>
                </template>
              </span>
            </template>
            <span>
              {{
                Array.isArray(item?.tags)
                  ? item.tags.map(tag => `${tag.name}`).join(', ')
                  : ''
              }}
            </span>
          </v-tooltip>
        </template>
        <template #[`item.creationdate`]="{ item }">
          <span>{{ dateFormatter.formatCreatedAt(item.createdAt) }}</span>
        </template>
        <template #[`item.duedate`]="{item}">
          <span 
            :class="{ 'red--text': dueDateInfo(item?.customFields?.dueAt).isOverdue }"
          >
            {{ dueDateInfo(item?.customFields?.dueAt).formatted }}
          </span>
        </template>
        <template #[`item.progress`]="{item}">
          <ProgressBar
            :executions="item?.executionsProgress"
            :percentage="item?.percentage"
            :case-count="item?.customFields?.caseCount"
          />
        </template>
      </v-data-table>
      <template v-else>
        <RunTableSkeleton class="mt-6" />
      </template>
    </div>
    <!-- Custom Pagination Component - only show for 'all' filter when we have server-side pagination -->
    <Pagination
      v-if="!skeletonLoaderState && totalItems > 0 && tableFilter === 'all'"
      :page="currentPage"
      :items-per-page="itemsPerPage"
      :total-pages="totalPages"
      :total-items="totalItems"
      @update:pagination="onUpdatePagination"
    />
  </div>
</template>
  
<script>
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ProgressBar from '@/components/base/ProgressBar'
import handleLoading from '@/mixins/loader.js';
import RunTableSkeleton from '@/components/Skeletons/TestRuns/RunTableSkeleton.vue';
import Pagination from '@/components/base/Pagination.vue';


export default {
    components:{
      ProgressBar,
      RunTableSkeleton,
      Pagination
    },
    mixins: [colorPreferencesMixin, handleLoading],
    props: {
        tableFilter: {
            type: String,
            default: 'all',
        },
        runsData: {
            type: Array,
        },
        filteredHeaders: {
            type: Array,
        },
        filteredItems: {
            type:Array,
        },
        rowClass: {
            type:Function,
        },
        value: {
          type: Array
        },
        relationsLoading: {
          type: Boolean,
          default: false
        },
        relationLoadingStates: {
          type: Object,
          default: () => ({
            tag: false,
            milestone: false,
            config: false
          })
        },
        totalItems: {
          type: Number,
          default: 0
        },
        currentPage: {
          type: Number,
          default: 1
        },
        itemsPerPage: {
          type: Number,
          default: 10
        },
        sortBy: {
          type: Array,
          default: () => []
        },
        sortDesc: {
          type: Array,
          default: () => []
        }
    },
    setup() {
      const dateFormatter = useDateFormatter();
      
      // Helper method for due date formatting with overdue detection
      const dueDateInfo = (date) => {
        return dateFormatter.formatDueDate(date);
      };
      
      return {
        dateFormatter,
        dueDateInfo
      };
    },
    data() {
        return {
            runs: this.runsData,
            itemKey: 'uid',
            isAllUserChecked: false,
            isSelectedUserChecked: false,
            statuses: [],
            priorities: [],
            isTruncated: false,
        };
    },
    computed: {
        itemsPerView() {
            if (this.tableFilter === 'all') {
                return this.filteredItems || [];
            } else {
                return this.selectedItems || []
            }
        },
        selectedItems:{
          get(){
            return this.value || [];
          },
          set(selectedItems){
            this.$emit('input', selectedItems)
          }
        },
        isFilterAll(){
          return this.tableFilter === 'all';
        },
        routeParamsHandle() {
          return this.$route.params.handle;
        },
        routeParamsKey() {
          return this.$route.params.key;
        },
        totalPages() {
          return Math.ceil(this.totalItems / this.itemsPerPage);
        },
    },
    created() {
      this.statuses = this.getStatuses("testRun");
      this.priorities = this.getPriorities("testRun");
    },
    methods: {
      safePercentage(value) {
        const percentage = value;
        return (typeof percentage === 'string' && percentage !== 'NaN') ? percentage : '0';
      },
      onUpdatePagination(options) {
        // Emit pagination change to parent component
        this.$emit('update-pagination', options);
      },
      updateTableOptions(options) {
        // Handle v-data-table sorting events and forward to parent
        const itemsPerPage = options.itemsPerPage === -1 ? 10000 : options.itemsPerPage;
        this.$emit('update-pagination', { ...options, itemsPerPage });
      },
      onRowClick(item) {
        this.$emit('row-click', item);
      },
      checkTruncate(uid, columnName) {
        this.$nextTick(() => {
          const el = this.$refs[`${columnName}_${uid}`];
          this.isTruncated = el?.scrollWidth > el?.clientWidth;
        });
      },
    },
};
</script>