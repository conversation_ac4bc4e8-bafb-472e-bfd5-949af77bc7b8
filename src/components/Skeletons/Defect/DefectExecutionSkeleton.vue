<template>
  <div>
    <template v-if="loading">
      <v-skeleton-loader
        width="100px"
        height="20px"
        type="text"
      />
    </template>

    <template v-else-if="isResponseSuccess && linkedExecutions && linkedExecutions.length > 0">
      <div class="d-flex align-center">
        <template v-if="!linkedExecutions || linkedExecutions.length === 0">
          <span class="text-caption">{{ $t('-') }}</span>
        </template>
        <template v-else-if="linkedExecutions.length === 1">
          <v-tooltip
            bottom
            left
            max-width="485px"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div
                class="d-flex align-center cursor-pointer text-truncate"
                v-bind="attrs"
                v-on="on"
                @click.stop="handleExecutionClick(linkedExecutions[0])"
              >
                <span class="text-body-2 text-truncate">{{ linkedExecutions[0].name.length > 15 ?
                  linkedExecutions[0].name.slice(0, 15) + '...' : linkedExecutions[0].name }}</span>
                <v-icon
                  size="20"
                  class="ml-1 flex-shrink-0"
                >
                  mdi-arrow-top-right
                </v-icon>
              </div>
            </template>
            <span>{{ linkedExecutions[0].name }}</span>
          </v-tooltip>
        </template>
        <template v-else>
          <v-menu offset-y>
            <template #activator="{ on: menuOn, attrs: menuAttrs }">
              <v-tooltip
                bottom
                left
                max-width="485px"
                content-class="tooltip-theme"
              >
                <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                  <div
                    class="d-flex align-center cursor-pointer text-truncate"
                    v-bind="{ ...tooltipAttrs, ...menuAttrs }"
                    v-on="{ ...tooltipOn, ...menuOn }"
                  >
                    <span class="text-body-2 text-truncate">{{ linkedExecutions[0].name.length > 15 ?
                      linkedExecutions[0].name.slice(0, 15) + '...' : linkedExecutions[0].name }}</span>
                    <v-icon
                      size="20"
                      class="ml-1 flex-shrink-0"
                    >
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </template>
                <span>{{ linkedExecutions[0].name }}</span>
              </v-tooltip>
            </template>
            <v-list dense>
              <v-list-item
                v-for="execution in linkedExecutions"
                :key="execution.uid"
                dense
                @click.stop="handleExecutionClick(execution)"
              >
                <v-list-item-title class="d-flex align-center justify-space-between text-truncate">
                  <span class="text-truncate">{{ execution.name }}</span>
                  <v-icon
                    size="20"
                    class="flex-shrink-0"
                  >
                    mdi-arrow-top-right
                  </v-icon>
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>
      </div>
    </template>

    <template v-else>
      <v-icon>mdi-minus</v-icon>
    </template>
  </div>
</template>

<script>
export default {
  name: 'DefectExecutionSkeleton',
  props: {
    endpointFunc: { type: Function, required: true },
  },
  data() {
    return {
      linkedExecutions: [],
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fectDefectExecutions();
  },
  methods: {
    async fectDefectExecutions() {
      this.loading = true;
      try {
        const res = await this.endpointFunc();
        this.linkedExecutions = res.data || [];
        this.isResponseSuccess = (res.status >= 200 && res.status < 300) || res.status === 304;
      } catch (err) {
        this.isResponseSuccess = false;
      } finally {
        this.loading = false;
      }
    },
    handleExecutionClick(execution) {
      this.$emit('execution-click', execution);
    },
  },
};
</script>
