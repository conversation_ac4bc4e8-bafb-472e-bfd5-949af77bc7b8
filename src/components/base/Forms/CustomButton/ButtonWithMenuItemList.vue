<!-- filepath: src/components/base/CustomFieldMenu.vue -->
<template>
  <v-menu v-model="menuOpen" bottom right offset-y class="rounded-lg" :close-on-content-click="false"
    :max-width="maxWidth">
    <template #activator="{ on, attrs }">
      <v-btn v-bind="attrs" v-on="on" class="text-capitalize btn-theme" :class="buttonClass" depressed
        :color="buttonColor" :height="buttonHeight" :max-width="buttonMaxWidth" :block="blockButton" :outlined="outlined" :text="textButton">
        <v-icon v-if="showIcon" :size="iconSize" class="mr-2">
          {{ icon }}
        </v-icon>
        {{ buttonText }}
      </v-btn>
    </template>

    <v-list class="pa-0 list-theme">
      <!-- Search Field -->
      <v-list-item v-if="showSearch" class="px-4 py-2" :ripple="false">
        <v-text-field v-model="searchTerm" class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
          :placeholder="searchPlaceholder" height="40" clear-icon="mdi-close-circle" clearable
          background-color="#F9F9FB" :hide-details="true" @input="$emit('search', searchTerm)">
          <template #prepend-inner>
            <SearchIcon />
          </template>
        </v-text-field>
      </v-list-item>

      <!-- Custom Fields List -->
      <template v-if="filteredItems.length">
        <v-list-item v-for="item in filteredItems" :key="item.id || item.uid" :ripple="false" class="px-4"
        :class="{
          'disabled-action': isItemDisabled(item)
        }"
          @click="selectItem(item)">
          <v-list-item-action class="mr-3" v-if="showCheckboxes">
            <v-checkbox :input-value="isItemSelected(item)" class="field-theme mt-0 pt-0" :ripple="false"
              off-icon="icon-checkbox-off" on-icon="icon-checkbox-on" :disabled="isItemDisabled(item)" hide-details
              @click.stop="toggleItem(item)" />
          </v-list-item-action>

          <v-list-item-content>
            <v-list-item-title class="fs-14px">
              {{ getItemText(item) }}
            </v-list-item-title>
            <v-list-item-subtitle v-if="getItemSubtext(item)" class="fs-12px">
              {{ getItemSubtext(item) }}
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
      </template>

      <!-- No Items Found -->
      <v-list-item v-else-if="showSearch && searchTerm">
        <v-list-item-content>
          <div class="text-center py-4">
            {{ noItemsText }}
          </div>
        </v-list-item-content>
      </v-list-item>

      <!-- Add New Custom Field Button -->
      <v-list-item v-if="showAddNew" class="px-4 py-2" @click="addNewField">
        <v-list-item-action v-if="showAddNewIcon">
          <v-icon size="16" color="primary">
            mdi-plus
          </v-icon>
        </v-list-item-action>
        <v-list-item-content>
          <span class="text-theme-primary fs-12px fw-semibold cursor-pointer">
            {{ addNewText }}
          </span>
        </v-list-item-content>
      </v-list-item>

      <!-- Action Buttons -->
      <v-list-item v-if="showActionButtons && (selectedItems.length || !multiple)">
        <div class="d-flex justify-center w-100 px-2 pt-2 pb-4">
          <v-btn class="text-capitalize btn-theme mr-2" depressed height="38px" width="50%" @click="cancel">
            {{ cancelText }}
          </v-btn>
          <v-btn class="text-capitalize btn-theme ml-2" depressed color="primary" height="38px" width="50%"
            :disabled="!canConfirm" @click="confirm">
            {{ confirmText }}
          </v-btn>
        </div>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';

export default {
  name: 'CustomFieldMenu',

  components: {
    SearchIcon
  },

  props: {
    // Button Props
    buttonText: {
      type: String,
      default: 'Add Custom Field'
    },
    buttonColor: {
      type: String,
      default: 'primary'
    },
    buttonMaxWidth: {
      type: [String, Number],
    },
    buttonClass: {
      type: String,
      default: 'fs-14px mb-8'
    },
    buttonHeight: {
      type: [String, Number],
      default: 38
    },
    blockButton: {
      type: Boolean,
      default: true
    },
    outlined: {
      type: Boolean,
      default: false
    },
    textButton: {
      type: Boolean,
      default: false
    },

    // Icon Props
    showIcon: {
      type: Boolean,
      default: true
    },
    icon: {
      type: String,
      default: 'mdi-plus'
    },
    iconSize: {
      type: [String, Number],
      default: 16
    },

    // Menu Props
    maxWidth: {
      type: [String, Number],
    },

    // Search Props
    showSearch: {
      type: Boolean,
      default: true
    },
    searchPlaceholder: {
      type: String,
      default: 'Search by name'
    },

    // Items Props
    items: {
      type: Array,
      default: () => []
    },
    itemText: {
      type: [String, Function],
      default: 'name'
    },
    itemSubtext: {
      type: [String, Function],
      default: null
    },
    itemValue: {
      type: [String, Function],
      default: 'id'
    },
    itemDisabled: {
      type: [String, Function],
      default: null
    },

    // Selection Props
    value: {
      type: [Array, Object, String, Number],
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: true
    },
    showCheckboxes: {
      type: Boolean,
      default: true
    },

    // Add New Props
    showAddNew: {
      type: Boolean,
      default: true
    },
    addNewText: {
      type: String,
      default: 'Add New Custom Field'
    },
    showAddNewIcon: {
      type: Boolean,
      default: true
    },

    // Action Buttons Props
    showActionButtons: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    confirmText: {
      type: String,
      default: 'Add'
    },

    // Text Props
    noItemsText: {
      type: String,
      default: 'No items found'
    },
  },

  data() {
    return {
      menuOpen: false,
      searchTerm: '',
      selectedItems: this.multiple ? (Array.isArray(this.value) ? [...this.value] : []) : this.value
    };
  },

  computed: {
    filteredItems() {
      if (!this.searchTerm) {
        return this.items;
      }
      const searchLower = this.searchTerm.toLowerCase();
      return this.items.filter(item => {
        const text = this.getItemText(item).toLowerCase();
        return text.includes(searchLower);
      });
    },

    canConfirm() {
      if (this.multiple) {
        return this.selectedItems.length > 0;
      }
      return this.selectedItems !== null && this.selectedItems !== undefined;
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.selectedItems = this.multiple ?
          (Array.isArray(newVal) ? [...newVal] : []) :
          newVal;
      },
      immediate: true
    },
    menuOpen(isOpen) {
      if (!isOpen) {
        this.selectedItems = this.multiple ? [] : null;
      }
    }
  },

  methods: {
    getItemText(item) {
      if (typeof this.itemText === 'function') {
        return this.itemText(item);
      }
      return item[this.itemText] || item.toString();
    },

    getItemSubtext(item) {
      if (!this.itemSubtext) return null;
      if (typeof this.itemSubtext === 'function') {
        return this.itemSubtext(item);
      }
      return item[this.itemSubtext];
    },

    getItemValue(item) {
      if (typeof this.itemValue === 'function') {
        return this.itemValue(item);
      }
      return item[this.itemValue] || item;
    },

    isItemDisabled(item) {
      if (!this.itemDisabled) return false;
      if (typeof this.itemDisabled === 'function') {
        return this.itemDisabled(item);
      }
      return item[this.itemDisabled];
    },

    isItemSelected(item) {
      const itemValue = this.getItemValue(item);
      if (this.multiple) {
        return this.selectedItems.some(selected =>
          this.getItemValue(selected) === itemValue
        );
      }
      return this.getItemValue(this.selectedItems) === itemValue;
    },

    selectItem(item) {
      if (this.isItemDisabled(item)) return;

      if (!this.multiple) {
        this.selectedItems = item;
        this.$emit('item-select', item);
        if (!this.showActionButtons) {
          this.confirm();
        }
      } else {
        this.toggleItem(item);
      }
    },

    toggleItem(item) {
      if (this.isItemDisabled(item)) return;

      const itemValue = this.getItemValue(item);
      const index = this.selectedItems.findIndex(selected =>
        this.getItemValue(selected) === itemValue
      );

      if (index > -1) {
        this.selectedItems.splice(index, 1);
      } else {
        this.selectedItems.push(item);
      }

      this.$emit('item-toggle', item, this.selectedItems);
    },

    addNewField() {
      this.$emit('add-new');
      this.closeMenu();
    },

    cancel() {
      this.selectedItems = this.multiple ?
        (Array.isArray(this.value) ? [...this.value] : []) :
        this.value;
      this.closeMenu();
      this.$emit('cancel');
    },

    confirm() {
      this.$emit('input', this.selectedItems);
      this.$emit('confirm', this.selectedItems);
      this.closeMenu();
    },

    closeMenu() {
      this.menuOpen = false;
      this.searchTerm = '';
    }
  }
};
</script>
