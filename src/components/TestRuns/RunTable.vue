<template>
  <div>
    <div
      :class="tableContainerClass"
    >
      <v-data-table
        v-if="hasInitiallyLoaded"
        v-model="selectedRows"
        :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
        :class="tableClass"
        :headers="filteredHeaders"
        :items=" tableFilter === 'all' ? filteredItems : selectedRows"
        :item-key="itemKey"
        :item-class="rowClass"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        :server-items-length="totalItems"
        :fixed-header="true"
        :height="shouldHaveOverflow ? '70vh' : 'auto'"
        show-select
        hide-default-footer
        disable-pagination
        @click:row="handleRowClick"
      >
        <template #[`header.data-table-select`]="{ props, on }">
          <div class="d-flex justify-start align-center">
            <v-checkbox
              id="remember-me-checkbox"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              :input-value="props.value"
              :indeterminate="props.indeterminate"
              @change="on.input"
            />
          </div>
        </template>

        <template #[`item.data-table-select`]="{ isSelected, select }">
          <td @click.stop>
            <div class="d-flex justify-start align-center">
              <v-checkbox
                id="remember-me-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :input-value="isSelected"
                @change="select"
                @click.stop
              />
            </div>
          </td>
        </template>

        <template #[`header.actions`]="{header}">
          <div class="d-none">
            {{ header.text }}
          </div>
        </template>
        <template #[`item.name`]="{item}">
          <v-tooltip
            bottom
            left
            max-width="485px"
            :disabled="item.name.length < 10"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div
                class="custom-attribute text-truncate font-weight-bold cursor-pointer"
                v-bind="attrs"
                v-on="on"
              >
                {{ item.name }}
              </div>
            </template>
            <span class="cursor-pointer">{{ item.name }}</span>
          </v-tooltip>
        </template>
        <template #[`item.priority`]="{item}">
          <span
            :style="{ color: getPriorityColor(item.priority, priorities) }"
            class="font-weight-bold text--lighten-1"
          >{{ getPriorityName(item.priority, priorities) }}</span>
        </template>
        <template #[`item.status`]="{item}">
          <span
            :style="{ color: getStatusColor(item.status, statuses) }"
            class="font-weight-bold text--lighten-1"
          >{{ getStatusName(item.status, statuses) }} </span>
        </template>
        <template #[`item.config`]="{item}">
          <div v-if="!processedRunCache.includes(item.uid) && relationLoadingStates.config">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          
          <template v-else-if="item.configs && item.configs.length > 0">
            <v-tooltip
              bottom
              left
              max-width="485px"
              :disabled="!Array.isArray(item.configs)"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="custom-attribute font-weight-regular text-theme-table-text"
                  v-bind="attrs"
                  v-on="on"
                >
                  <template>
                    <div class="text-truncate">
                      <span v-if="Array.isArray(item?.configs) && item?.configs?.length > 0">
                        {{ item.configs?.map(config => config.option).map(option => option).join(', ') }}
                      </span>
                      <v-icon v-else>mdi-minus</v-icon>
                    </div>
                  </template>
                </span>
              </template>
              <span>
                {{ Array.isArray(item?.configs) ? item.configs?.map(config => config.option).map(option => option).join(', ') : '' }}
              </span>
            </v-tooltip>
          </template>
          <template v-else>
            <v-icon>mdi-minus</v-icon>
          </template>
        </template>
        <template #[`item.milestone`]="{item}">
          <div v-if="!processedRunCache.includes(item.uid) && relationLoadingStates.milestone">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <template v-else>
            <v-tooltip
              v-if="item.testMilestones"
              bottom
              left
              max-width="485px"
              :disabled="!Array.isArray(item?.testMilestones) || !item.testMilestones || item.testMilestones.length < 2"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="custom-attribute font-weight-regular text-theme-table-text"
                  v-bind="attrs"
                  v-on="on"
                >
                  <div class="text-truncate">
                    <span v-if="Array.isArray(item?.testMilestones) && item.testMilestones.length > 0">
                      {{ item.testMilestones.map(milestone => milestone.name).join(', ') }}
                    </span>
                    <v-icon v-else>mdi-minus</v-icon>
                  </div>
                </span>
              </template>
              <span>
                {{ Array.isArray(item?.testMilestones) ? item.testMilestones.map(milestone => milestone.name).join(', ') : '' }}
              </span>
            </v-tooltip>
            <v-skeleton-loader
              v-else-if="processedRunCache.includes(item.uid)"
              type="text"
              height="16"
              class="w-100"
            />
            <v-icon v-else-if="!relationLoadingStates.milestone">
              mdi-minus
            </v-icon>
          </template>
        </template>
        <template #[`item.tags`]="{item}">
          <div v-if="!processedRunCache.includes(item.uid) && relationLoadingStates.tag">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else-if="item.tags"
            bottom
            left
            max-width="485px"
            :disabled="!Array.isArray(item?.tags) || !item.tags || item.tags.length < 2"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <template>
                  <div class="text-truncate">
                    <template v-if="Array.isArray(item?.tags)">
                      <template v-if="item.tags && item.tags.length > 0">
                        {{ item.tags.map(tag => `${tag.name}`).join(', ') }}
                      </template>
                      <template v-else>
                        <v-icon>mdi-minus</v-icon>
                      </template>
                    </template>
                    <template v-else-if="typeof item?.tags === 'string' && item.tags.trim() !== ''">
                      {{ item.tags }}
                    </template>
                    <template v-else>
                      <v-icon>mdi-minus</v-icon>
                    </template>
                  </div>

                </template>
              </span>
            </template>
            <span>
              {{
                Array.isArray(item?.tags)
                  ? item.tags.map(tag => `${tag.name}`).join(', ')
                  : ''
              }}
            </span>
          </v-tooltip>
          <v-skeleton-loader
            v-else-if="processedRunCache.includes(item.uid)"
            type="text"
            height="16"
            class="w-100"
          />
          <v-icon v-else-if="!relationLoadingStates.tag">
            mdi-minus
          </v-icon>
        </template>
        <template #[`item.testcases`]="{item}">
          <span class="text-theme-table-text fs-14px">
            {{ item.customFields.caseCount || 0 }} <span class="text-lowercase">{{ $t('testCases') }}</span>
          </span>
        </template>
        <template #[`item.creationdate`]="{item}">
          <span class="">{{ formatDate(item.createdAt) }}</span>
        </template>
        <template #[`item.duedate`]="{item}">
          <template v-if="item.customFields.dueAt">
            <span class="">{{ formatDate(item.customFields.dueAt) }}</span>
          </template>
          <template v-else>
            <v-icon>mdi-minus</v-icon>
          </template>
        </template>
        <template #[`item.progress`]="{item}">
          <ProgressBar
            :executions="generateExecutionsProgress(item?.customFields?.frequency)"
            :percentage="item?.customFields?.progress"
            :case-count="getObjectCount(item?.customFields?.frequency)"
          />
        </template>
        <template #[`item.configurations`]="{ item }">
          <div class="d-flex flex-row align-center justify-start">
            <v-btn
              v-if="!item.configs || item.configs.length === 0"
              color="primary"
              text
              class="fw-semibold text-capitalize"
              @click="onAddConfiguration(item)"
            >
              <v-icon
                color="primary"
              >
                mdi-plus
              </v-icon> {{ $t('add') }}
            </v-btn>
            <v-menu
              v-else
              bottom
              offset-y
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  class="d-flex align-center"
                  v-on="on"
                >
                  <span>{{ item.configs ? item.configs.map(config => config.option).map(option => option).join(', ') : '' }}</span>
                  <v-icon>mdi-chevron-down</v-icon>
                </div>
              </template>
              <v-list>
                <v-list-item
                  v-for="(config, index) in item.customFields.configs"
                  :key="index"
                >
                  <div class="d-flex align-center">
                    <div
                      v-if="editingIndex === index"
                      class="d-flex align-center"
                    >
                      <v-text-field
                        ref="itemInput"
                        v-model="editedValue"
                        class="new-folder-input mt-0 pt-0 pa-0 editable "
                        type="text"
                        background-color="transparent"
                        hide-details
                        autofocus
                        height="20px"
                        solo
                        flat
                        dense
                        @blur="saveItem(item, index)"
                        @keyup.enter="saveItem(item, index)"
                        @click.stop
                      />
                    </div>
                    <span
                      v-else
                      class="fs-14 text-theme-label"
                    >{{ item.configs && item.configs[index] ? item.configs[index].option : '' }}</span>
                    <v-btn
                      icon
                      plain
                      small
                      class="btn-plain-theme"
                      @click.stop="startEditItem(item, index)"
                    >
                      <PencilIcon />
                    </v-btn>
                    <v-btn
                      icon
                      plain
                      small
                      class="btn-plain-theme"
                      @click.stop="deleteItem(item, index)"
                    >
                      <DeleteIcon16px />
                    </v-btn>
                  </div>
                </v-list-item>
                <v-btn
                  depressed
                  :ripple="false"
                  plain
                  class="f-color-white btn-theme text-capitalize rounded-lg btn-plain-theme"
                  color="primary"
                  height="40px"
                  @click.stop="onAddConfiguration(item)"
                >
                  <v-icon
                    color="primary"
                    class="mr-2"
                  >
                    mdi-plus
                  </v-icon>
                  <span>{{ $t('plans.create.testRuns.addConfiguration') }} </span>
                </v-btn>
              </v-list>
            </v-menu>
          </div>
        </template>
        <template #[`item.actions`]="{ item }">
          <td @click.stop>
            <div class="d-flex flex-row justify-center ml-4">
              <v-menu
                left
                offset-y
              >
                <template #activator="{ on }">
                  <v-btn
                    icon
                    v-on="on"
                  >
                    <v-icon>mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                <v-list
                  dense
                  class="text-left actions-list"
                >
                  <v-tooltip
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          v-if="!isRunProjectArchived"
                          :key="1"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onDuplicateConfig(item)"
                        >
                          <div class="d-flex align-center">
                            <ApplyConfigIcon />
                          </div>
                          <v-list-item-content class="ml-2 text-truncate item-block">
                            {{ $t('testruns.duplicateApplyConfig') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('testruns.duplicateApplyConfig').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="!isRunProjectArchived"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :key="2"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onArchive(item)"
                        >
                          <ArchieveIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('archive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('archive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-else-if="isRunProjectArchived"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :key="3"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onUnArchive(item)"
                        >
                          <UnarchivedIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('unarchive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('unarchive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="!isRunProjectArchived"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :key="4"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onEdit(item)"
                        >
                          <EditIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('edit') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          v-if="!isRunProjectArchived"
                          :key="5"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onMilestone(item)"
                        >
                          <div class="d-flex align-center">
                            <MilestoneIcon />
                          </div>
                          <v-list-item-content class="ml-2">
                            {{ $t('testruns.linkToMilestone') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('testruns.linkToMilestone').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          v-if="!isRunProjectArchived"
                          :key="6"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onPlan(item)"
                        >
                          <div class="d-flex align-center">
                            <CheckedIcon />
                          </div>
                          <v-list-item-content class="ml-2">
                            {{ $t('testruns.linkToTestPlans') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('testruns.linkToTestPlans').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    bottom
                    :disabled="deleteEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :key="7"
                          :disabled="!deleteEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onDelete(item)"
                        >
                          <DeleteIcon />
                          <v-list-item-content class="ml-2 error--text">
                            {{ $t('delete') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                </v-list>
              </v-menu>
            </div>
          </td>
        </template>

        <template #no-data>
          <div class="text-center pa-5">
            <p class="text-subtitle-1">
              {{ $t('noMatchingResults') }}
            </p>
          </div>
        </template>

        <template #[`item.users`]="{ item }">
          <td class="d-flex align-center">
            <v-row>
              <div
                v-for="(pic, imgIndex) in item.images"
                :key="imgIndex"
              >
                <v-avatar
                  class="ml-n2 custom_border"
                  size="30"
                >
                  <img :src="pic">
                </v-avatar>
              </div>
              <v-avatar
                v-if="item.showCount"
                class="font-weight-bold gray-ish--text ml-n2"
                color="#ebecf0"
                size="30"
              >
                +{{ item.count }}
              </v-avatar>
            </v-row>
            <div class="d-flex flex-row justify-center">
              <v-menu
                offset-y
              >
                <template #activator="{ on }">
                  <v-btn
                    icon
                    class="metaball-icon"
                    v-on="on"
                  >
                    <v-icon color="gray-ish">
                      mdi-dots-vertical
                    </v-icon>
                  </v-btn>
                </template>
                <v-list dense>
                  <v-tooltip
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          v-if="!isRunProjectArchived"
                          :disabled="!writeEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onEdit(item)"
                        >
                          <EditIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('edit') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-if="!isRunProjectArchived"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          :disabled="!writeEntity"
                          @click="onArchive(item)"
                        >
                          <ArchieveIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('archive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('archive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    v-else-if="isRunProjectArchived"
                    bottom
                    :disabled="writeEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          :disabled="!writeEntity"
                          @click="onUnArchive(item)"
                        >
                          <UnarchivedIcon />
                          <v-list-item-content class="ml-2">
                            {{ $t('unarchive') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('unarchive').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                  <v-tooltip
                    bottom
                    :disabled="deleteEntity"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item
                          :disabled="!deleteEntity"
                          :class="{
                            pointer: true,
                            'disabled-action': isProjectArchived,
                          }"
                          @click="onDelete(item)"
                        >
                          <DeleteIcon />
                          <v-list-item-content class="ml-2 error--text">
                            {{ $t('delete') }}
                          </v-list-item-content>
                        </v-list-item>
                      </div>
                    </template>
                    <span>
                      {{ $t('testruns.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
                    </span>
                  </v-tooltip>
                </v-list>
              </v-menu>
            </div>
          </td>
        </template>
      </v-data-table>
      <template v-else>
        <RunTableSkeleton class="mt-6" />
      </template>
      <DuplicateAndApplyConfigDialog
        v-if="showConfigurations"
        is-add-config
        :value="duplicateAndApplyConfigDialog"
        :configurations="configurationSorted"
        :button-loading="buttonLoading"
        :button-loading-items="buttonLoadingItems"
        :is-menu-visible="buttonLoading"
        :selected-run="selectedRun"
        @handleDuplicateAndApplyConfig="handleDuplicateAndApplyConfig"
        @close="onCloseDuplicateAndApplyConfigDialog"
        @addConfiguration="addConfiguration"
        @addConfigurationItems="addConfigurationItems"
        @deleteConfigurationGroup="deleteConfigurationGroup"
        @deleteConfigurationItem="deleteConfigurationItem"
        @editConfigurationGroup="editConfigurationGroup"
      />
    </div>
  </div>
</template>

<script>

import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import ArchieveIcon from '@/assets/svg/archived.svg';
import UnarchivedIcon from '@/assets/svg/unarchive24px.svg';
import ProgressBar from '@/components/base/ProgressBar'
import { formattedDate } from '@/utils/util';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import CheckedIcon from '@/assets/svg/checked.svg';
import MilestoneIcon from '@/assets/svg/milestone.svg';
import ApplyConfigIcon from '@/assets/svg/duplicate-apply-config.svg';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js';
import PencilIcon from '@/assets/svg/pencil-16.svg';
import DeleteIcon16px from '@/assets/svg/delete-16.svg';
import DuplicateAndApplyConfigDialog from '@/components/TestRuns/DuplicateAndApplyConfigDialog.vue';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import makeConfigurationService from '@/services/api/configuration'
import { useRelations } from '@/composables/utils/relations';

export default {
  components: {
    DeleteIcon,
    DeleteIcon16px,
    EditIcon,
    ArchieveIcon,
    UnarchivedIcon,
    ProgressBar,
    MilestoneIcon,
    CheckedIcon,
    ApplyConfigIcon,

    PencilIcon,
    DuplicateAndApplyConfigDialog,
  },
  mixins: [colorPreferencesMixin, projectStatus, handleLoading],
  props: {
    filteredHeaders: Array,
    filteredItems: Array,
    itemKey: String,
    rowClass: Function,
    clearSelection: Boolean,
    hasInitiallyLoaded: Boolean,
    selectedRuns: {
      type: [Array, Object],
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    deleteEntity:{
      type: Boolean,
      default: false
    },
    isRunProjectArchived: {
      type: Boolean,
      default: false
    },
    tableFilter: {
      type: String,
      default: 'all'
    },
    configurations: {
      type: Array,
      default: () => [],
    },
    showConfigurations: {
      type: Boolean,
      default: false,
    },
    totalItems: {
      type: Number,
      default: 0
    },
    relationsLoading: {
      type: Boolean,
      default: false
    },
    relationLoadingStates: {
      type: Object,
      default: () => ({})
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    const { formatDate } = useDateFormatter();
    const { processedRunCache } = useRelations();
    return { formatDate, processedRunCache };
  },

  data() {
    return {
      debounce: false,
      selectedItems: [],
      statuses: [],
      priorities: [],
      editedValue: '',
      isEditingTitle: false,
      editedTitle: '',
      editingIndex: null,
      internalConfigurations: null,
      duplicateAndApplyConfigDialog: false,
      configurationsData: [],
      buttonLoading: false,
      buttonLoadingItems: false,
      selectedRun: {},
      globalConfiguration: {},
    };
  },
  computed: {
    selectedRows:{
      get(){
        return this.selectedRuns
      },
      set(value){
        this.selected = value;
        this.$emit('select-item', this.selected)
      }
    },
    // Configurations will be sorted on backend, so just return them directly
    configurationSorted() {
      return this.configurationsData || [];
    },
    routeParamsHandle() {
      return this.$route.params.handle;
    },
    routeParamsKey() {
      return this.$route.params.key;
    },
    shouldHaveOverflow() {
      // Enable overflow (horizontal scroll) if more than 4 columns
      return this.filteredHeaders && this.filteredHeaders.length > 6;
    },
    tableContainerClass() {
      return this.shouldHaveOverflow ? 'table-scroll-container' : 'table-no-scroll-container';
    },
    tableClass() {
      const baseClasses = 'data-table-style table-fixed custom-table';
      return this.shouldHaveOverflow ? `${baseClasses} table-min-width` : baseClasses;
    },
  },
  watch: {
    clearSelection(newVal) {
      if (newVal) {
        this.selectedItems = [];
        this.$emit('select-item', this.selectedItems);
      }
    },
    configurations: {
      handler(val) {
        this.internalConfigurations = [...val];
      },
      immediate: true
    }
  },
  created() {
    this.priorities = this.getPriorities("testRun");
    this.statuses = this.getStatuses("testRun");
  },
  mounted() {
    if(this.showConfigurations) {
      this.getConfigurations();
    }
  },
  methods: {
    formattedDate,
    isSelected(id){
      return this.selectedItems.includes(id);
    },
    onAddConfiguration(item) {
      this.selectedRun = item;
      this.duplicateAndApplyConfigDialog = true;
    },
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 10, 0, 'name', 'asc');
        this.configurationsData = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    async handleDuplicateAndApplyConfig(items){
      let data = {
        configs: items.sets.flat(),
        runUid: this.selectedRun.uid,
        addedConfig: items.addedConfig,
        removedConfig: items.removedConfig,
        addedConfigCount: items.addedConfigCount,
        removedConfigCount: items.removedConfigCount
      }
      this.$emit('handleDuplicateAndApplyConfig', data);
    },
    async updateConfiguration(uid, name, options) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const configurationService = makeConfigurationService(this.$api);
      try {
        await configurationService.updateConfiguration(handle, projectKey, uid, {
          name: name,
          options: options,
        });
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    onCloseDuplicateAndApplyConfigDialog() {
      this.duplicateAndApplyConfigDialog = false;
    },
    async addConfiguration(configuration) {
      this.buttonLoading = true;
      try {
        const configurationService = makeConfigurationService(this.$api);
        await configurationService.createConfigurations(
          this.$route.params.handle,
          this.$route.params.key,
          {
            name: configuration.newConfigurationName,
            options: configuration.options,
          }
        );
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      } finally {
        this.buttonLoading = false;
      }
    },
    async addConfigurationItems(item) {
      this.buttonLoadingItems = true;
      try {
        const formatOptions = () => {
          return item.items.map(opt => {
            if (opt.uid) {
              return { name: opt.name, uid: opt.uid };
            } else {
              return { name: opt };
            }
          });
        };
        await this.updateConfiguration(item.uid, item.name, formatOptions());
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      } finally {
        this.buttonLoadingItems = false;
      }
    },
    async deleteConfigurationGroup(group) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const configurationService = makeConfigurationService(this.$api);
      try {
        await configurationService.deleteConfiguration(handle, projectKey, group.uid);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration group' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    async deleteConfigurationItem(data) {
      try {
        await this.updateConfiguration(data.uid, data.name, data.items);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    
    async editConfigurationGroup(data) {
      try {
        await this.updateConfiguration(data.uid, data.name, data?.items);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    onMilestone(item) {
      this.$emit('open-milestone-dialog', item);
    },
    onPlan(item) {
      this.$emit('open-plan-dialog', item);
    },
    onDuplicateConfig(item) {
      this.$emit('open-duplicate-dialog', {
        items: item,
        type: 'specificDuplicate',
      });
    },
    onChangeDueDate(item) {
      this.$emit('on-change-due-date', item);
    },
    safePercentage(value) {
      const percentage = value;
      return (typeof percentage === 'string' && percentage !== 'NaN') ? percentage : '0';
    },
    handleRowClick(item) {
      this.$emit('select-row', item);
    },
    onEdit(item) {
      this.$emit('edit-item', item);
    },
    onArchive(item) {
      if (!this.isRunProjectArchived) {
      this.$emit('archive-item', item);
      }
    },
    onUnArchive(item) {
      this.$emit('unarchive-item', item);
    },
    onDelete(item) {
      this.$emit('delete-item', item);
    },
    toggleStar(item) {
      this.debounce = true;
      this.$emit('toggle-star', item);
    },
    editItem(item) {
      this.$emit('editConfigItem', {
        uid: this.uid,
        name: this.title,
        item: item,
      });
    },
    deleteItem(item, index) {
      const unSelectedConfigs = item.customFields.configs.filter((_, i) => i !== index);
      const data = {
        newConfigs: unSelectedConfigs,
        runUid: item.uid,
      }
      this.$emit('deleteConfigItem', data);
    },
    startEditItem(item, index) {
      this.editingIndex = index;
      this.editedValue = item.configs && item.configs[index] ? item.configs[index].option : ''
      this.$nextTick(() => {
        if (this.$refs?.itemInput) {
          this.$refs?.itemInput?.$el?.focus();
        }
      });
    },
    saveItem(oldItem, index) {
      if (this.editedValue) {
        const config = oldItem?.customFields?.configs[index];
        const [configUid, optionUid] = config.split("::");
        this.$emit('editConfigItem', {
          configUid: configUid,
          optionUid: optionUid,
          newItemName: this.editedValue
        });
      }
      this.editingIndex = null;
      this.editedValue = '';
    },
    onUpdatePagination(options) {
      this.$emit('update-pagination', options);
    },
    updateTableOptions(options) {
      // Handle v-data-table sorting events and forward to parent
      const itemsPerPage = options.itemsPerPage === -1 ? 10000 : options.itemsPerPage;
      this.$emit('update-pagination', { ...options, itemsPerPage });
    },
  },
};
</script>
<style scoped>
  .metaball-icon {
    z-index: 10;
  }
  .v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
    background-color: #ffffff;
  }
  .v-data-table-header__icon {
    opacity: 1 !important;
  }
  .v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
    background-color: #F9FAFB;
  }
  .pointer {
    cursor: pointer;
  }
  .header_text {
    color: #475467;
    font-weight: 700;
  }
  .custom_color {
    color: #667085;
  }
  .custom_border {
    border: 2px solid #ffffff;
  }
  .v-data-table table {
    border-collapse: collapse;
  }
  .v-data-table th {
    border: none !important;
  }
  .v-data-table td {
    border: none !important;
    cursor: pointer;
  }
  .v-data-table .v-data-table__wrapper tbody tr {
    height: 80px;
  }
  .v-data-table .v-data-table__wrapper tbody tr td {
    height: 80px !important;
    padding-top: 0;
  }
  .v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
    background-color: transparent !important;
  }
  .item-block {
    display: block !important;
  }
</style>
