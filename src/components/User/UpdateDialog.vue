<template>
  <div>
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      @click:outside="clickOutside"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6 mb-4">
            <h2
              v-if="!loaderState"
              class="black--text"
            >
              {{ $t('edit') }}
            </h2>
            <v-skeleton-loader
              v-else
              class="mr-3"
              height="24"
              width="75"
              type="text"
            />
            <v-btn
              v-if="!loaderState"
              icon
              @click="$emit('closeDialog')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
            <v-skeleton-loader
              v-else
              height="36"
              width="36"
              rounded
              type="avatar"
            />
          </div>
          <div>
            <div>
              <div class="text-start">
                <v-label
                  v-if="!loaderState"
                  class="text-theme-label font-weight-medium"
                >
                  {{ projectScope ? $t('role') : $t('defaultRole') }}  <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-skeleton-loader
                  v-else
                  class="mr-3"
                  height="24"
                  width="120"
                  type="text"
                />
              </div>
              <v-autocomplete
                v-if="!loaderState"
                v-model="selectedRole"
                :disabled="projectScope && filter === 'pending'"
                :items="roles"
                :rules="roleRules"
                item-text="name"
                item-value="uid"
                :background-color="'#F9F9FB'"
                :placeholder="$t('chooseRole')"
                append-icon="mdi-chevron-down"
                class="rounded-lg field-theme"
                height="38px"
                required
                dense
                :menu-props="{ offsetY: true }"
                @change="defaultRoleSelected"
                @update:search-input="allowSearch && searchRoles('default', $event)"
              />
              <v-skeleton-loader
                v-else
                class="mt-4"
                height="46"
                width="100%"
                type="button"
              />
            </div>
            <div
              v-if="assignedProjects.length"
              style="background-color: #F2F4F7;"
              class="px-2 py-3 rounded-lg mt-2"
            >
              <div
                v-for="(project, index) in assignedProjects"
                :key="index"
                class="project-container"
              >
                <div
                  class="roleProject d-flex justify-space-between align-center"
                  style="gap: 12px;"
                >
                  <div class="text-start align-self-start">
                    <v-label
                      class="text-theme-label font-weight-medium fs-14px"
                    >
                      {{ $t('project') }}
                    </v-label>
                    <v-select
                      v-model="project.projectUid"
                      :items="projects"
                      :disabled="project.loading || (projectScope)"
                      item-value="uid"
                      :placeholder="$t('chooseProject')"
                      append-icon="mdi-chevron-down"
                      class="rounded-lg field-theme"
                      height="38px"
                      :background-color="'#F9F9FB'"
                      required
                      dense
                      :menu-props="{ offsetY: true }"
                      @change="selectProject(project.projectUid, index)"
                    >
                      <template #selection="{ item }">
                        <span>{{ truncateText(item?.name, 12) }}</span>
                      </template>
                      <template #item="{ item, on, attrs }">
                        <v-list-item
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item-title>{{ item.name }}</v-list-item-title>
                        </v-list-item>
                      </template>
                    </v-select>
                  </div>
                  <div class="text-start">
                    <v-label
                      class="text-theme-label font-weight-medium fs-14px"
                    >
                      {{ $t('projectRole') }}
                    </v-label>
                    <v-autocomplete
                      v-model="project.roleUid"
                      :items="project.roles"
                      :disabled="project.loading"
                      item-value="uid"
                      :placeholder="project.loading ? `${$t('loading')}...` : $t('chooseRole')"
                      append-icon="mdi-chevron-down"
                      class="rounded-lg field-theme"
                      height="38px"
                      :background-color="'#F9F9FB'"
                      required
                      dense
                      :menu-props="{ offsetY: true }"
                      @change="selectRole(project.roleUid, index)"
                      @update:search-input="allowSearch && searchRoles(index, $event)"
                    >
                      <template #selection="{ item }">
                        <span>{{ truncateText(item?.name, 10) }}</span>
                      </template>
                      <template #item="{ item, on, attrs }">
                        <v-list-item
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item-title>{{ item.name }}</v-list-item-title>
                        </v-list-item>
                      </template>
                    </v-autocomplete>
                    <span
                      v-if="assignedProjects[index].roleIncludeOrgPermissions"
                      class="red--text mb-3"
                      :style="{
                        overflow: 'hidden',
                        display: '-webkit-box',
                        '-webkit-line-clamp': 2,
                        '-webkit-box-orient': 'vertical',
                        'font-size': '12px'
                      }"
                    >{{ $t('roleHasOrgPermissions') }}</span>
                  </div>
                  <div
                    v-if="!projectScope && filter !== 'pending'"
                    class="delete-action text-end"
                  >
                    <v-btn
                      height="30"
                      color="danger"
                      class="text-capitalize rounded-lg mr-3 white--text btn-theme"
                      depressed
                      :disabled="project.loading"
                      icon
                      @click="deleteProject(index)"
                    >
                      <DeleteIcon />
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="my-4 text-end"
            >
              <template v-if="!projectScope">
                <v-tooltip
                  top
                  left
                  max-width="485px"
                  content-class="tooltip-theme"
                  :disabled="!reachedMaxProjectsLimit"
                >
                  <template #activator="{on, attrs}">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <template v-if="!loaderState">
                        <v-btn
                          v-if="!projectScope && !isDefaultOwnerRole"
                          width="150"
                          color="#F2F4F7"
                          height="40"
                          :depressed="true"
                          class="text-capitalize btn-theme"
                          elevation="0"
                          :disabled="reachedMaxProjectsLimit"
                          @click="addProject"
                        >
                          <v-icon>mdi-plus</v-icon>
                          {{ $t('addProject') }}
                        </v-btn>
                      </template>
                      <v-skeleton-loader
                        v-else
                        class="mt-2 ml-auto"
                        height="40"
                        width="150"
                        type="button"
                      />
                    </div>
                  </template>
                  <span>
                    {{ $t('noProjectLeftToAssign') }}
                  </span>
                </v-tooltip>
              </template>
            </div>
            <div class="select-title mt-4 mb-1">
              <v-label
                v-if="!loaderState"
                class="text-left fs-14px text-theme-label font-weight-medium"
              >
                {{ $t('tags') }}
              </v-label>
              <v-skeleton-loader
                v-else
                class="mr-3"
                height="24"
                width="120"
                type="text"
              />
            </div>
            <TagSelector 
              v-if="!loaderState"
              v-model="tagsData.selectedTags"
              :items="tags"
              class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
            />
            <v-skeleton-loader
              v-else
              class="mt-4"
              height="46"
              width="100%"
              type="button"
            />
            <section class="d-flex flex-column w-full">
              <div class="d-flex w-full justify-space-between align-center mt-4">
                <p
                  v-if="!loaderState"
                  class="font-weight-medium ma-0"
                >
                  {{ $t('replaceExistingTags') }}
                </p>
                <v-skeleton-loader
                  v-else
                  class="mt-4"
                  height="24"
                  width="140"
                  type="text"
                />
                <v-switch
                  v-if="!loaderState"
                  v-model="showReplaceTag"
                  inset
                  color="primary"
                />
                <v-skeleton-loader
                  v-else
                  class="mt-4"
                  height="24"
                  width="48"
                  type="button"
                />
              </div>

              <section
                v-if="showReplaceTag"
                class="d-flex w-full flex-column"
              >
                <div
                  class="d-flex flex-column"
                >
                  <div class="select-title mt-4 mb-1">
                    <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                      {{ $t('replaceTag') }}
                    </v-label>
                  </div>
                  <TagSelector
                    v-model="tagsData.replaceTag"
                    :items="tags"
                    class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
                  />
                </div>
                <div
                  class="d-flex flex-column"
                >
                  <div class="select-title mb-1">
                    <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                      {{ $t('with') }}
                    </v-label>
                  </div>
                  <TagSelector
                    v-model="tagsData.withTag"
                    :items="tags"
                    class="pt-1 pa-0 mt-0 rounded-lg field-theme custom-prepend"
                  />
                </div>
              </section>
            </section>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          v-if="!loaderState"
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          depressed
          class="text-capitalize btn-theme"
          elevation="0"
          @click="showDialog = false"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-skeleton-loader
          height="40px"
          width="204.5px"
          type="button"
        />
        <v-btn
          v-if="!loaderState"
          width="204.5px"
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="updateUser"
        >
          {{ $t('save') }} 
        </v-btn>
        <v-skeleton-loader
          v-else
          class="rounded-lg primary"
          height="40px "
          width="204.5px"
          type="button"
        />
      </div>
    </v-dialog>
  </div>
</template>

<script>
import TagSelector from '@/components/base/TagSelector.vue';
import {  showErrorToast } from '@/utils/toast';
import ProjectsService from '@/services/api/project';
import DeleteIcon from '@/assets/svg/delete.svg';
import makeRoleService from '@/services/api/role';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('user');
import { debounce } from 'debounce';
let makeProjectService


export default {
  name: 'EditDialog',
  components: {
    TagSelector,
    DeleteIcon
  },
  props: {
    isOpen: Boolean,
    projects: {
      type: Array,
      default: () => [],
    },
    tags: {
      type: Array,
      default: () => [],
    },
    selectedUser: {
      type: Object,
      default: null,
    },
    value: {
      type: Boolean,
      default: false,
    },
    filter: {
      type: String
    }
  },
  data() {
    return {
      tagsData: {
        selectedTags: [],
        replaceTag: [],
        withTag: [],
      },
      showReplaceTag: false,
      selectedRole: "",
      assignedProjects: [],
      allowSearch: false,
      loaderState: false,
      roles: [],
      roleRules: [
        v => !!v || this.$t('roleRequired'),
      ],
    };
  },
  computed:{
    ...mapState(['currentAccount']),
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
    projectScope(){
      return !!this.$route.params.key;
    },
    isDefaultOwnerRole() {
      if (typeof this.selectedRole === 'object' && this.selectedRole.slug === 'owner') {
        return true;
      }

      if (
        typeof this.selectedRole === 'string' &&
        this.roles?.some(role => role.uid === this.selectedRole && role.slug === 'owner')
      ) {
        return true;
      }

      return false;
    },
    reachedMaxProjectsLimit() {
      return this.assignedProjects.length >= this.projects.length;
    },
  },
  async created(){
    this.loaderState = true;
    makeProjectService = ProjectsService(this.$api);
    const delayLoader = setTimeout(() => {
        this.loaderState = true;
    },500);
    clearTimeout(delayLoader);
    await this.initializeForm();
    await new Promise((resolve) => setTimeout(() => {
        this.loaderState = false;
      resolve()
    }, 500))
    this.loaderState = false;
    await new Promise(resolve => setTimeout(() => {
      this.allowSearch = true;
      resolve();
    }, 1000)); 
  },
  
  methods: {
    clickOutside() {
      this.showDialog = false;
    },
    async initializeForm() {
      if (this.selectedUser) {
        this.roles = await this.getRoles(this.currentAccount.handle);
        const selectedUserRole = this.roles.find(role => role.uid === this.selectedUser.role?.uid || role.uid === this.selectedUser.roleUid);
        this.selectedRole = selectedUserRole; 
        this.selectedProject = this.selectedUser.project;
        this.tagsData.selectedTags = this.selectedUser.tags || [];

        if(this.selectedUser.overriddenRoles?.length){
          this.assignedProjects = this.selectedUser.overriddenRoles.flatMap(role => 
            role.projectUids.map(projectUid => ({
              projectUid: projectUid,
              roleUid: role.roleId,
              roles: [],
              loading: false
            }))
          );

          for (const [index, project] of this.assignedProjects.entries()) {
            this.assignedProjects[index].loading = true;
            try {
              const roles = await this.getRoles(this.currentAccount.handle, 0, {includeProjects: [project.projectUid]});
              this.assignedProjects[index].roles = roles;
            } catch (error) {
              showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, error?.response?.data);
            } finally {
              this.assignedProjects[index].loading = false;
            }

          }
        }
      }
    },
    defaultRoleSelected(){
      if(this.isDefaultOwnerRole && !this.projectScope)
        this.assignedProjects = []
    },
    async getRoles(handle, offset = 0, prm) {
      const roleService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      try {
        const params = {
          includePermissions: true,
          ...(this.projectScope ? {includeOrgRoles: true} : {}),
          offset,
          limit: 250,
          ...(prm ? prm : {})
        }
        const response = await roleService.getRoles(handle, projectKey, params);
        return response.data?.items || [];

      } catch (err) {
        this.redirectOnError(err.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, err?.response?.data);
      }
    },
    updateUser(){
      const obj = {
        selectedRole: this.selectedRole,
        assignedProjects: this.assignedProjects,
        tagsData: this.tagsData,
        currentRole: this.selectedUser.role.name,
        assignedRole: this.roles?.find(role => role.uid === this.selectedRole.uid).slug
      }
      this.$emit('clickSave', obj)
    },
    searchRoles: debounce(async function (scope, q) {
      if (this.loaderState) return;

      const projectUid = scope !== 'default' ? this.assignedProjects[scope].projectUid : null;
      const newRoles = await this.getRoles(this.currentAccount.handle, 0, {
        q,
        ...(projectUid ? { includeProjects: [projectUid] } : {})
      });

      if(scope == 'default')
       this.roles = newRoles;
      else
        this.assignedProjects[scope].roles = newRoles
      
    }, 500), 
    selectRole(roleUid, index){
      if(!roleUid ||!Number.isInteger(index)) return;
        
      const orgPermissions = [
        'write_project', 
        'delete_project', 
        'write_tag', 
        'write_integration', 
        'delete_integration', 
        'write_setting', 
        'write_key', 
        'delete_key', 
        'full_billing', 
        'limited_billing'
      ];
      const findIndex = this.assignedProjects[index].roles.findIndex(role => role.uid === roleUid);
      const role = this.assignedProjects[index]?.roles[findIndex].permissions || [];
      const orgPermissionExists = this.assignedProjects[index]?.roles[findIndex].slug === 'owner' || orgPermissions.some(permission => role.includes(permission));
      this.assignedProjects[index].roleIncludeOrgPermissions = orgPermissionExists
    },
    async selectProject(projectUid, index){
      const projectExists = this.assignedProjects.filter(project => project.projectUid === projectUid);

      if(projectExists.length > 1){
        this.$nextTick(() => {
          this.assignedProjects[index].projectUid = null;
          this.assignedProjects[index].roleUid = null;
        })
        return showErrorToast(this.$swal, this.$t("projectAlreadyAdded"));
      }

      this.assignedProjects[index].loading = true;
      const roles = await this.getRoles(this.currentAccount.handle, 0, {includeProjects: [projectUid]});
      this.assignedProjects[index].roles = roles || [];
      this.assignedProjects[index].loading = false;
    },
    addProject(){
      if(this.assignedProjects.length == this.projects.length){
        return showErrorToast(this.$swal, this.$t("maxProjectsLimitReached"));
      }

      this.assignedProjects.push({
        projectUid: null,
        roleUid: null,
        roles: [],
        loading: false
      });
    },
    deleteProject(index){
      this.assignedProjects.splice(index, 1);
    },
    async getProjects(additionalParams = {}, handle) {
      if (!handle) handle = this.currentAccount.handle;
      const searchParams = new URLSearchParams();
      searchParams.set('status', this.filter);
      searchParams.set('includeCount', true);
    
      Object.keys(additionalParams).forEach(key => {
        searchParams.set(key, additionalParams[key]);
      });

      const response = await makeProjectService.getProjects(handle, searchParams.toString());
      this.projects = response.data.items;

    },
    resetForm() {
      this.tagsData = {
        selectedTags: [],
        replaceTag: [],
        withTag: [],
      };
      this.assignedProjects = [];
      this.showReplaceTag = false;
      this.selectedRole = "";
      this.selectedProject = "";
    },
    truncateText(name, maxLength) {   
      return name?.length > maxLength ? name.substring(0, maxLength) + '...' : name;
    }
  },
};
</script>

<style scoped>
.select-title{
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
}
input {
  height: 38px !important;
}
.custom-prepend{
  padding: 0px !important;
}
</style>
