<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table data-table-style mt-6"
      :headers="filteredHeaders"
      :items="items"
      :item-key="itemKey"
      hide-default-footer
      disable-pagination
    >
      <template #[`header.name`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.requestedBy`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.role`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.createdAt`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.tag`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.email`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.lastActivity`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.project`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`header.status`]="{ header }">
        <span class="header_text">
          {{ header.text }}
        </span>
      </template>
      <template #[`item.role`]="{ item }">
        <td class="text-start">
          <v-menu
            offset-y
            :position-y="10"
          >
            <template #activator="{ on }">
              <div
                role="button"
                v-on="writeMember && filter == 'active' ? on : false"
              >
                <span :class="{ 'grey--text': !item?.role?.name }">
                  {{ filter == 'pending' ? item.overriddenRoles[0].name : item?.role?.name ? item.role.name : $t('noRoleAssigned') }}
                </span>
                <v-icon
                  v-if="writeMember && filter == 'active'"
                  class="ml-3"
                >
                  mdi-chevron-down
                </v-icon>
              </div>
            </template>
            <v-list class="pt-4">
              <v-list-item
                v-for="(role, index) in roles"
                :key="index"
                class="m-0"
                :class="role.uid === item.role.uid ? 'v-list-item--active' : ''"
                :value="role.uid"
                @click="$emit('update-role', { uid: role.uid, name: role.name, currentRole: item.role.uid, currentRoleName: item.role.name}, item.uid)"
              >
                <div class="d-flex align-start">
                  <div class="d-flex align-center mt-2">
                    <span class="mr-2">{{ role.name }}</span>
                  </div>
                </div>
              </v-list-item>
            </v-list>
          </v-menu>
        </td>
      </template>
      <template #[`item.createdAt`]="{ item }">
        <span class="gray-ish--text text-subtitle-1">
          {{ item.createdAt ? timeAgo(item.createdAt) : '-' }}
        </span>
      </template>
      <template #[`item.tag`]="{ item }">
        <div v-if="isTagLoading">
          <v-skeleton-loader
            type="text"
            height="16"
            class="w-100"
          />
        </div>
        <UserTagEditor
          v-else
          :key="`${tags}-${tags.map((t) => t.uid).join('-')}`"
          :is-project-archived="false"
          :items="tags"
          :value="item.tags"
          @updateTag="updateTag($event, item)"
          @refreshTags="fetchTags"
        />
      </template>
      <template #[`item.email`]="{ item }">
        <span class="gray-ish--text text-subtitle-1">
          {{ item?.email }}
        </span>
      </template>
      <template #[`item.lastActivity`]="{ item }">
        <span class="gray-ish--text text-subtitle-1">
          {{ item.lastActivity ? timeAgo(item.lastActivity) : '-' }}
        </span>
      </template>
      <template #[`item.status`]="{ item }">
        <v-tooltip
          v-if="item.status === 'invalid_email'"
          bottom
          left
          max-width="485px"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <span
              class="text-body-1 font-weight-medium text-capitalize"
              :style="{ color: getStatusColor(item.status || '') }"
              v-bind="attrs"
              v-on="on"
            >
              {{ formatStatus(item.status) }}
            </span>
          </template>
          <span>{{ $t('emailUndeliverable') }}</span>
        </v-tooltip>
        <span
          v-else
          class="text-body-1 font-weight-medium text-capitalize"
          :style="{ color: getStatusColor(item.status || '') }"
        >
          {{ formatStatus(item.status) }}
        </span>
      </template>
      <template #[`item.project`]="{ item }">
        <span class="gray-ish--text text-subtitle-1">
          <template v-if="item.role?.projects?.length">
            <div class="avatar-group d-flex justify-start">
              <v-tooltip
                v-for="(project, index) in item.role?.projects?.slice(0, 5)"
                :key="index"
                bottom
              >
                <template #activator="{ on, attrs }">
                  <Avatar
                    v-bind="attrs"
                    :avatar="{ user: project.avatarUrl || require('@/assets/png/project.png') }"
                    :size="36"
                    class="mr-n2"
                    v-on="on"
                  />
                </template>
                <span>{{ project.name }}</span>
              </v-tooltip>
              <Avatar
                v-if="item.projects.length > 5"
                :name="`+${item.projects.length - 5}`"
                :size="36"
                class="mr-n2 img-rest-amount"
              />
            </div>
          </template>
        </span>
      </template>
      <template #[`item.name`]="{ item }">
        <td class="d-flex align-center">
          <div class="custom-div mr-2" />
          <Avatar
            class="mr-2"
            :avatar="item.avatar"
          />
          <div class="text-start">
            <div class="font-weight-bold">
              {{ item?.firstName }} {{ item?.lastName }}
            </div>
          </div>
        </td>
      </template>

      <template #[`item.deleteIcon`]="{ item }">
        <td class="d-flex align-center">
          <v-tooltip bottom>
            <template #activator="{ on, attrs }">
              <v-btn
                v-if="filter == 'pending'"
                icon
                v-bind="attrs"
                v-on="on"
                @click="$emit('resend-invite', item)"
              >
                <ResendIcon style="color: #000" />
              </v-btn>
            </template>
            <span>{{ $t('resendInvite') }}</span>
          </v-tooltip>
          <v-btn
            v-if="filter !== 'expired'"
            icon
            @click="$emit('edit-item', item)"
          >
            <EditIcon style="color: #000" />
          </v-btn>
          <template v-if="!projectScope || (projectScope && filter !== 'active')">
            <v-tooltip
              bottom
              :disabled="deleteMember"
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-btn
                    :disabled="!deleteMember"
                    icon
                    @click="$emit('delete-item', item)"
                  >
                    <CancelIcon />
                  </v-btn>
                </div>
              </template>
              <span>
                {{ $t('inviteUser.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
              </span>
            </v-tooltip>
          </template>
        </td>
      </template>
    </v-data-table>
    <template v-else>
      <UserTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import EditIcon from '@/assets/svg/edit.svg';
import ResendIcon from '@/assets/svg/resend.svg';
import CancelIcon from '@/assets/svg/cancel.svg';
import { formattedDate, timeAgo } from '@/utils/util';
import handleLoading from '@/mixins/loader.js';
import UserTableSkeleton from '@/components/Skeletons/User/UserTableSkeleton.vue';
import Avatar from '@/components/base/Avatar.vue';
import UserTagEditor from '@/components/base/UserTagEditor.vue';

export default {
  components: {
    EditIcon,
    ResendIcon,
    CancelIcon,
    UserTableSkeleton,
    Avatar,
    UserTagEditor,
  },
  mixins: [handleLoading],
  props: {
    filteredHeaders: Array,
    itemKey: String,
    items: Array,
    roles: Array,
    tags: Array,
    isTagLoading: Boolean,
    writeMember: {
      type: Boolean,
      default: false,
    },
    deleteMember: {
      type: Boolean,
      default: false,
    },
    filter: String,
  },
  data() {
    return {
      showCreateTagDialog: false,
      selectedTag: null,
      createTag: null,
    };
  },
  computed: {
    projectScope() {
      return !!this.$route.params.key;
    },
  },
  methods: {
    formattedDate,
    timeAgo,
    getStatusColor(status) {
      const statusColors = {
        pending: '#42A5F5',
        canceled: '#667085',
        expired: '#FFA726',
        invalid_email: '#EF5350',
        delivered: '#66BB6A',
      };
      return statusColors[status.toLowerCase()] || '#000';
    },
    formatStatus(status) {
      if (!status) return '';
      return status.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
    },
    fetchTags() {
      this.$emit('refreshTags');
    },
    updateTag(event, item) {
      const newTagUids = event.map((e) => e.uid);
      const existingTagUids = item.tags.map((t) => t.uid).filter((e) => !newTagUids.includes(e));
      const payload = {
        tagReplacements: {
          newTagUids: newTagUids?.length ? newTagUids : undefined,
          existingTagUids: existingTagUids?.length ? existingTagUids : undefined,
        },
        userUids: [item.uid],
      };
      this.$emit('update-tag', payload);
    },
  },
};
</script>
