steps:
  - name: 'node:18'
    entrypoint: bash
    args:
     - -c
     - |
       yarn install
       yarn build:staging

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    args:
     - gsutil
     - cp
     - dist/$TAG_NAME/index.html
     - gs://staging.app.testfiesta.com/$TAG_NAME/index.html
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  # Fancy footwork here to remove the nested directory required for our build
  #   to include the version numbmer in asset paths.
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    args:
     - gsutil
     - -m
     - rsync
     - -Ri
     - dist/$TAG_NAME/$TAG_NAME/assets
     - gs://staging.app.testfiesta.com/$TAG_NAME/assets
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP="$(curl icanhazip.com)/32"
       echo "$$EXTERNAL_IP" | tee /workspace/IP.txt
       EXTERNAL_CIDRS="$(gcloud container clusters describe staging-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g'),$$EXTERNAL_IP"
       gcloud container clusters update staging-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'container'
      - 'clusters'
      - 'get-credentials'
      - 'staging-cluster'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/jobs/system'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'


  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       STATUS="0" && kubectl wait --timeout=2m --for=condition=complete -n staging job/app-version-insert-frontend-v0-116-2 || STATUS="$$?"
       if [ "$$STATUS" -ne "0" ]; then curl -vvv -XPOST -H 'Content-Type:application/json' --data "{\"content\":\"Version adding failed for Staging Frontend [v0-116-2]\"}" https://discord.com/api/webhooks/1381490175586074684/3YRKtiRiKXZqSFEKXkwDV4SBSGwu2TmZLCuTFH_SUejleX3X5zv1n4PO41vZMRUAvHDI; fi
       kubectl create cm --dry-run=client -n staging frontend-nginx-cm --from-file=./deploy/base/deployment/default.conf -o yaml | kubectl apply -f -
       sleep 30
       for pod in $(kubectl get pods -n staging -o name --no-headers=true | grep frontend-nginx); do echo "Reloading $pod"; kubectl exec -it -n staging $pod -- bash -c 'nginx -s reload'; done
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args: 
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/jobs/user'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP=$(cat /workspace/IP.txt)
       EXTERNAL_CIDRS=$(gcloud container clusters describe staging-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g' | sed "s~,$$EXTERNAL_IP~~g")
       gcloud container clusters update staging-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

options:
  logging: CLOUD_LOGGING_ONLY
