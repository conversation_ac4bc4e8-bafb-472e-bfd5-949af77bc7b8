map $arg_fv $qsVersion {
    "~^(?P<qfv>v[0-9]+\.[0-9]+\.[0-9])$"   $qfv;
    default                                "v0.116.2";
}

map $cookie_fv $cVersion {
    "~^(?P<cfv>v[0-9]+\.[0-9]+\.[0-9])$"   $cfv;
    default                                "v0.116.2";
}

server {
    listen       80;
    listen  [::]:80;
    server_name   _;

    #access_log  /var/log/nginx/host.access.log  main;

    location ~* /(?P<version>v[0-9]+\.[0-9]+\.[0-9])/assets/(?P<basename>[a-z0-9\.\-_]+\.(js|css|html|png|svg|jpe?g|gif|ico))$ {
        resolver kube-dns.kube-system.svc.cluster.local;
        proxy_pass https://storage.googleapis.com/staging.app.testfiesta.com/$version/assets/$basename;
    }

    location / {
        index /index.html;
        set $selectedVersion $cVersion;
        if ($arg_fv ~* "^v[0-9]+\.[0-9]+\.[0-9]$" ) {
          set $selectedVersion $qsVersion;
        }
        resolver kube-dns.kube-system.svc.cluster.local;
        proxy_pass https://storage.googleapis.com/staging.app.testfiesta.com/$selectedVersion/index.html;
    }

    location /healthz {
	add_header Content-Type application/json;
        return 200 '{ "version": "v0.116.2" }';
    }

    location /version {
        add_header Content-Type application/json;
        return 200 '{ "version": "v0.116.2" }';
    }
}

